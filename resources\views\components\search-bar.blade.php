@props([
    'placeholder' => 'Search courses, events, members...',
    'showQuickResults' => true,
    'size' => 'default' // default, large, small
])

<div class="search-bar-component relative" x-data="searchBar()" @click.away="closeResults()">
    <!-- Search Input -->
    <div class="relative">
        <input type="text"
               x-model="query"
               @input.debounce.300ms="search()"
               @focus="showResults = true"
               @keydown.escape="closeResults()"
               @keydown.arrow-down.prevent="navigateDown()"
               @keydown.arrow-up.prevent="navigateUp()"
               @keydown.enter.prevent="selectResult()"
               placeholder="{{ $placeholder }}"
               class="search-input {{ $size === 'large' ? 'text-lg py-4 px-6' : ($size === 'small' ? 'text-sm py-2 px-4' : 'py-3 px-4') }} w-full bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent pl-12">
        
        <!-- Search Icon -->
        <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
        </div>
        
        <!-- Loading Spinner -->
        <div x-show="loading" class="absolute right-4 top-1/2 transform -translate-y-1/2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500"></div>
        </div>
        
        <!-- Clear Button -->
        <button x-show="query.length > 0 && !loading" 
                @click="clearSearch()"
                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    @if($showQuickResults)
    <!-- Quick Results Dropdown -->
    <div x-show="showResults && (suggestions.length > 0 || query.length > 0)"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         class="absolute top-full left-0 right-0 mt-2 bg-gray-800 rounded-lg shadow-lg border border-gray-700 z-50 max-h-96 overflow-y-auto">
        
        <!-- Quick Suggestions -->
        <template x-if="suggestions.length > 0">
            <div class="py-2">
                <div class="px-4 py-2 text-xs font-medium text-gray-400 uppercase tracking-wider border-b border-gray-700">
                    Quick Results
                </div>
                <template x-for="(suggestion, index) in suggestions" :key="suggestion.url">
                    <a :href="suggestion.url"
                       @mouseenter="selectedIndex = index"
                       :class="{ 'bg-gray-700': selectedIndex === index }"
                       class="flex items-center px-4 py-3 hover:bg-gray-700 transition-colors cursor-pointer border-b border-gray-700 last:border-b-0">
                        <span class="text-lg mr-3" x-text="suggestion.icon"></span>
                        <div class="flex-1">
                            <div class="font-medium text-sm" x-text="suggestion.title"></div>
                            <div class="text-xs text-gray-400 capitalize" x-text="suggestion.type"></div>
                        </div>
                    </a>
                </template>
            </div>
        </template>
        
        <!-- No Results -->
        <template x-if="query.length >= 2 && suggestions.length === 0 && !loading">
            <div class="px-4 py-6 text-center text-gray-400">
                <svg class="w-8 h-8 mx-auto mb-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <p class="text-sm">No results found</p>
            </div>
        </template>
        
        <!-- View All Results -->
        <template x-if="query.length >= 2">
            <div class="border-t border-gray-700">
                <a :href="`{{ route('search') }}?q=${encodeURIComponent(query)}`"
                   class="flex items-center justify-center px-4 py-3 text-primary-400 hover:text-primary-300 hover:bg-gray-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <span class="text-sm font-medium">View all results</span>
                </a>
            </div>
        </template>
    </div>
    @endif
</div>

<script>
function searchBar() {
    return {
        query: '',
        suggestions: [],
        showResults: false,
        loading: false,
        selectedIndex: -1,

        async search() {
            if (this.query.length < 2) {
                this.suggestions = [];
                this.showResults = false;
                return;
            }

            this.loading = true;
            this.showResults = true;

            try {
                const response = await fetch(`{{ route('search.quick') }}?q=${encodeURIComponent(this.query)}`);
                const data = await response.json();
                
                if (data.success) {
                    this.suggestions = data.suggestions;
                    this.selectedIndex = -1;
                }
            } catch (error) {
                console.error('Search error:', error);
                this.suggestions = [];
            } finally {
                this.loading = false;
            }
        },

        clearSearch() {
            this.query = '';
            this.suggestions = [];
            this.showResults = false;
            this.selectedIndex = -1;
        },

        closeResults() {
            this.showResults = false;
            this.selectedIndex = -1;
        },

        navigateDown() {
            if (this.suggestions.length === 0) return;
            this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);
        },

        navigateUp() {
            if (this.suggestions.length === 0) return;
            this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        },

        selectResult() {
            if (this.selectedIndex >= 0 && this.suggestions[this.selectedIndex]) {
                window.location.href = this.suggestions[this.selectedIndex].url;
            } else if (this.query.length >= 2) {
                window.location.href = `{{ route('search') }}?q=${encodeURIComponent(this.query)}`;
            }
        }
    }
}
</script>

<style>
.search-input:focus {
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.search-bar-component .suggestion-item:hover {
    background-color: rgba(55, 65, 81, 0.8);
}
</style>
