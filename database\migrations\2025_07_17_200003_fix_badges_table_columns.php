<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('badges', function (Blueprint $table) {
            // Add missing badge fields
            if (!Schema::hasColumn('badges', 'slug')) {
                $table->string('slug')->unique()->after('name');
                $table->index('slug');
            }
            
            if (!Schema::hasColumn('badges', 'icon')) {
                $table->string('icon')->default('🏆')->after('description');
            }
            
            if (!Schema::hasColumn('badges', 'color')) {
                $table->string('color')->default('#3B82F6')->after('icon');
            }
            
            if (!Schema::hasColumn('badges', 'type')) {
                $table->string('type')->default('achievement')->after('color');
                $table->index('type');
            }
            
            if (!Schema::hasColumn('badges', 'xp_reward')) {
                $table->integer('xp_reward')->default(0)->after('type');
                $table->index('xp_reward');
            }
            
            if (!Schema::hasColumn('badges', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('xp_reward');
                $table->index('is_active');
            }
            
            if (!Schema::hasColumn('badges', 'requirements')) {
                $table->json('requirements')->nullable()->after('is_active');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('badges', function (Blueprint $table) {
            $table->dropColumn([
                'slug',
                'icon',
                'color',
                'type',
                'xp_reward',
                'is_active',
                'requirements'
            ]);
        });
    }
};
