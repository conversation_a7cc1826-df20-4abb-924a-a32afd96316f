<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CourseEnrollment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'progress_percentage',
        'enrolled_at',
        'completed_at',
        'last_accessed_at',
    ];

    protected $casts = [
        'enrolled_at' => 'datetime',
        'completed_at' => 'datetime',
        'last_accessed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the enrollment
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course that is enrolled
     */
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Check if the course is completed
     */
    public function isCompleted(): bool
    {
        return $this->progress_percentage >= 100;
    }

    /**
     * Mark the course as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'progress_percentage' => 100,
            'completed_at' => now(),
        ]);
    }

    /**
     * Update progress percentage
     */
    public function updateProgress(int $percentage): void
    {
        $this->update([
            'progress_percentage' => min(100, max(0, $percentage)),
            'last_accessed_at' => now(),
        ]);

        if ($percentage >= 100 && !$this->completed_at) {
            $this->markAsCompleted();
        }
    }

    /**
     * Get time spent on course
     */
    public function getTimeSpentAttribute(): string
    {
        if (!$this->last_accessed_at || !$this->enrolled_at) {
            return '0 minutes';
        }

        $minutes = $this->enrolled_at->diffInMinutes($this->last_accessed_at);

        if ($minutes < 60) {
            return $minutes . ' minutes';
        }

        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;

        return $hours . 'h ' . $remainingMinutes . 'm';
    }
}
