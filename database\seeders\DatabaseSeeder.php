<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolesAndPermissionsSeeder::class,
            CampusSeeder::class,
            CourseSeeder::class,
            TaskSeeder::class,
            EventSeeder::class,
            ChatRoomSeeder::class,
            TestimonialSeeder::class,
            UserSubscriptionSeeder::class,
        ]);

        // Create admin user
        $admin = \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'xp' => 1000,
                'level' => 10,
                'is_active' => true,
            ]
        );
        $admin->assignRole('admin');

        // Create test users
        $champion = \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Champion User',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'subscription_plan' => 'champions',
                'xp' => 500,
                'level' => 5,
                'is_active' => true,
            ]
        );
        $champion->assignRole('champion');

        $user = \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'email_verified_at' => now(),
                'password' => bcrypt('password'),
                'xp' => 100,
                'level' => 2,
                'is_active' => true,
            ]
        );
        $user->assignRole('user');
    }
}
