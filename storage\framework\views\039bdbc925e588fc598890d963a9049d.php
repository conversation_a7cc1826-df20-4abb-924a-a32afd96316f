<?php $__env->startSection('title', 'Edit Badge - ' . $badge->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Badge</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.badges.index')); ?>">Badges</a></li>
                    <li class="breadcrumb-item active">Edit <?php echo e($badge->name); ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?php echo e(route('admin.badges.show', $badge)); ?>" class="btn btn-info">
                <i class="fas fa-eye"></i> View Badge
            </a>
            <a href="<?php echo e(route('admin.badges.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Badges
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Badge Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Information</h6>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('admin.badges.update', $badge)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Badge Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="name" name="name" value="<?php echo e(old('name', $badge->name)); ?>" required>
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="slug" name="slug" value="<?php echo e(old('slug', $badge->slug)); ?>" required>
                                    <small class="form-text text-muted">URL-friendly version</small>
                                    <?php $__errorArgs = ['slug'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="description" name="description" rows="3" required><?php echo e(old('description', $badge->description)); ?></textarea>
                            <small class="form-text text-muted">What this badge represents</small>
                            <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Badge Type <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="type" name="type" required>
                                        <option value="">Select badge type</option>
                                        <option value="achievement" <?php echo e(old('type', $badge->type) === 'achievement' ? 'selected' : ''); ?>>Achievement</option>
                                        <option value="milestone" <?php echo e(old('type', $badge->type) === 'milestone' ? 'selected' : ''); ?>>Milestone</option>
                                        <option value="special" <?php echo e(old('type', $badge->type) === 'special' ? 'selected' : ''); ?>>Special</option>
                                        <option value="seasonal" <?php echo e(old('type', $badge->type) === 'seasonal' ? 'selected' : ''); ?>>Seasonal</option>
                                    </select>
                                    <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">Badge Color</label>
                                    <input type="color" class="form-control form-control-color <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="color" name="color" value="<?php echo e(old('color', $badge->color ?? '#6366f1')); ?>">
                                    <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="icon_url" class="form-label">Icon URL</label>
                                    <input type="url" class="form-control <?php $__errorArgs = ['icon_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="icon_url" name="icon_url" value="<?php echo e(old('icon_url', $badge->icon_url)); ?>" 
                                           placeholder="https://example.com/icon.png">
                                    <small class="form-text text-muted">URL to badge icon image</small>
                                    <?php $__errorArgs = ['icon_url'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="xp_reward" class="form-label">XP Reward</label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['xp_reward'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="xp_reward" name="xp_reward" value="<?php echo e(old('xp_reward', $badge->xp_reward)); ?>" min="0" max="1000">
                                    <small class="form-text text-muted">XP points awarded when earned</small>
                                    <?php $__errorArgs = ['xp_reward'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">Requirements (JSON)</label>
                            <textarea class="form-control <?php $__errorArgs = ['requirements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="requirements" name="requirements" rows="4"><?php echo e(old('requirements', json_encode($badge->requirements ?? [], JSON_PRETTY_PRINT))); ?></textarea>
                            <small class="form-text text-muted">JSON object defining badge requirements</small>
                            <?php $__errorArgs = ['requirements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $badge->sort_order)); ?>" min="0">
                                    <small class="form-text text-muted">Display order (lower numbers first)</small>
                                    <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_rare" name="is_rare" value="1" 
                                               <?php echo e(old('is_rare', $badge->is_rare) ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_rare">
                                            <strong>Rare Badge</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Mark as rare/special badge</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       <?php echo e(old('is_active', $badge->is_active) ? 'checked' : ''); ?>>
                                <label class="form-check-label" for="is_active">
                                    <strong>Active</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Badge can be earned by users</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?php echo e(route('admin.badges.index')); ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Badge
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Badge Preview & Stats -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Preview</h6>
                </div>
                <div class="card-body text-center">
                    <div class="badge-preview mb-3">
                        <div class="badge-icon" style="width: 80px; height: 80px; background-color: <?php echo e($badge->color ?? '#6366f1'); ?>; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                            <?php if($badge->icon_url): ?>
                                <img src="<?php echo e($badge->icon_url); ?>" alt="<?php echo e($badge->name); ?>" style="width: 50px; height: 50px; border-radius: 50%;">
                            <?php else: ?>
                                <i class="fas fa-award text-white" style="font-size: 2rem;"></i>
                            <?php endif; ?>
                        </div>
                        <h5 class="badge-name"><?php echo e($badge->name); ?></h5>
                        <p class="badge-description text-muted"><?php echo e($badge->description); ?></p>
                        <div class="badge-meta">
                            <span class="badge badge-primary"><?php echo e(ucfirst($badge->type)); ?></span>
                            <span class="badge badge-success"><?php echo e($badge->xp_reward ?? 0); ?> XP</span>
                            <?php if($badge->is_rare): ?>
                                <span class="badge badge-warning">Rare</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Badge Statistics -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary"><?php echo e($badge->users()->count()); ?></h4>
                                <small class="text-muted">Total Earned</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo e($badge->users()->wherePivot('created_at', '>=', now()->subDays(30))->count()); ?></h4>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.badges.show', $badge)); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <?php if($badge->users()->count() == 0): ?>
                        <form action="<?php echo e(route('admin.badges.destroy', $badge)); ?>" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this badge?')">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Badge
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const colorInput = document.getElementById('color');
    const badgeIcon = document.querySelector('.badge-icon');
    
    colorInput.addEventListener('input', function() {
        badgeIcon.style.backgroundColor = this.value;
    });

    document.getElementById('name').addEventListener('input', function() {
        document.querySelector('.badge-name').textContent = this.value || '<?php echo e($badge->name); ?>';
    });

    document.getElementById('description').addEventListener('input', function() {
        document.querySelector('.badge-description').textContent = this.value || '<?php echo e($badge->description); ?>';
    });

    document.getElementById('type').addEventListener('change', function() {
        document.querySelector('.badge-meta .badge-primary').textContent = this.value ? this.value.charAt(0).toUpperCase() + this.value.slice(1) : 'Achievement';
    });

    document.getElementById('xp_reward').addEventListener('input', function() {
        document.querySelector('.badge-meta .badge-success').textContent = (this.value || 0) + ' XP';
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/badges/edit.blade.php ENDPATH**/ ?>