<?php $__env->startSection('title', 'Edit Task - ' . $task->title); ?>
<?php $__env->startSection('page-title', 'Edit Task'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Edit Task</h1>
            <p class="text-gray-400 mt-1">Update task: <?php echo e($task->title); ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.tasks.show', $task)); ?>" class="btn-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Task
            </a>
            <a href="<?php echo e(route('admin.tasks.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Tasks
            </a>
        </div>
    </div>

    <form action="<?php echo e(route('admin.tasks.update', $task)); ?>" method="POST" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Task Title *</label>
                        <input type="text" name="title" id="title" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<?php echo e(old('title', $task->title)); ?>" required>
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                        <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required><?php echo e(old('description', $task->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-300 mb-2">Task Type *</label>
                            <select name="type" id="type" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required>
                                <option value="">Select task type</option>
                                <option value="daily" <?php echo e(old('type', $task->type) === 'daily' ? 'selected' : ''); ?>>Daily Task</option>
                                <option value="weekly" <?php echo e(old('type', $task->type) === 'weekly' ? 'selected' : ''); ?>>Weekly Task</option>
                                <option value="monthly" <?php echo e(old('type', $task->type) === 'monthly' ? 'selected' : ''); ?>>Monthly Task</option>
                                <option value="one_time" <?php echo e(old('type', $task->type) === 'one_time' ? 'selected' : ''); ?>>One-time Task</option>
                                <option value="milestone" <?php echo e(old('type', $task->type) === 'milestone' ? 'selected' : ''); ?>>Milestone</option>
                            </select>
                            <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                            <select name="category" id="category" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select category</option>
                                <option value="learning" <?php echo e(old('category', $task->category) === 'learning' ? 'selected' : ''); ?>>Learning</option>
                                <option value="social" <?php echo e(old('category', $task->category) === 'social' ? 'selected' : ''); ?>>Social</option>
                                <option value="achievement" <?php echo e(old('category', $task->category) === 'achievement' ? 'selected' : ''); ?>>Achievement</option>
                                <option value="engagement" <?php echo e(old('category', $task->category) === 'engagement' ? 'selected' : ''); ?>>Engagement</option>
                                <option value="special" <?php echo e(old('category', $task->category) === 'special' ? 'selected' : ''); ?>>Special</option>
                            </select>
                            <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Configuration -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Configuration</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="xp_reward" class="block text-sm font-medium text-gray-300 mb-2">XP Reward</label>
                            <input type="number" name="xp_reward" id="xp_reward" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<?php echo e(old('xp_reward', $task->xp_reward)); ?>" min="0" max="1000">
                            <p class="mt-1 text-xs text-gray-400">XP points awarded for completion</p>
                            <?php $__errorArgs = ['xp_reward'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="difficulty" class="block text-sm font-medium text-gray-300 mb-2">Difficulty</label>
                            <select name="difficulty" id="difficulty" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select difficulty</option>
                                <option value="easy" <?php echo e(old('difficulty', $task->difficulty) === 'easy' ? 'selected' : ''); ?>>Easy</option>
                                <option value="medium" <?php echo e(old('difficulty', $task->difficulty) === 'medium' ? 'selected' : ''); ?>>Medium</option>
                                <option value="hard" <?php echo e(old('difficulty', $task->difficulty) === 'hard' ? 'selected' : ''); ?>>Hard</option>
                                <option value="expert" <?php echo e(old('difficulty', $task->difficulty) === 'expert' ? 'selected' : ''); ?>>Expert</option>
                            </select>
                            <?php $__errorArgs = ['difficulty'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div>
                        <label for="requirements" class="block text-sm font-medium text-gray-300 mb-2">Requirements (JSON)</label>
                        <textarea name="requirements" id="requirements" rows="4" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"><?php echo e(old('requirements', json_encode($task->requirements ?? [], JSON_PRETTY_PRINT))); ?></textarea>
                        <p class="mt-1 text-xs text-gray-400">JSON object defining task completion requirements</p>
                        <?php $__errorArgs = ['requirements'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <!-- Scheduling -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Scheduling</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                            <input type="datetime-local" name="start_date" id="start_date" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<?php echo e(old('start_date', $task->start_date ? $task->start_date->format('Y-m-d\TH:i') : '')); ?>">
                            <p class="mt-1 text-xs text-gray-400">When this task becomes available</p>
                            <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                            <input type="datetime-local" name="end_date" id="end_date" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<?php echo e(old('end_date', $task->end_date ? $task->end_date->format('Y-m-d\TH:i') : '')); ?>">
                            <p class="mt-1 text-xs text-gray-400">When this task expires (optional)</p>
                            <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="max_completions" class="block text-sm font-medium text-gray-300 mb-2">Max Completions</label>
                            <input type="number" name="max_completions" id="max_completions" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<?php echo e(old('max_completions', $task->max_completions)); ?>" min="1">
                            <p class="mt-1 text-xs text-gray-400">Maximum times this task can be completed</p>
                            <?php $__errorArgs = ['max_completions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                            <input type="number" name="sort_order" id="sort_order" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="<?php echo e(old('sort_order', $task->sort_order)); ?>" min="0">
                            <p class="mt-1 text-xs text-gray-400">Display order (lower numbers first)</p>
                            <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Settings</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" <?php echo e(old('is_active', $task->is_active) ? 'checked' : ''); ?> class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_active" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Active</span>
                                <p class="text-xs text-gray-400">Task is available to users</p>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_featured" id="is_featured" value="1" <?php echo e(old('is_featured', $task->is_featured) ? 'checked' : ''); ?> class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_featured" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Featured Task</span>
                                <p class="text-xs text-gray-400">Highlight this task</p>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between">
                <a href="<?php echo e(route('admin.tasks.index')); ?>" class="btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Task
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Task Statistics -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-primary-400"><?php echo e($task->userTasks()->count()); ?></div>
                            <div class="text-sm text-gray-400">Total Attempts</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400"><?php echo e($task->userTasks()->where('is_completed', true)->count()); ?></div>
                            <div class="text-sm text-gray-400">Completions</div>
                        </div>
                    </div>
                    
                    <?php if($task->userTasks()->count() > 0): ?>
                    <div class="mt-4 text-center">
                        <div class="text-xl font-bold text-blue-400">
                            <?php echo e(round(($task->userTasks()->where('is_completed', true)->count() / $task->userTasks()->count()) * 100, 1)); ?>%
                        </div>
                        <div class="text-sm text-gray-400">Completion Rate</div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Task Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Preview</h3>
                </div>
                <div class="p-6">
                    <div class="task-preview">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-lg bg-primary-600 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2H9z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="task-title text-white font-medium"><?php echo e($task->title); ?></h4>
                                <p class="task-type text-sm text-gray-400"><?php echo e(ucfirst(str_replace('_', ' ', $task->type))); ?></p>
                            </div>
                        </div>
                        <p class="task-description text-gray-300 text-sm mb-4"><?php echo e($task->description); ?></p>
                        <div class="flex items-center space-x-2">
                            <span class="task-xp inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><?php echo e($task->xp_reward ?? 0); ?> XP</span>
                            <?php if($task->difficulty): ?>
                                <span class="task-difficulty inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"><?php echo e(ucfirst($task->difficulty)); ?></span>
                            <?php endif; ?>
                            <?php if($task->is_featured): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Featured</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('admin.tasks.show', $task)); ?>" class="w-full btn-outline text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Details
                    </a>
                    <?php if($task->userTasks()->count() == 0): ?>
                    <form action="<?php echo e(route('admin.tasks.destroy', $task)); ?>" method="POST" class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this task?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-danger text-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Task
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </form>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const typeSelect = document.getElementById('type');
    const xpInput = document.getElementById('xp_reward');
    const difficultySelect = document.getElementById('difficulty');

    titleInput.addEventListener('input', function() {
        document.querySelector('.task-title').textContent = this.value || '<?php echo e($task->title); ?>';
    });

    descriptionInput.addEventListener('input', function() {
        document.querySelector('.task-description').textContent = this.value || '<?php echo e($task->description); ?>';
    });

    typeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        document.querySelector('.task-type').textContent = selectedOption.text || '<?php echo e(ucfirst(str_replace("_", " ", $task->type))); ?>';
    });

    xpInput.addEventListener('input', function() {
        document.querySelector('.task-xp').textContent = (this.value || 0) + ' XP';
    });

    difficultySelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            document.querySelector('.task-difficulty').textContent = selectedOption.text;
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/tasks/edit.blade.php ENDPATH**/ ?>