<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageOptimizationService
{
    protected ImageManager $imageManager;
    protected array $config;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
        $this->config = config('image_optimization', [
            'quality' => 85,
            'max_width' => 1920,
            'max_height' => 1080,
            'thumbnail_sizes' => [
                'small' => [150, 150],
                'medium' => [300, 300],
                'large' => [600, 600],
            ],
            'formats' => ['webp', 'jpg'],
            'progressive' => true,
        ]);
    }

    /**
     * Optimize and store an uploaded image
     */
    public function optimizeAndStore(UploadedFile $file, string $path, array $options = []): array
    {
        $options = array_merge($this->config, $options);
        $results = [];

        // Generate unique filename
        $filename = $this->generateFilename($file, $path);
        
        // Load the image
        $image = $this->imageManager->read($file->getPathname());
        
        // Resize if needed
        if ($image->width() > $options['max_width'] || $image->height() > $options['max_height']) {
            $image = $image->scale(
                width: $options['max_width'],
                height: $options['max_height']
            );
        }

        // Generate optimized versions in different formats
        foreach ($options['formats'] as $format) {
            $optimizedFilename = $this->changeExtension($filename, $format);
            $optimizedPath = $path . '/' . $optimizedFilename;
            
            $optimizedImage = clone $image;
            
            // Apply format-specific optimizations
            switch ($format) {
                case 'webp':
                    $encoded = $optimizedImage->toWebp($options['quality']);
                    break;
                case 'jpg':
                case 'jpeg':
                    $encoded = $optimizedImage->toJpeg($options['quality']);
                    break;
                case 'png':
                    $encoded = $optimizedImage->toPng();
                    break;
                default:
                    continue 2;
            }
            
            // Store the optimized image
            Storage::disk('public')->put($optimizedPath, $encoded);
            
            $results[$format] = [
                'path' => $optimizedPath,
                'url' => Storage::disk('public')->url($optimizedPath),
                'size' => Storage::disk('public')->size($optimizedPath),
                'width' => $optimizedImage->width(),
                'height' => $optimizedImage->height(),
            ];
        }

        // Generate thumbnails
        if (!empty($options['thumbnail_sizes'])) {
            $results['thumbnails'] = $this->generateThumbnails($image, $path, $filename, $options);
        }

        return $results;
    }

    /**
     * Generate thumbnails in different sizes
     */
    public function generateThumbnails($image, string $path, string $filename, array $options): array
    {
        $thumbnails = [];

        foreach ($options['thumbnail_sizes'] as $size => [$width, $height]) {
            $thumbnail = clone $image;
            $thumbnail = $thumbnail->cover($width, $height);
            
            foreach ($options['formats'] as $format) {
                $thumbFilename = $this->generateThumbnailFilename($filename, $size, $format);
                $thumbPath = $path . '/thumbnails/' . $thumbFilename;
                
                // Apply format-specific encoding
                switch ($format) {
                    case 'webp':
                        $encoded = $thumbnail->toWebp($options['quality']);
                        break;
                    case 'jpg':
                    case 'jpeg':
                        $encoded = $thumbnail->toJpeg($options['quality']);
                        break;
                    case 'png':
                        $encoded = $thumbnail->toPng();
                        break;
                    default:
                        continue 2;
                }
                
                Storage::disk('public')->put($thumbPath, $encoded);
                
                $thumbnails[$size][$format] = [
                    'path' => $thumbPath,
                    'url' => Storage::disk('public')->url($thumbPath),
                    'size' => Storage::disk('public')->size($thumbPath),
                    'width' => $width,
                    'height' => $height,
                ];
            }
        }

        return $thumbnails;
    }

    /**
     * Optimize existing image
     */
    public function optimizeExisting(string $imagePath, array $options = []): array
    {
        $options = array_merge($this->config, $options);
        
        if (!Storage::disk('public')->exists($imagePath)) {
            throw new \Exception("Image not found: {$imagePath}");
        }

        $imageContent = Storage::disk('public')->get($imagePath);
        $image = $this->imageManager->read($imageContent);
        
        // Get original size for comparison
        $originalSize = Storage::disk('public')->size($imagePath);
        
        // Apply optimizations
        if ($image->width() > $options['max_width'] || $image->height() > $options['max_height']) {
            $image = $image->scale(
                width: $options['max_width'],
                height: $options['max_height']
            );
        }

        // Re-encode with optimization
        $pathInfo = pathinfo($imagePath);
        $extension = strtolower($pathInfo['extension']);
        
        switch ($extension) {
            case 'webp':
                $encoded = $image->toWebp($options['quality']);
                break;
            case 'jpg':
            case 'jpeg':
                $encoded = $image->toJpeg($options['quality']);
                break;
            case 'png':
                $encoded = $image->toPng();
                break;
            default:
                throw new \Exception("Unsupported image format: {$extension}");
        }

        // Store optimized version
        Storage::disk('public')->put($imagePath, $encoded);
        $newSize = Storage::disk('public')->size($imagePath);
        
        return [
            'original_size' => $originalSize,
            'optimized_size' => $newSize,
            'savings' => $originalSize - $newSize,
            'savings_percentage' => round((($originalSize - $newSize) / $originalSize) * 100, 2),
            'width' => $image->width(),
            'height' => $image->height(),
        ];
    }

    /**
     * Batch optimize images in a directory
     */
    public function batchOptimize(string $directory, array $options = []): array
    {
        $results = [];
        $files = Storage::disk('public')->files($directory);
        
        foreach ($files as $file) {
            if ($this->isImageFile($file)) {
                try {
                    $result = $this->optimizeExisting($file, $options);
                    $results[$file] = $result;
                } catch (\Exception $e) {
                    $results[$file] = ['error' => $e->getMessage()];
                }
            }
        }

        return $results;
    }

    /**
     * Generate responsive image HTML
     */
    public function generateResponsiveHtml(array $imageData, string $alt = '', array $attributes = []): string
    {
        if (empty($imageData)) {
            return '';
        }

        $html = '<picture>';
        
        // Add WebP sources if available
        if (isset($imageData['webp'])) {
            $html .= '<source srcset="' . $imageData['webp']['url'] . '" type="image/webp">';
        }
        
        // Add fallback image
        $fallbackFormat = isset($imageData['jpg']) ? 'jpg' : (isset($imageData['jpeg']) ? 'jpeg' : 'png');
        if (isset($imageData[$fallbackFormat])) {
            $attributeString = '';
            foreach ($attributes as $key => $value) {
                $attributeString .= " {$key}=\"{$value}\"";
            }
            
            $html .= '<img src="' . $imageData[$fallbackFormat]['url'] . '" alt="' . htmlspecialchars($alt) . '"' . $attributeString . '>';
        }
        
        $html .= '</picture>';
        
        return $html;
    }

    /**
     * Generate srcset for responsive images
     */
    public function generateSrcset(array $thumbnails, string $format = 'webp'): string
    {
        $srcset = [];
        
        foreach ($thumbnails as $size => $formats) {
            if (isset($formats[$format])) {
                $width = $formats[$format]['width'];
                $srcset[] = $formats[$format]['url'] . " {$width}w";
            }
        }
        
        return implode(', ', $srcset);
    }

    /**
     * Clean up old image versions
     */
    public function cleanup(string $imagePath): bool
    {
        $pathInfo = pathinfo($imagePath);
        $directory = $pathInfo['dirname'];
        $filename = $pathInfo['filename'];
        
        // Delete main image and all its variants
        $patterns = [
            $imagePath,
            $directory . '/' . $filename . '.webp',
            $directory . '/' . $filename . '.jpg',
            $directory . '/' . $filename . '.jpeg',
            $directory . '/' . $filename . '.png',
        ];
        
        foreach ($patterns as $pattern) {
            if (Storage::disk('public')->exists($pattern)) {
                Storage::disk('public')->delete($pattern);
            }
        }
        
        // Delete thumbnails
        $thumbnailDir = $directory . '/thumbnails';
        if (Storage::disk('public')->exists($thumbnailDir)) {
            $thumbnailFiles = Storage::disk('public')->files($thumbnailDir);
            foreach ($thumbnailFiles as $file) {
                if (str_contains($file, $filename)) {
                    Storage::disk('public')->delete($file);
                }
            }
        }
        
        return true;
    }

    /**
     * Get image information
     */
    public function getImageInfo(string $imagePath): array
    {
        if (!Storage::disk('public')->exists($imagePath)) {
            throw new \Exception("Image not found: {$imagePath}");
        }

        $imageContent = Storage::disk('public')->get($imagePath);
        $image = $this->imageManager->read($imageContent);
        
        return [
            'width' => $image->width(),
            'height' => $image->height(),
            'size' => Storage::disk('public')->size($imagePath),
            'mime_type' => $image->origin()->mediaType(),
            'url' => Storage::disk('public')->url($imagePath),
        ];
    }

    /**
     * Generate unique filename
     */
    protected function generateFilename(UploadedFile $file, string $path): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = \Str::slug($name);
        
        return $name . '_' . time() . '.' . $extension;
    }

    /**
     * Generate thumbnail filename
     */
    protected function generateThumbnailFilename(string $filename, string $size, string $format): string
    {
        $pathInfo = pathinfo($filename);
        return $pathInfo['filename'] . '_' . $size . '.' . $format;
    }

    /**
     * Change file extension
     */
    protected function changeExtension(string $filename, string $newExtension): string
    {
        $pathInfo = pathinfo($filename);
        return $pathInfo['filename'] . '.' . $newExtension;
    }

    /**
     * Check if file is an image
     */
    protected function isImageFile(string $filename): bool
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp']);
    }
}
