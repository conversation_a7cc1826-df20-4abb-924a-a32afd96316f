@extends('layouts.dashboard')

@section('title', 'Account Settings - The Real World')
@section('page-title', 'Account Settings')

@section('content')
<div class="p-6">
    <!-- Settings Navigation -->
    <div class="mb-8">
        <nav class="flex space-x-8">
            <a href="{{ route('dashboard.settings.index') }}" 
               class="pb-2 border-b-2 border-primary-500 text-primary-400 font-medium">
                General
            </a>
            <a href="{{ route('dashboard.settings.subscription') }}" 
               class="pb-2 border-b-2 border-transparent text-gray-400 hover:text-white transition-colors">
                Subscription
            </a>
            <a href="{{ route('dashboard.settings.billing') }}" 
               class="pb-2 border-b-2 border-transparent text-gray-400 hover:text-white transition-colors">
                Billing
            </a>
        </nav>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Notification Settings -->
        <div class="card">
            <h3 class="text-xl font-semibold mb-6">Notification Preferences</h3>
            
            <form action="{{ route('dashboard.settings.notifications') }}" method="POST">
                @csrf
                @method('PUT')

                <div class="space-y-6">
                    <!-- Email Notifications -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Email Notifications</h4>
                            <p class="text-sm text-gray-400">Receive notifications via email</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="email_notifications" 
                                   value="1"
                                   {{ $notificationSettings['email_notifications'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Push Notifications -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Push Notifications</h4>
                            <p class="text-sm text-gray-400">Receive push notifications in browser</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="push_notifications" 
                                   value="1"
                                   {{ $notificationSettings['push_notifications'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Marketing Emails -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Marketing Emails</h4>
                            <p class="text-sm text-gray-400">Receive promotional emails and updates</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="marketing_emails" 
                                   value="1"
                                   {{ $notificationSettings['marketing_emails'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Event Reminders -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Event Reminders</h4>
                            <p class="text-sm text-gray-400">Get reminded about upcoming events</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="event_reminders" 
                                   value="1"
                                   {{ $notificationSettings['event_reminders'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Lesson Updates -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Lesson Updates</h4>
                            <p class="text-sm text-gray-400">Notifications about new lessons and content</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="lesson_updates" 
                                   value="1"
                                   {{ $notificationSettings['lesson_updates'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Chat Mentions -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Chat Mentions</h4>
                            <p class="text-sm text-gray-400">Get notified when someone mentions you in chat</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="chat_mentions" 
                                   value="1"
                                   {{ $notificationSettings['chat_mentions'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit" class="btn-primary">
                        Save Notification Settings
                    </button>
                </div>
            </form>
        </div>

        <!-- Privacy Settings -->
        <div class="card">
            <h3 class="text-xl font-semibold mb-6">Privacy Settings</h3>
            
            <form action="{{ route('dashboard.settings.privacy') }}" method="POST">
                @csrf
                @method('PUT')

                <div class="space-y-6">
                    <!-- Profile Visibility -->
                    <div>
                        <h4 class="font-medium mb-3">Profile Visibility</h4>
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="radio" 
                                       name="profile_visibility" 
                                       value="public"
                                       {{ $privacySettings['profile_visibility'] === 'public' ? 'checked' : '' }}
                                       class="w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 focus:ring-primary-500">
                                <span class="ml-3">
                                    <span class="font-medium">Public</span>
                                    <span class="block text-sm text-gray-400">Anyone can view your profile</span>
                                </span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" 
                                       name="profile_visibility" 
                                       value="private"
                                       {{ $privacySettings['profile_visibility'] === 'private' ? 'checked' : '' }}
                                       class="w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 focus:ring-primary-500">
                                <span class="ml-3">
                                    <span class="font-medium">Private</span>
                                    <span class="block text-sm text-gray-400">Only you can view your profile</span>
                                </span>
                            </label>
                        </div>
                    </div>

                    <!-- Show Progress -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Show Learning Progress</h4>
                            <p class="text-sm text-gray-400">Display your course progress publicly</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="show_progress" 
                                   value="1"
                                   {{ $privacySettings['show_progress'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Show Badges -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Show Badges</h4>
                            <p class="text-sm text-gray-400">Display your earned badges publicly</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="show_badges" 
                                   value="1"
                                   {{ $privacySettings['show_badges'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <!-- Allow Messages -->
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium">Allow Direct Messages</h4>
                            <p class="text-sm text-gray-400">Let other members send you direct messages</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   name="allow_messages" 
                                   value="1"
                                   {{ $privacySettings['allow_messages'] ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit" class="btn-primary">
                        Save Privacy Settings
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Subscription Overview -->
    <div class="mt-8">
        <div class="card">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-xl font-semibold mb-2">Current Subscription</h3>
                    <p class="text-gray-400">Manage your subscription and billing information</p>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-primary-400 mb-1">
                        {{ ucfirst($subscriptionInfo['current_plan']) }}
                    </div>
                    <p class="text-sm text-gray-400">Current Plan</p>
                </div>
            </div>

            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                @foreach($subscriptionInfo['plan_features'] as $feature => $description)
                <div class="bg-gray-700 rounded-lg p-4">
                    <h4 class="font-medium capitalize mb-2">{{ str_replace('_', ' ', $feature) }}</h4>
                    <p class="text-sm text-gray-400">{{ $description }}</p>
                </div>
                @endforeach
            </div>

            <div class="mt-6 flex items-center justify-between">
                <div class="flex space-x-4">
                    <a href="{{ route('dashboard.settings.subscription') }}" class="btn-primary">
                        Manage Subscription
                    </a>
                    <a href="{{ route('dashboard.settings.billing') }}" class="btn-secondary">
                        View Billing
                    </a>
                </div>
                
                @if($subscriptionInfo['can_cancel'])
                <div class="text-sm text-gray-400">
                    Next billing: {{ $subscriptionInfo['next_billing_date']->format('M j, Y') }}
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="mt-8">
        <div class="card border-red-600">
            <h3 class="text-xl font-semibold mb-4 text-red-400">Danger Zone</h3>
            
            <div class="bg-red-600/10 border border-red-600/20 rounded-lg p-6">
                <div class="flex items-start justify-between">
                    <div>
                        <h4 class="font-medium text-red-400 mb-2">Delete Account</h4>
                        <p class="text-sm text-gray-400 mb-4">
                            Permanently delete your account and all associated data. This action cannot be undone.
                        </p>
                        <ul class="text-sm text-gray-400 space-y-1">
                            <li>• All your progress will be lost</li>
                            <li>• Your subscription will be cancelled</li>
                            <li>• You will lose access to all content</li>
                            <li>• This action is irreversible</li>
                        </ul>
                    </div>
                    <button onclick="openDeleteModal()" 
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                        Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-semibold mb-4 text-red-400">Confirm Account Deletion</h3>
        
        <form action="{{ route('dashboard.settings.delete-account') }}" method="POST">
            @csrf
            @method('DELETE')
            
            <div class="mb-4">
                <p class="text-gray-300 mb-4">
                    This action will permanently delete your account. Please enter your password and type "DELETE" to confirm.
                </p>
                
                <div class="space-y-4">
                    <div>
                        <label for="delete_password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                        <input type="password" 
                               name="password" 
                               id="delete_password"
                               class="input-field"
                               required>
                    </div>
                    
                    <div>
                        <label for="delete_confirmation" class="block text-sm font-medium text-gray-300 mb-2">Type "DELETE" to confirm</label>
                        <input type="text" 
                               name="confirmation" 
                               id="delete_confirmation"
                               class="input-field"
                               required>
                    </div>
                </div>
            </div>
            
            <div class="flex items-center justify-end space-x-4">
                <button type="button" onclick="closeDeleteModal()" class="btn-secondary">Cancel</button>
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                    Delete Account
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openDeleteModal() {
    document.getElementById('deleteModal').classList.remove('hidden');
    document.getElementById('deleteModal').classList.add('flex');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    document.getElementById('deleteModal').classList.remove('flex');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
@endsection
