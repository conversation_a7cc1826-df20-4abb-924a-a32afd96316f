<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security-related configuration options for The Real World
    | application. These settings help protect against common security threats.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for different types of requests to prevent abuse.
    |
    */
    'rate_limiting' => [
        'login' => [
            'max_attempts' => 5,
            'decay_minutes' => 15,
        ],
        'registration' => [
            'max_attempts' => 3,
            'decay_minutes' => 60,
        ],
        'password_reset' => [
            'max_attempts' => 3,
            'decay_minutes' => 60,
        ],
        'api' => [
            'max_attempts' => 60,
            'decay_minutes' => 1,
        ],
        'search' => [
            'max_attempts' => 30,
            'decay_minutes' => 1,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Security
    |--------------------------------------------------------------------------
    |
    | Configure password security requirements and breach checking.
    |
    */
    'password' => [
        'min_length' => 8,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'check_breaches' => env('CHECK_PASSWORD_BREACHES', true),
        'max_age_days' => 90, // Force password change after 90 days
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    |
    | Configure session security settings.
    |
    */
    'session' => [
        'timeout_minutes' => 120, // 2 hours
        'regenerate_on_login' => true,
        'invalidate_on_password_change' => true,
        'concurrent_sessions' => 3, // Max concurrent sessions per user
    ],

    /*
    |--------------------------------------------------------------------------
    | IP Security
    |--------------------------------------------------------------------------
    |
    | Configure IP-based security measures.
    |
    */
    'ip_security' => [
        'enable_blocking' => env('ENABLE_IP_BLOCKING', true),
        'max_failed_attempts' => 10,
        'block_duration_minutes' => 60,
        'whitelist' => [
            '127.0.0.1',
            '::1',
        ],
        'blacklist' => [
            // Add known malicious IPs here
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Security
    |--------------------------------------------------------------------------
    |
    | Configure content security policies and input validation.
    |
    */
    'content_security' => [
        'enable_xss_protection' => true,
        'enable_sql_injection_detection' => true,
        'max_upload_size' => 10 * 1024 * 1024, // 10MB
        'allowed_file_types' => [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'videos' => ['mp4', 'webm', 'ogg'],
            'documents' => ['pdf', 'doc', 'docx', 'txt'],
        ],
        'scan_uploads' => env('SCAN_UPLOADS', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Two-Factor Authentication
    |--------------------------------------------------------------------------
    |
    | Configure 2FA settings.
    |
    */
    'two_factor' => [
        'enabled' => env('TWO_FACTOR_ENABLED', false),
        'required_for_admin' => true,
        'backup_codes_count' => 8,
        'window' => 1, // Time window for TOTP validation
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit Logging
    |--------------------------------------------------------------------------
    |
    | Configure security audit logging.
    |
    */
    'audit' => [
        'enabled' => env('SECURITY_AUDIT_ENABLED', true),
        'log_channel' => 'security',
        'events' => [
            'login_success',
            'login_failed',
            'password_changed',
            'email_changed',
            'account_locked',
            'suspicious_activity',
            'admin_action',
            'permission_changed',
        ],
        'retention_days' => 90,
    ],

    /*
    |--------------------------------------------------------------------------
    | CSRF Protection
    |--------------------------------------------------------------------------
    |
    | Configure CSRF protection settings.
    |
    */
    'csrf' => [
        'enabled' => true,
        'token_lifetime' => 120, // minutes
        'regenerate_on_use' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | API Security
    |--------------------------------------------------------------------------
    |
    | Configure API security settings.
    |
    */
    'api' => [
        'require_https' => env('API_REQUIRE_HTTPS', true),
        'token_lifetime' => 60 * 24, // 24 hours in minutes
        'refresh_token_lifetime' => 60 * 24 * 30, // 30 days in minutes
        'max_tokens_per_user' => 5,
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    |
    | Configure security headers to be sent with responses.
    |
    */
    'headers' => [
        'x_content_type_options' => 'nosniff',
        'x_frame_options' => 'DENY',
        'x_xss_protection' => '1; mode=block',
        'strict_transport_security' => 'max-age=31536000; includeSubDomains',
        'content_security_policy' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https://js.stripe.com",
            'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com",
            'font-src' => "'self' https://fonts.gstatic.com",
            'img-src' => "'self' data: https:",
            'connect-src' => "'self' https://api.stripe.com",
            'frame-src' => "https://js.stripe.com",
        ],
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'permissions_policy' => 'geolocation=(), microphone=(), camera=()',
    ],

    /*
    |--------------------------------------------------------------------------
    | Encryption
    |--------------------------------------------------------------------------
    |
    | Configure encryption settings for sensitive data.
    |
    */
    'encryption' => [
        'algorithm' => 'AES-256-CBC',
        'key_rotation_days' => 365,
        'encrypt_database_fields' => [
            'users.social_security_number',
            'users.payment_info',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring
    |--------------------------------------------------------------------------
    |
    | Configure security monitoring and alerting.
    |
    */
    'monitoring' => [
        'enabled' => env('SECURITY_MONITORING_ENABLED', true),
        'alert_threshold' => [
            'failed_logins_per_minute' => 10,
            'new_user_registrations_per_minute' => 5,
            'api_requests_per_minute' => 1000,
        ],
        'notification_channels' => [
            'email' => env('SECURITY_ALERT_EMAIL', '<EMAIL>'),
            'slack' => env('SECURITY_ALERT_SLACK_WEBHOOK'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup and Recovery
    |--------------------------------------------------------------------------
    |
    | Configure security-related backup settings.
    |
    */
    'backup' => [
        'encrypt_backups' => true,
        'backup_encryption_key' => env('BACKUP_ENCRYPTION_KEY'),
        'secure_delete' => true, // Securely delete old backups
    ],
];
