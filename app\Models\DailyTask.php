<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyTask extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'target_value',
        'xp_reward',
        'icon',
        'is_active',
        'difficulty_level',
        'requirements',
    ];

    protected $casts = [
        'requirements' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get user tasks for this daily task
     */
    public function userTasks()
    {
        return $this->hasMany(UserTask::class, 'task_id');
    }

    /**
     * Get active daily tasks
     */
    public static function getActiveTasks()
    {
        return self::where('is_active', true)->get();
    }

    /**
     * Get tasks by difficulty level
     */
    public static function getTasksByDifficulty(int $level)
    {
        return self::where('is_active', true)
            ->where('difficulty_level', $level)
            ->get();
    }

    /**
     * Get random daily tasks for user
     */
    public static function getRandomTasksForUser(User $user, int $count = 3)
    {
        $userLevel = $user->level ?? 1;

        // Get tasks appropriate for user level
        $maxDifficulty = min(5, max(1, ceil($userLevel / 10)));

        return self::where('is_active', true)
            ->where('difficulty_level', '<=', $maxDifficulty)
            ->inRandomOrder()
            ->take($count)
            ->get();
    }

    /**
     * Check if user can complete this task
     */
    public function canUserComplete(User $user): bool
    {
        // Check if user already completed this task today
        $today = now()->format('Y-m-d');
        $existingTask = UserTask::where('user_id', $user->id)
            ->where('task_id', $this->id)
            ->whereDate('completion_date', $today)
            ->first();

        return !$existingTask;
    }

    /**
     * Get task progress for user
     */
    public function getProgressForUser(User $user): array
    {
        $today = now()->format('Y-m-d');
        $userTask = UserTask::where('user_id', $user->id)
            ->where('task_id', $this->id)
            ->whereDate('assigned_date', $today)
            ->first();

        if (!$userTask) {
            return [
                'current' => 0,
                'target' => $this->target_value,
                'percentage' => 0,
                'is_completed' => false,
            ];
        }

        $percentage = $this->target_value > 0
            ? min(100, ($userTask->progress / $this->target_value) * 100)
            : 0;

        return [
            'current' => $userTask->progress,
            'target' => $this->target_value,
            'percentage' => $percentage,
            'is_completed' => $userTask->is_completed,
        ];
    }
}
