@props([
    'name' => 'file',
    'accept' => '*',
    'multiple' => false,
    'maxSize' => '10MB',
    'collection' => null,
    'preview' => true,
    'dropzone' => true,
])

<div class="file-upload-component" data-name="{{ $name }}" data-collection="{{ $collection }}">
    @if($dropzone)
    <!-- Dropzone -->
    <div class="dropzone border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-primary-500 transition-colors cursor-pointer"
         onclick="document.getElementById('{{ $name }}Input').click()">
        <div class="dropzone-content">
            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            <h3 class="text-lg font-medium mb-2">Drop files here or click to upload</h3>
            <p class="text-gray-400 text-sm">Maximum file size: {{ $maxSize }}</p>
            @if($accept !== '*')
                <p class="text-gray-400 text-xs mt-1">Accepted formats: {{ $accept }}</p>
            @endif
        </div>
        
        <!-- Drag overlay -->
        <div class="drag-overlay hidden absolute inset-0 bg-primary-600 bg-opacity-20 border-2 border-primary-500 rounded-lg flex items-center justify-center">
            <div class="text-center">
                <svg class="w-16 h-16 text-primary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <h3 class="text-xl font-bold text-primary-400">Drop files to upload</h3>
            </div>
        </div>
    </div>
    @endif

    <!-- Hidden file input -->
    <input type="file" 
           id="{{ $name }}Input"
           name="{{ $name }}"
           accept="{{ $accept }}"
           {{ $multiple ? 'multiple' : '' }}
           class="hidden"
           onchange="handleFileSelect(this)">

    @if(!$dropzone)
    <!-- Simple file input -->
    <div class="simple-upload">
        <label for="{{ $name }}Input" class="btn-secondary cursor-pointer inline-flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Choose Files
        </label>
        <span class="ml-3 text-sm text-gray-400">Maximum file size: {{ $maxSize }}</span>
    </div>
    @endif

    <!-- Upload progress -->
    <div class="upload-progress hidden mt-4">
        <div class="bg-gray-700 rounded-full h-2 mb-2">
            <div class="progress-bar bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
        </div>
        <div class="flex justify-between text-sm text-gray-400">
            <span class="progress-text">Uploading...</span>
            <span class="progress-percentage">0%</span>
        </div>
    </div>

    @if($preview)
    <!-- File preview -->
    <div class="file-preview mt-4 hidden">
        <h4 class="font-medium mb-3">Selected Files</h4>
        <div class="preview-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <!-- Preview items will be added here -->
        </div>
    </div>
    @endif

    <!-- Upload results -->
    <div class="upload-results mt-4 hidden">
        <h4 class="font-medium mb-3">Upload Results</h4>
        <div class="results-list space-y-2">
            <!-- Results will be added here -->
        </div>
    </div>
</div>

<script>
// File upload functionality
function handleFileSelect(input) {
    const component = input.closest('.file-upload-component');
    const files = Array.from(input.files);
    
    if (files.length === 0) return;
    
    // Show preview if enabled
    const previewContainer = component.querySelector('.file-preview');
    if (previewContainer) {
        showFilePreview(files, previewContainer);
    }
    
    // Auto-upload if desired
    if (component.dataset.autoUpload !== 'false') {
        uploadFiles(files, component);
    }
}

// Show file preview
function showFilePreview(files, container) {
    const grid = container.querySelector('.preview-grid');
    grid.innerHTML = '';
    
    files.forEach((file, index) => {
        const item = document.createElement('div');
        item.className = 'preview-item bg-gray-700 rounded-lg p-3';
        
        let preview = '';
        if (file.type.startsWith('image/')) {
            const url = URL.createObjectURL(file);
            preview = `<img src="${url}" alt="${file.name}" class="w-full h-24 object-cover rounded mb-2">`;
        } else {
            preview = `<div class="w-full h-24 bg-gray-600 rounded mb-2 flex items-center justify-center">
                <span class="text-2xl">${getFileIcon(file.type)}</span>
            </div>`;
        }
        
        item.innerHTML = `
            ${preview}
            <h5 class="font-medium text-sm truncate">${file.name}</h5>
            <p class="text-xs text-gray-400">${formatFileSize(file.size)}</p>
        `;
        
        grid.appendChild(item);
    });
    
    container.classList.remove('hidden');
}

// Upload files
async function uploadFiles(files, component) {
    const progressContainer = component.querySelector('.upload-progress');
    const progressBar = component.querySelector('.progress-bar');
    const progressText = component.querySelector('.progress-text');
    const progressPercentage = component.querySelector('.progress-percentage');
    const resultsContainer = component.querySelector('.upload-results');
    const resultsList = component.querySelector('.results-list');
    
    progressContainer.classList.remove('hidden');
    
    const collection = component.dataset.collection;
    const results = [];
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        try {
            progressText.textContent = `Uploading ${file.name}...`;
            
            const formData = new FormData();
            formData.append('file', file);
            if (collection) formData.append('collection', collection);
            formData.append('is_public', '1');
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);
            
            const response = await fetch('/dashboard/media/upload', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                results.push({ success: true, file: file.name, media: data.media });
            } else {
                results.push({ success: false, file: file.name, error: data.message });
            }
            
        } catch (error) {
            results.push({ success: false, file: file.name, error: error.message });
        }
        
        // Update progress
        const progress = ((i + 1) / files.length) * 100;
        progressBar.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';
    }
    
    // Show results
    progressText.textContent = 'Upload complete!';
    showUploadResults(results, resultsContainer, resultsList);
    
    // Hide progress after delay
    setTimeout(() => {
        progressContainer.classList.add('hidden');
    }, 2000);
}

// Show upload results
function showUploadResults(results, container, list) {
    list.innerHTML = '';
    
    results.forEach(result => {
        const item = document.createElement('div');
        item.className = `result-item flex items-center justify-between p-3 rounded-lg ${
            result.success ? 'bg-green-600/20 border border-green-600/30' : 'bg-red-600/20 border border-red-600/30'
        }`;
        
        item.innerHTML = `
            <div class="flex items-center">
                <span class="mr-3">${result.success ? '✅' : '❌'}</span>
                <span class="font-medium">${result.file}</span>
            </div>
            <div class="text-sm ${result.success ? 'text-green-400' : 'text-red-400'}">
                ${result.success ? 'Uploaded' : result.error}
            </div>
        `;
        
        list.appendChild(item);
    });
    
    container.classList.remove('hidden');
}

// Drag and drop functionality
document.addEventListener('DOMContentLoaded', function() {
    const dropzones = document.querySelectorAll('.dropzone');
    
    dropzones.forEach(dropzone => {
        const overlay = dropzone.querySelector('.drag-overlay');
        
        dropzone.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            overlay.classList.remove('hidden');
        });
        
        dropzone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (!dropzone.contains(e.relatedTarget)) {
                overlay.classList.add('hidden');
            }
        });
        
        dropzone.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            overlay.classList.add('hidden');
            
            const files = Array.from(e.dataTransfer.files);
            const input = dropzone.closest('.file-upload-component').querySelector('input[type="file"]');
            
            // Create a new FileList
            const dt = new DataTransfer();
            files.forEach(file => dt.items.add(file));
            input.files = dt.files;
            
            handleFileSelect(input);
        });
    });
});

// Utility functions
function getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('audio/')) return '🎵';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word')) return '📝';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
    return '📁';
}

function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return Math.round(size * 100) / 100 + ' ' + units[unitIndex];
}
</script>

<style>
.dropzone {
    position: relative;
    min-height: 200px;
}

.drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
}

.file-upload-component .preview-item {
    transition: transform 0.2s;
}

.file-upload-component .preview-item:hover {
    transform: translateY(-2px);
}
</style>
