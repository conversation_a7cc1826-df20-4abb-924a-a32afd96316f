<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'campus_id',
        'title',
        'description',
        'instructions',
        'task_type',
        'task_data',
        'xp_reward',
        'sort_order',
        'is_active',
        'is_daily',
        'difficulty',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_daily' => 'boolean',
        'task_data' => 'array',
    ];

    /**
     * Relationships
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    public function userTasks(): HasMany
    {
        return $this->hasMany(UserTask::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDaily($query)
    {
        return $query->where('is_daily', true);
    }

    /**
     * Helper Methods
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    public function isCompletedByUserToday(User $user): bool
    {
        return $this->userTasks()
            ->where('user_id', $user->id)
            ->whereDate('completed_at', today())
            ->exists();
    }

    public function getCompletionCountForUser(User $user): int
    {
        return $this->userTasks()
            ->where('user_id', $user->id)
            ->count();
    }

    public function getTotalCompletions(): int
    {
        return $this->userTasks()->count();
    }

    public function getIconHtml(): string
    {
        // Default icons based on task type
        $defaultIcons = [
            'lesson' => '📚',
            'login' => '🔑',
            'profile' => '👤',
            'social' => '📱',
            'referral' => '🤝',
            'streak' => '🔥',
            'course' => '🎓',
            'chat' => '💬',
            'event' => '📅',
            'general' => '✅',
        ];

        return $defaultIcons[$this->task_type] ?? '✅';
    }

    public function getDifficultyColor(): string
    {
        $colors = [
            'easy' => '#10B981',
            'medium' => '#F59E0B',
            'hard' => '#EF4444',
        ];

        return $colors[$this->difficulty] ?? '#6B7280';
    }
}
