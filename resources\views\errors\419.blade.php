@extends('layouts.app')

@section('title', '419 - Page Expired - The Real World')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
        <!-- Error Icon -->
        <div class="mb-8">
            <div class="mx-auto w-24 h-24 bg-yellow-600 rounded-full flex items-center justify-center">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>

        <!-- Error Code -->
        <h1 class="text-6xl font-bold text-yellow-400 mb-4">419</h1>
        
        <!-- Error Message -->
        <h2 class="text-2xl font-bold text-white mb-4">Page Expired</h2>
        
        <p class="text-gray-400 mb-8 leading-relaxed">
            Your session has expired for security reasons. This usually happens when:
        </p>

        <!-- Reasons List -->
        <div class="text-left bg-gray-800 rounded-lg p-6 mb-8">
            <ul class="space-y-3 text-gray-300">
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    You've been inactive for too long
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Your security token has expired
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    The page was left open for an extended period
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    You submitted a form after the session expired
                </li>
            </ul>
        </div>

        <!-- Security Notice -->
        <div class="bg-blue-900/20 border border-blue-600 rounded-lg p-4 mb-8">
            <div class="flex items-start">
                <svg class="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-sm text-blue-300">
                    <p class="font-medium mb-1">Security Feature</p>
                    <p>Session expiration is a security feature that protects your account from unauthorized access. Your data remains safe.</p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-4">
            <!-- Refresh Page -->
            <button onclick="window.location.reload()" 
                    class="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Page
            </button>
            
            @auth
                <!-- For authenticated users -->
                <a href="{{ route('dashboard.index') }}" 
                   class="w-full inline-flex justify-center items-center px-6 py-3 border border-primary-600 text-base font-medium rounded-lg text-primary-400 bg-transparent hover:bg-primary-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z"></path>
                    </svg>
                    Go to Dashboard
                </a>
            @else
                <!-- For guests -->
                <a href="{{ route('login') }}" 
                   class="w-full inline-flex justify-center items-center px-6 py-3 border border-primary-600 text-base font-medium rounded-lg text-primary-400 bg-transparent hover:bg-primary-600 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                    </svg>
                    Sign In Again
                </a>
            @endauth
            
            <!-- Back Button -->
            <button onclick="history.back()" 
                    class="w-full inline-flex justify-center items-center px-6 py-3 border border-gray-600 text-base font-medium rounded-lg text-gray-300 bg-transparent hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Go Back
            </button>
        </div>

        <!-- Tips Section -->
        <div class="mt-12 pt-8 border-t border-gray-700">
            <h3 class="text-lg font-semibold text-white mb-4">💡 Tips to Avoid This</h3>
            <div class="text-left bg-gray-800 rounded-lg p-4">
                <ul class="space-y-2 text-gray-300 text-sm">
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">•</span>
                        Save your work frequently when filling out forms
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">•</span>
                        Complete forms in one session when possible
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">•</span>
                        Keep your browser tab active during long sessions
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-400 mr-2">•</span>
                        Log out properly when finished to maintain security
                    </li>
                </ul>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8">
            <p class="text-gray-400 text-sm mb-4">
                Need help? Our support team is available 24/7.
            </p>
            <div class="flex justify-center space-x-6 text-sm">
                <a href="mailto:<EMAIL>" class="text-primary-400 hover:text-primary-300 transition-colors duration-200">
                    Contact Support
                </a>
                <a href="{{ route('faq') }}" class="text-primary-400 hover:text-primary-300 transition-colors duration-200">
                    FAQ
                </a>
                <a href="{{ route('home') }}" class="text-primary-400 hover:text-primary-300 transition-colors duration-200">
                    Home
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Background Pattern -->
<div class="fixed inset-0 -z-10">
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black"></div>
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23374151" fill-opacity="0.1"%3E%3Ccircle cx="7" cy="7" r="1"/%3E%3Ccircle cx="27" cy="7" r="1"/%3E%3Ccircle cx="47" cy="7" r="1"/%3E%3Ccircle cx="7" cy="27" r="1"/%3E%3Ccircle cx="27" cy="27" r="1"/%3E%3Ccircle cx="47" cy="27" r="1"/%3E%3Ccircle cx="7" cy="47" r="1"/%3E%3Ccircle cx="27" cy="47" r="1"/%3E%3Ccircle cx="47" cy="47" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
</div>

<script>
// Auto-refresh after 10 seconds if user doesn't take action
setTimeout(function() {
    if (confirm('Would you like to refresh the page automatically?')) {
        window.location.reload();
    }
}, 10000);
</script>
@endsection
