<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $user = Auth::user();

        // Get subscription information
        $subscriptionInfo = [
            'current_plan' => $user->subscription_plan ?? 'free',
            'plan_features' => $this->getPlanFeatures($user->subscription_plan ?? 'free'),
            'billing_cycle' => 'monthly', // This would come from <PERSON><PERSON> in a real app
            'next_billing_date' => now()->addMonth(), // Mock data
            'can_cancel' => $user->subscription_plan !== 'free',
        ];

        // Get notification preferences (mock data for now)
        $notificationSettings = [
            'email_notifications' => true,
            'push_notifications' => true,
            'marketing_emails' => false,
            'event_reminders' => true,
            'lesson_updates' => true,
            'chat_mentions' => true,
        ];

        // Get privacy settings
        $privacySettings = [
            'profile_visibility' => 'public',
            'show_progress' => true,
            'show_badges' => true,
            'allow_messages' => true,
        ];

        return view('dashboard.settings.index', compact(
            'user', 'subscriptionInfo', 'notificationSettings', 'privacySettings'
        ));
    }

    /**
     * Update notification settings
     */
    public function updateNotifications(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'email_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'event_reminders' => 'boolean',
            'lesson_updates' => 'boolean',
            'chat_mentions' => 'boolean',
        ]);

        // In a real app, you'd store these in a user_settings table
        // For now, we'll just return success

        return back()->with('success', 'Notification settings updated successfully!');
    }

    /**
     * Update privacy settings
     */
    public function updatePrivacy(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'profile_visibility' => 'required|in:public,private,friends',
            'show_progress' => 'boolean',
            'show_badges' => 'boolean',
            'allow_messages' => 'boolean',
        ]);

        // In a real app, you'd store these in a user_settings table
        // For now, we'll just return success

        return back()->with('success', 'Privacy settings updated successfully!');
    }

    /**
     * Show subscription management
     */
    public function subscription()
    {
        $user = Auth::user();

        $plans = [
            'free' => [
                'name' => 'Free',
                'price' => 0,
                'features' => [
                    'Access to basic courses',
                    'Community chat access',
                    'Basic support',
                ],
                'limitations' => [
                    'Limited course access',
                    'No premium events',
                    'Basic features only',
                ]
            ],
            'prosper' => [
                'name' => 'Prosper',
                'price' => 49,
                'features' => [
                    'All Free features',
                    'Access to Prosper courses',
                    'Premium chat rooms',
                    'Priority support',
                    'Monthly live sessions',
                ],
                'limitations' => []
            ],
            'conquer' => [
                'name' => 'Conquer',
                'price' => 99,
                'features' => [
                    'All Prosper features',
                    'Access to Conquer courses',
                    'Advanced trading strategies',
                    'Weekly 1-on-1 sessions',
                    'Exclusive events',
                ],
                'limitations' => []
            ],
            'champions' => [
                'name' => 'Champions',
                'price' => 199,
                'features' => [
                    'All Conquer features',
                    'Direct access to mentors',
                    'Private Champions chat',
                    'Daily live sessions',
                    'Personal success coaching',
                    'Exclusive networking events',
                ],
                'limitations' => []
            ],
        ];

        $currentPlan = $user->subscription_plan ?? 'free';

        return view('dashboard.settings.subscription', compact('user', 'plans', 'currentPlan'));
    }

    /**
     * Show billing information
     */
    public function billing()
    {
        $user = Auth::user();

        // Mock billing data - in a real app this would come from Stripe
        $billingInfo = [
            'payment_method' => [
                'type' => 'card',
                'last_four' => '4242',
                'brand' => 'Visa',
                'exp_month' => 12,
                'exp_year' => 2025,
            ],
            'billing_address' => [
                'line1' => '123 Main St',
                'city' => 'New York',
                'state' => 'NY',
                'postal_code' => '10001',
                'country' => 'US',
            ],
            'invoices' => [
                [
                    'id' => 'inv_001',
                    'date' => now()->subMonth(),
                    'amount' => 99.00,
                    'status' => 'paid',
                    'plan' => 'Conquer',
                ],
                [
                    'id' => 'inv_002',
                    'date' => now()->subMonths(2),
                    'amount' => 99.00,
                    'status' => 'paid',
                    'plan' => 'Conquer',
                ],
            ]
        ];

        return view('dashboard.settings.billing', compact('user', 'billingInfo'));
    }

    /**
     * Delete user account
     */
    public function deleteAccount(Request $request)
    {
        $request->validate([
            'password' => 'required',
            'confirmation' => 'required|in:DELETE',
        ]);

        $user = Auth::user();

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'The password is incorrect.']);
        }

        // In a real app, you'd want to:
        // 1. Cancel any active subscriptions
        // 2. Clean up user data according to GDPR
        // 3. Send confirmation email
        // 4. Log the deletion for audit purposes

        // For now, we'll just deactivate the account
        $user->update(['is_active' => false]);
        Auth::logout();

        return redirect()->route('home')->with('success', 'Your account has been deactivated.');
    }

    /**
     * Get features for a subscription plan
     */
    private function getPlanFeatures($plan)
    {
        $features = [
            'free' => [
                'courses' => 'Basic courses only',
                'events' => 'Public events only',
                'chat' => 'General chat rooms',
                'support' => 'Community support',
            ],
            'prosper' => [
                'courses' => 'Prosper + Basic courses',
                'events' => 'Prosper events + Public',
                'chat' => 'Premium chat rooms',
                'support' => 'Priority support',
            ],
            'conquer' => [
                'courses' => 'Conquer + Prosper + Basic',
                'events' => 'All events access',
                'chat' => 'Elite chat rooms',
                'support' => '1-on-1 support',
            ],
            'champions' => [
                'courses' => 'All courses access',
                'events' => 'VIP event access',
                'chat' => 'Champions private chat',
                'support' => 'Personal coaching',
            ],
        ];

        return $features[$plan] ?? $features['free'];
    }

    /**
     * Update subscription preferences
     */
    public function updateSubscription(Request $request)
    {
        $request->validate([
            'billing_cycle' => 'required|in:monthly,yearly',
            'auto_renewal' => 'required|boolean',
            'email_receipts' => 'boolean',
            'renewal_reminders' => 'boolean',
        ]);

        $user = Auth::user();

        // In a real application, you would update Stripe subscription here
        // For now, we'll just flash a success message

        return redirect()->route('dashboard.settings.subscription')
            ->with('success', 'Subscription preferences updated successfully!');
    }
}
