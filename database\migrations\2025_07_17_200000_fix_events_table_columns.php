<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('events', function (Blueprint $table) {
            // Add missing columns that are referenced in the Event model
            if (!Schema::hasColumn('events', 'category')) {
                $table->string('category')->default('workshop')->after('description');
                $table->index('category');
            }
            
            if (!Schema::hasColumn('events', 'slug')) {
                $table->string('slug')->unique()->after('title');
                $table->index('slug');
            }
            
            if (!Schema::hasColumn('events', 'is_published')) {
                $table->boolean('is_published')->default(true)->after('is_premium');
                $table->index('is_published');
            }
            
            if (!Schema::hasColumn('events', 'meeting_url')) {
                $table->string('meeting_url')->nullable()->after('video_url');
            }
            
        });

        // Handle column renaming with MariaDB compatibility
        if (Schema::hasColumn('events', 'start_datetime') && !Schema::hasColumn('events', 'start_time')) {
            // Add new column
            Schema::table('events', function (Blueprint $table) {
                $table->timestamp('start_time')->nullable();
            });

            // Copy data
            DB::statement('UPDATE events SET start_time = start_datetime WHERE start_datetime IS NOT NULL');

            // Drop old column
            Schema::table('events', function (Blueprint $table) {
                $table->dropColumn('start_datetime');
            });
        }

        if (Schema::hasColumn('events', 'end_datetime') && !Schema::hasColumn('events', 'end_time')) {
            // Add new column
            Schema::table('events', function (Blueprint $table) {
                $table->timestamp('end_time')->nullable();
            });

            // Copy data
            DB::statement('UPDATE events SET end_time = end_datetime WHERE end_datetime IS NOT NULL');

            // Drop old column
            Schema::table('events', function (Blueprint $table) {
                $table->dropColumn('end_datetime');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse column renaming with MariaDB compatibility
        if (Schema::hasColumn('events', 'start_time')) {
            // Add old column back
            Schema::table('events', function (Blueprint $table) {
                $table->timestamp('start_datetime')->nullable();
            });

            // Copy data back
            DB::statement('UPDATE events SET start_datetime = start_time WHERE start_time IS NOT NULL');

            // Drop new column
            Schema::table('events', function (Blueprint $table) {
                $table->dropColumn('start_time');
            });
        }

        if (Schema::hasColumn('events', 'end_time')) {
            // Add old column back
            Schema::table('events', function (Blueprint $table) {
                $table->timestamp('end_datetime')->nullable();
            });

            // Copy data back
            DB::statement('UPDATE events SET end_datetime = end_time WHERE end_time IS NOT NULL');

            // Drop new column
            Schema::table('events', function (Blueprint $table) {
                $table->dropColumn('end_time');
            });
        }

        Schema::table('events', function (Blueprint $table) {
            $table->dropColumn(['category', 'slug', 'is_published', 'meeting_url']);
        });
    }
};
