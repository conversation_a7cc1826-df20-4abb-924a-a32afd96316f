@extends('layouts.admin')

@section('title', $user->name . ' - User Details')
@section('page-title', 'User Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">User Details</h1>
            <p class="text-gray-400 mt-1">{{ $user->name }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.users.edit', $user) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit User
            </a>
            <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Users
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- User Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">User Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Full Name</label>
                            <p class="text-white">{{ $user->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Email Address</label>
                            <p class="text-white">{{ $user->email }}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Phone Number</label>
                            <p class="text-white">{{ $user->phone ?? 'Not provided' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Campus</label>
                            @if($user->campus)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">{{ $user->campus->name }}</span>
                            @else
                                <p class="text-gray-400">No campus assigned</p>
                            @endif
                        </div>
                    </div>

                    @if($user->bio)
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Bio</label>
                        <p class="text-gray-300">{{ $user->bio }}</p>
                    </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Level</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Level {{ $user->level }}</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">XP Points</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">{{ $user->xp }} XP</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Coins</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">{{ $user->coins ?? 0 }} Coins</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            <div class="flex items-center space-x-2">
                                @if($user->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                @endif
                                @if($user->is_admin)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Admin</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Email Status</label>
                            @if($user->email_verified_at)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Verified</span>
                                <p class="text-xs text-gray-400 mt-1">{{ $user->email_verified_at->format('M j, Y') }}</p>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Unverified</span>
                            @endif
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Subscription</label>
                            @if(method_exists($user, 'subscribed') && $user->subscribed())
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">None</span>
                            @endif
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Member Since</label>
                            <p class="text-white">{{ $user->created_at->format('M j, Y') }}</p>
                            <p class="text-xs text-gray-400">{{ $user->created_at->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Activity -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Learning Progress</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Course Enrollments -->
                        <div>
                            <h4 class="text-md font-medium text-white mb-4">Course Enrollments ({{ $user->courseEnrollments()->count() }})</h4>
                            @if($user->courseEnrollments()->count() > 0)
                                <div class="space-y-3">
                                    @foreach($user->courseEnrollments()->with('course')->take(5)->get() as $enrollment)
                                    <div class="bg-gray-700 rounded-lg p-3">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="text-sm font-medium text-white">{{ $enrollment->course->title }}</p>
                                                <p class="text-xs text-gray-400">{{ $enrollment->progress_percentage }}% complete</p>
                                            </div>
                                            <div class="w-16 h-2 bg-gray-600 rounded-full">
                                                <div class="h-2 bg-primary-500 rounded-full" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                    @if($user->courseEnrollments()->count() > 5)
                                        <p class="text-xs text-gray-400 text-center">And {{ $user->courseEnrollments()->count() - 5 }} more...</p>
                                    @endif
                                </div>
                            @else
                                <p class="text-gray-400 text-sm">No course enrollments yet</p>
                            @endif
                        </div>

                        <!-- Recent Badges -->
                        <div>
                            <h4 class="text-md font-medium text-white mb-4">Badges Earned ({{ $user->badges()->count() }})</h4>
                            @if($user->badges()->count() > 0)
                                <div class="grid grid-cols-2 gap-3">
                                    @foreach($user->badges()->take(4)->get() as $badge)
                                    <div class="bg-gray-700 rounded-lg p-3 text-center">
                                        <div class="w-8 h-8 rounded-full mx-auto mb-2" style="background-color: {{ $badge->color ?? '#3B82F6' }}">
                                            @if($badge->icon_url)
                                                <img src="{{ $badge->icon_url }}" alt="{{ $badge->name }}" class="w-8 h-8 rounded-full">
                                            @else
                                                <div class="w-8 h-8 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                            @endif
                                        </div>
                                        <p class="text-xs font-medium text-white">{{ $badge->name }}</p>
                                    </div>
                                    @endforeach
                                </div>
                                @if($user->badges()->count() > 4)
                                    <p class="text-xs text-gray-400 text-center mt-3">And {{ $user->badges()->count() - 4 }} more...</p>
                                @endif
                            @else
                                <p class="text-gray-400 text-sm">No badges earned yet</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Recent Activity</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        @if($user->lessonProgress()->where('is_completed', true)->count() > 0)
                            @foreach($user->lessonProgress()->with('lesson')->where('is_completed', true)->latest()->take(5)->get() as $completion)
                            <div class="flex items-center space-x-3">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-white">Completed lesson: {{ $completion->lesson->title }}</p>
                                    <p class="text-sm text-gray-400">{{ $completion->updated_at->diffForHumans() }}</p>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <p class="text-gray-400 text-sm">No recent activity</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- User Avatar & Quick Info -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Profile</h3>
                </div>
                <div class="p-6">
                    <div class="text-center">
                        @if($user->avatar_url)
                            <img src="{{ $user->avatar_url }}" alt="{{ $user->name }}" class="w-20 h-20 rounded-full mx-auto mb-4 object-cover">
                        @else
                            <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <span class="text-2xl font-bold text-white">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                        @endif
                        <h4 class="text-lg font-medium text-white mb-1">{{ $user->name }}</h4>
                        <p class="text-sm text-gray-400 mb-4">{{ $user->email }}</p>
                        
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-xl font-bold text-primary-400">{{ $user->level }}</div>
                                <div class="text-xs text-gray-400">Level</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-green-400">{{ $user->xp }}</div>
                                <div class="text-xs text-gray-400">XP</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-blue-400">{{ $user->courseEnrollments()->count() }}</div>
                            <div class="text-sm text-gray-400">Enrollments</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400">{{ $user->lessonProgress()->where('is_completed', true)->count() }}</div>
                            <div class="text-sm text-gray-400">Completed</div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-center mt-4">
                        <div>
                            <div class="text-2xl font-bold text-purple-400">{{ $user->badges()->count() }}</div>
                            <div class="text-sm text-gray-400">Badges</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-yellow-400">{{ $user->userTasks()->where('is_completed', true)->count() }}</div>
                            <div class="text-sm text-gray-400">Tasks Done</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="{{ route('admin.users.edit', $user) }}" class="w-full btn-primary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit User
                    </a>
                    
                    <form action="{{ route('admin.users.toggle-status', $user) }}" method="POST" class="w-full">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="w-full {{ $user->is_active ? 'btn-outline' : 'btn-secondary' }} text-center">
                            @if($user->is_active)
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                </svg>
                                Deactivate User
                            @else
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Activate User
                            @endif
                        </button>
                    </form>

                    @if(!$user->email_verified_at)
                    <button class="w-full btn-secondary text-center" onclick="alert('Email verification feature coming soon!')">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        Send Verification
                    </button>
                    @endif

                    <button class="w-full btn-outline text-center" onclick="alert('Password reset feature coming soon!')">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                        </svg>
                        Reset Password
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
