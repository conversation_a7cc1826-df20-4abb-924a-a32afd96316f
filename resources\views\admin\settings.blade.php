@extends('layouts.admin')

@section('title', 'Settings')

@section('content')
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gradient">System Settings</h1>
    <p class="text-gray-400 mt-2">Configure application settings and preferences</p>
</div>

<!-- Settings Sections -->
<div class="space-y-6">
    <!-- Application Settings -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Application Settings
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Application Name</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['app_settings']['name'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Application URL</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['app_settings']['url'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Timezone</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['app_settings']['timezone'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Debug Mode</label>
                <div class="bg-gray-700 rounded-lg p-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $settings['app_settings']['debug'] ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800' }}">
                        {{ $settings['app_settings']['debug'] ? 'Enabled' : 'Disabled' }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Mail Settings -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Mail Configuration
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Mail Driver</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['mail_settings']['driver'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">SMTP Host</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['mail_settings']['host'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">SMTP Port</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['mail_settings']['port'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Status</label>
                <div class="bg-gray-700 rounded-lg p-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $settings['mail_settings']['host'] !== 'Not configured' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ $settings['mail_settings']['host'] !== 'Not configured' ? 'Configured' : 'Not Configured' }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Cache Settings -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
            Cache Configuration
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Cache Driver</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['cache_settings']['driver'] }}</div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-400 mb-2">Cache Prefix</label>
                <div class="bg-gray-700 rounded-lg p-3 text-white">{{ $settings['cache_settings']['prefix'] ?: 'None' }}</div>
            </div>
        </div>
    </div>

    <!-- System Actions -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
            </svg>
            System Maintenance
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span class="font-medium">Clear Cache</span>
                <p class="text-xs text-gray-300 mt-1">Clear all application cache</p>
            </button>

            <button class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
                <span class="font-medium">Optimize</span>
                <p class="text-xs text-gray-300 mt-1">Optimize application</p>
            </button>

            <button class="bg-yellow-600 hover:bg-yellow-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="font-medium">View Logs</span>
                <p class="text-xs text-gray-300 mt-1">Check system logs</p>
            </button>
        </div>
    </div>

    <!-- Security Settings -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Security Settings
        </h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                    <h4 class="font-medium text-white">Two-Factor Authentication</h4>
                    <p class="text-sm text-gray-400">Require 2FA for admin accounts</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                    <h4 class="font-medium text-white">Rate Limiting</h4>
                    <p class="text-sm text-gray-400">Enable API rate limiting</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
            </div>

            <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                <div>
                    <h4 class="font-medium text-white">Security Monitoring</h4>
                    <p class="text-sm text-gray-400">Monitor suspicious activities</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-red-600"></div>
                </label>
            </div>
        </div>
    </div>
</div>
@endsection
