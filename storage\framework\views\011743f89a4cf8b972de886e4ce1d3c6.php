<?php $__env->startSection('title', 'Analytics Dashboard'); ?>
<?php $__env->startSection('page-title', 'Analytics Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-white">Analytics Dashboard</h1>
        <p class="text-gray-400 mt-1">Monitor your platform's performance and user engagement</p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Users</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['total_users'])); ?></p>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Active Users (30d)</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['active_users'])); ?></p>
                </div>
            </div>
        </div>

        <!-- New Users This Month -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">New This Month</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['new_users_this_month'])); ?></p>
                </div>
            </div>
        </div>

        <!-- Total Courses -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Courses</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['total_courses'])); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Navigation -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <a href="<?php echo e(route('admin.analytics.users')); ?>" 
           class="bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors group">
            <div class="flex items-center">
                <div class="p-3 bg-blue-600 rounded-lg group-hover:bg-blue-500 transition-colors">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-white">User Analytics</h3>
                    <p class="text-gray-400 text-sm">Registration trends, activity patterns</p>
                </div>
            </div>
        </a>

        <a href="<?php echo e(route('admin.analytics.revenue')); ?>" 
           class="bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors group">
            <div class="flex items-center">
                <div class="p-3 bg-green-600 rounded-lg group-hover:bg-green-500 transition-colors">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-white">Revenue Analytics</h3>
                    <p class="text-gray-400 text-sm">Subscription metrics, revenue trends</p>
                </div>
            </div>
        </a>

        <a href="<?php echo e(route('admin.analytics.engagement')); ?>" 
           class="bg-gray-800 hover:bg-gray-700 rounded-lg p-6 transition-colors group">
            <div class="flex items-center">
                <div class="p-3 bg-purple-600 rounded-lg group-hover:bg-purple-500 transition-colors">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-white">Engagement Analytics</h3>
                    <p class="text-gray-400 text-sm">Course completion, user activity</p>
                </div>
            </div>
        </a>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Popular Campuses -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Popular Campuses</h3>
            </div>
            <div class="p-6">
                <?php $__empty_1 = true; $__currentLoopData = $popularCampuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center justify-between py-3 <?php echo e(!$loop->last ? 'border-b border-gray-700' : ''); ?>">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm"><?php echo e($campus->icon ?? substr($campus->name, 0, 1)); ?></span>
                            </div>
                            <div>
                                <p class="text-white font-medium"><?php echo e($campus->name); ?></p>
                                <p class="text-gray-400 text-sm"><?php echo e($campus->users_count); ?> students</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="w-16 bg-gray-700 rounded-full h-2">
                                <div class="bg-primary-600 h-2 rounded-full"
                                     style="width: <?php echo e($popularCampuses->max('users_count') > 0 ? min(100, ($campus->users_count / $popularCampuses->max('users_count')) * 100) : 0); ?>%"></div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-gray-400 text-center py-8">No campus data available</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Recent Users</h3>
            </div>
            <div class="p-6">
                <?php $__empty_1 = true; $__currentLoopData = $recentUsers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="flex items-center py-3 <?php echo e(!$loop->last ? 'border-b border-gray-700' : ''); ?>">
                        <div class="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mr-3">
                            <span class="text-white font-bold text-sm"><?php echo e(substr($user->name, 0, 1)); ?></span>
                        </div>
                        <div class="flex-1">
                            <p class="text-white font-medium"><?php echo e($user->name); ?></p>
                            <p class="text-gray-400 text-sm"><?php echo e($user->email); ?></p>
                        </div>
                        <div class="text-right">
                            <p class="text-gray-400 text-xs"><?php echo e($user->created_at->diffForHumans()); ?></p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                <?php echo e($user->subscription_plan === 'free' || !$user->subscription_plan ? 'bg-gray-700 text-gray-300' : 'bg-primary-600 text-white'); ?>">
                                <?php echo e(ucfirst($user->subscription_plan ?? 'free')); ?>

                            </span>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-gray-400 text-center py-8">No recent users</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/analytics/index.blade.php ENDPATH**/ ?>