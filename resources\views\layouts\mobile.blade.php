<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <title>@yield('title', 'The Real World')</title>
    <meta name="description" content="@yield('description', 'The Real World - Escape The Matrix')">

    <!-- PWA Meta Tags -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#f59e0b">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=montserrat:400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    @stack('styles')
    
    <style>
        /* Mobile-specific styles */
        .mobile-safe-area {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }
        
        .mobile-header {
            height: calc(4rem + env(safe-area-inset-top));
            padding-top: env(safe-area-inset-top);
        }
        
        .mobile-bottom-nav {
            height: calc(4rem + env(safe-area-inset-bottom));
            padding-bottom: env(safe-area-inset-bottom);
        }
        
        /* Prevent zoom on input focus */
        input, select, textarea {
            font-size: 16px;
        }
        
        /* Touch-friendly buttons */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-900 text-white overflow-x-hidden">
    <div class="min-h-screen flex flex-col" x-data="{ bottomNavOpen: false }">
        <!-- Mobile Header -->
        <header class="mobile-header bg-gray-800 shadow-sm border-b border-gray-700 fixed top-0 left-0 right-0 z-50">
            <div class="flex items-center justify-between h-16 px-4">
                <!-- Left Action -->
                <div class="flex items-center">
                    @hasSection('header-left')
                        @yield('header-left')
                    @else
                        <button onclick="history.back()" class="touch-target flex items-center justify-center text-gray-400 hover:text-white">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                        </button>
                    @endif
                </div>

                <!-- Title -->
                <div class="flex-1 text-center">
                    <h1 class="text-lg font-semibold text-white truncate px-4">
                        @yield('page-title', 'The Real World')
                    </h1>
                </div>

                <!-- Right Action -->
                <div class="flex items-center">
                    @hasSection('header-right')
                        @yield('header-right')
                    @else
                        <!-- Notifications -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="touch-target flex items-center justify-center text-gray-400 hover:text-white relative">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-7a9 9 0 0118 0v7h6"></path>
                                </svg>
                                @auth
                                    @php
                                        $unreadCount = auth()->user()->unreadNotifications()->count();
                                    @endphp
                                    @if($unreadCount > 0)
                                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                            {{ $unreadCount > 9 ? '9+' : $unreadCount }}
                                        </span>
                                    @endif
                                @endauth
                            </button>
                            
                            <!-- Mobile Notification Dropdown -->
                            <div x-show="open" 
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="transform opacity-100 scale-100"
                                 x-transition:leave-end="transform opacity-0 scale-95"
                                 @click.away="open = false"
                                 class="absolute right-0 mt-2 w-80 max-w-[calc(100vw-2rem)] bg-gray-800 rounded-lg shadow-lg border border-gray-700 z-50">
                                
                                <div class="p-4 border-b border-gray-700">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-sm font-semibold">Notifications</h3>
                                        @auth
                                            @if($unreadCount > 0)
                                                <button onclick="markAllAsRead()" class="text-xs text-primary-400 hover:text-primary-300">
                                                    Mark all read
                                                </button>
                                            @endif
                                        @endauth
                                    </div>
                                </div>
                                
                                <div class="max-h-64 overflow-y-auto">
                                    @auth
                                        @forelse(auth()->user()->notifications()->take(3)->get() as $notification)
                                            <div class="p-4 border-b border-gray-700 {{ $notification->read_at ? '' : 'bg-gray-750' }}">
                                                <div class="flex items-start">
                                                    <div class="text-xl mr-3">{{ $notification->data['icon'] ?? '🔔' }}</div>
                                                    <div class="flex-1">
                                                        <p class="text-sm font-medium">{{ $notification->data['title'] ?? 'Notification' }}</p>
                                                        <p class="text-xs text-gray-400 mt-1">{{ $notification->created_at->diffForHumans() }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="p-6 text-center">
                                                <div class="text-3xl mb-2">🔔</div>
                                                <p class="text-gray-400 text-sm">No notifications</p>
                                            </div>
                                        @endforelse
                                        
                                        @if(auth()->user()->notifications()->count() > 3)
                                            <div class="p-4">
                                                <a href="{{ route('dashboard.notifications.index') }}" class="block text-center text-sm text-primary-400">
                                                    View all
                                                </a>
                                            </div>
                                        @endif
                                    @else
                                        <div class="p-6 text-center">
                                            <p class="text-gray-400 text-sm">Please log in to view notifications</p>
                                        </div>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 pt-16 pb-20 overflow-y-auto">
            @yield('content')
        </main>

        <!-- Mobile Bottom Navigation -->
        @auth
            <nav class="mobile-bottom-nav bg-gray-800 border-t border-gray-700 fixed bottom-0 left-0 right-0 z-50">
                <div class="flex items-center justify-around h-16">
                    <!-- Dashboard -->
                    <a href="{{ route('dashboard.index') }}" 
                       class="flex flex-col items-center justify-center touch-target {{ request()->routeIs('dashboard.index') ? 'text-primary-400' : 'text-gray-400' }}">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        <span class="text-xs mt-1">Home</span>
                    </a>

                    <!-- Campuses -->
                    <a href="{{ route('dashboard.campuses') }}" 
                       class="flex flex-col items-center justify-center touch-target {{ request()->routeIs('dashboard.campuses*') ? 'text-primary-400' : 'text-gray-400' }}">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <span class="text-xs mt-1">Learn</span>
                    </a>

                    <!-- Events -->
                    <a href="{{ route('dashboard.events.index') }}" 
                       class="flex flex-col items-center justify-center touch-target {{ request()->routeIs('dashboard.events*') ? 'text-primary-400' : 'text-gray-400' }}">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="text-xs mt-1">Events</span>
                    </a>

                    <!-- Chat -->
                    <a href="{{ route('dashboard.chat') }}" 
                       class="flex flex-col items-center justify-center touch-target {{ request()->routeIs('dashboard.chat*') ? 'text-primary-400' : 'text-gray-400' }}">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span class="text-xs mt-1">Chat</span>
                    </a>

                    <!-- Profile -->
                    <a href="{{ route('dashboard.profile.show') }}" 
                       class="flex flex-col items-center justify-center touch-target {{ request()->routeIs('dashboard.profile*') ? 'text-primary-400' : 'text-gray-400' }}">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span class="text-xs mt-1">Profile</span>
                    </a>
                </div>
            </nav>
        @endauth
    </div>

    @stack('scripts')
    
    <script>
        // Mark all notifications as read
        function markAllAsRead() {
            fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            }).then(response => {
                if (response.ok) {
                    location.reload();
                }
            });
        }

        // Prevent zoom on input focus (iOS Safari)
        document.addEventListener('touchstart', function() {}, true);
        
        // Handle viewport height changes (mobile keyboard)
        function setViewportHeight() {
            let vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        window.addEventListener('resize', setViewportHeight);
        setViewportHeight();
        
        // PWA install prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install button if needed
            const installButton = document.getElementById('install-button');
            if (installButton) {
                installButton.style.display = 'block';
                installButton.addEventListener('click', () => {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((choiceResult) => {
                        if (choiceResult.outcome === 'accepted') {
                            console.log('User accepted the install prompt');
                        }
                        deferredPrompt = null;
                    });
                });
            }
        });
    </script>
</body>
</html>
