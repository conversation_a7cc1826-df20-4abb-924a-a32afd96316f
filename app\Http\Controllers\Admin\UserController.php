<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request)
    {
        $query = User::with('roles');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('role')) {
            $query->whereHas('roles', function($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        if ($request->filled('subscription')) {
            $query->where('subscription_plan', $request->subscription);
        }

        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Sort
        $sortBy = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortBy, $sortDirection);

        $users = $query->paginate(20);

        // Get filter options
        $roles = Role::all();
        $subscriptionPlans = ['free', 'prosper', 'conquer', 'champions'];

        return view('admin.users.index', compact('users', 'roles', 'subscriptionPlans'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $roles = Role::all();
        $subscriptionPlans = ['free', 'prosper', 'conquer', 'champions'];

        return view('admin.users.create', compact('roles', 'subscriptionPlans'));
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'subscription_plan' => 'required|in:free,prosper,conquer,champions',
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'subscription_plan' => $request->subscription_plan,
            'email_verified_at' => now(),
            'is_active' => true,
        ]);

        // Assign roles
        if ($request->filled('roles')) {
            $user->assignRole($request->roles);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load(['roles', 'userLessonProgress.lesson.course', 'eventRsvps.event']);

        // Get user statistics
        $stats = [
            'total_xp' => $user->xp,
            'current_level' => $user->level,
            'lessons_completed' => $user->userLessonProgress()->where('is_completed', true)->count(),
            'courses_started' => $user->userLessonProgress()
                ->join('lessons', 'user_lesson_progress.lesson_id', '=', 'lessons.id')
                ->distinct('lessons.course_id')
                ->count('lessons.course_id'),
            'events_attended' => $user->eventRsvps()->where('rsvp_status', 'attended')->count(),
            'messages_sent' => $user->chatMessages()->count(),
            'tasks_completed' => $user->userTasks()->count(),
        ];

        // Get recent activity
        $recentActivity = collect();

        // Recent lesson completions
        $recentLessons = $user->userLessonProgress()
            ->where('is_completed', true)
            ->with('lesson')
            ->latest('completed_at')
            ->take(5)
            ->get();

        foreach ($recentLessons as $progress) {
            $recentActivity->push([
                'type' => 'lesson_completed',
                'description' => 'Completed lesson: ' . $progress->lesson->title,
                'date' => $progress->completed_at,
            ]);
        }

        // Recent event RSVPs
        $recentRsvps = $user->eventRsvps()
            ->with('event')
            ->latest('rsvp_at')
            ->take(5)
            ->get();

        foreach ($recentRsvps as $rsvp) {
            $recentActivity->push([
                'type' => 'event_rsvp',
                'description' => 'RSVP\'d to event: ' . $rsvp->event->title,
                'date' => $rsvp->rsvp_at,
            ]);
        }

        $recentActivity = $recentActivity->sortByDesc('date')->take(10);

        return view('admin.users.show', compact('user', 'stats', 'recentActivity'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $subscriptionPlans = ['free', 'prosper', 'conquer', 'champions'];
        $userRoles = $user->roles->pluck('name')->toArray();

        return view('admin.users.edit', compact('user', 'roles', 'subscriptionPlans', 'userRoles'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'subscription_plan' => 'required|in:free,prosper,conquer,champions',
            'is_active' => 'boolean',
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'subscription_plan' => $request->subscription_plan,
            'is_active' => $request->boolean('is_active'),
        ];

        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($request->password);
        }

        $user->update($updateData);

        // Update roles
        $user->syncRoles($request->roles ?? []);

        return redirect()->route('admin.users.show', $user)
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user)
    {
        // Prevent deleting the current admin user
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user active status
     */
    public function toggleStatus(User $user)
    {
        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';

        return redirect()->back()
            ->with('success', "User {$status} successfully.");
    }
}
