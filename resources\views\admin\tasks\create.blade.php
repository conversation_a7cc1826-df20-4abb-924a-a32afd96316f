@extends('layouts.admin')

@section('title', 'Create Task')
@section('page-title', 'Create Task')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Create New Task</h1>
            <p class="text-gray-400 mt-1">Add a new task for users to complete</p>
        </div>
        <a href="{{ route('admin.tasks.index') }}" class="btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Tasks
        </a>
    </div>

    <form action="{{ route('admin.tasks.store') }}" method="POST" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        @csrf
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Task Title *</label>
                        <input type="text" name="title" id="title" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('title') }}" required placeholder="Enter task title">
                        @error('title')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                        <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required placeholder="Detailed description of what needs to be done">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="type" class="block text-sm font-medium text-gray-300 mb-2">Task Type *</label>
                            <select name="type" id="type" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required>
                                <option value="">Select task type</option>
                                <option value="daily" {{ old('type') === 'daily' ? 'selected' : '' }}>Daily Task</option>
                                <option value="weekly" {{ old('type') === 'weekly' ? 'selected' : '' }}>Weekly Task</option>
                                <option value="monthly" {{ old('type') === 'monthly' ? 'selected' : '' }}>Monthly Task</option>
                                <option value="one_time" {{ old('type') === 'one_time' ? 'selected' : '' }}>One-time Task</option>
                                <option value="milestone" {{ old('type') === 'milestone' ? 'selected' : '' }}>Milestone</option>
                            </select>
                            @error('type')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                            <select name="category" id="category" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select category</option>
                                <option value="learning" {{ old('category') === 'learning' ? 'selected' : '' }}>Learning</option>
                                <option value="social" {{ old('category') === 'social' ? 'selected' : '' }}>Social</option>
                                <option value="achievement" {{ old('category') === 'achievement' ? 'selected' : '' }}>Achievement</option>
                                <option value="engagement" {{ old('category') === 'engagement' ? 'selected' : '' }}>Engagement</option>
                                <option value="special" {{ old('category') === 'special' ? 'selected' : '' }}>Special</option>
                            </select>
                            @error('category')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Configuration -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Configuration</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="xp_reward" class="block text-sm font-medium text-gray-300 mb-2">XP Reward</label>
                            <input type="number" name="xp_reward" id="xp_reward" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('xp_reward', 0) }}" min="0" max="1000" placeholder="50">
                            <p class="mt-1 text-xs text-gray-400">XP points awarded for completion</p>
                            @error('xp_reward')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="difficulty" class="block text-sm font-medium text-gray-300 mb-2">Difficulty</label>
                            <select name="difficulty" id="difficulty" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select difficulty</option>
                                <option value="easy" {{ old('difficulty') === 'easy' ? 'selected' : '' }}>Easy</option>
                                <option value="medium" {{ old('difficulty') === 'medium' ? 'selected' : '' }}>Medium</option>
                                <option value="hard" {{ old('difficulty') === 'hard' ? 'selected' : '' }}>Hard</option>
                                <option value="expert" {{ old('difficulty') === 'expert' ? 'selected' : '' }}>Expert</option>
                            </select>
                            @error('difficulty')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="requirements" class="block text-sm font-medium text-gray-300 mb-2">Requirements (JSON)</label>
                        <textarea name="requirements" id="requirements" rows="4" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder='{"lessons_completed": 1, "courses_enrolled": 1}'>{{ old('requirements') }}</textarea>
                        <p class="mt-1 text-xs text-gray-400">JSON object defining task completion requirements</p>
                        @error('requirements')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Scheduling -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Scheduling</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-300 mb-2">Start Date</label>
                            <input type="datetime-local" name="start_date" id="start_date" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('start_date') }}">
                            <p class="mt-1 text-xs text-gray-400">When this task becomes available</p>
                            @error('start_date')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-300 mb-2">End Date</label>
                            <input type="datetime-local" name="end_date" id="end_date" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('end_date') }}">
                            <p class="mt-1 text-xs text-gray-400">When this task expires (optional)</p>
                            @error('end_date')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="max_completions" class="block text-sm font-medium text-gray-300 mb-2">Max Completions</label>
                            <input type="number" name="max_completions" id="max_completions" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('max_completions') }}" min="1" placeholder="Unlimited">
                            <p class="mt-1 text-xs text-gray-400">Maximum times this task can be completed</p>
                            @error('max_completions')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                            <input type="number" name="sort_order" id="sort_order" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('sort_order', 0) }}" min="0" placeholder="0">
                            <p class="mt-1 text-xs text-gray-400">Display order (lower numbers first)</p>
                            @error('sort_order')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Settings</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_active" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Active</span>
                                <p class="text-xs text-gray-400">Task is available to users</p>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_featured" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Featured Task</span>
                                <p class="text-xs text-gray-400">Highlight this task</p>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between">
                <a href="{{ route('admin.tasks.index') }}" class="btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Task
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Task Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Preview</h3>
                </div>
                <div class="p-6">
                    <div class="task-preview">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-lg bg-primary-600 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V7a2 2 0 00-2-2H9z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="task-title text-white font-medium">Task Title</h4>
                                <p class="task-type text-sm text-gray-400">Task Type</p>
                            </div>
                        </div>
                        <p class="task-description text-gray-300 text-sm mb-4">Task description will appear here</p>
                        <div class="flex items-center space-x-2">
                            <span class="task-xp inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">0 XP</span>
                            <span class="task-difficulty inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Difficulty</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guidelines -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Guidelines</h3>
                </div>
                <div class="p-6">
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Use clear, actionable titles
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Set appropriate XP rewards
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Define clear requirements
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Test requirements thoroughly
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const typeSelect = document.getElementById('type');
    const xpInput = document.getElementById('xp_reward');
    const difficultySelect = document.getElementById('difficulty');

    titleInput.addEventListener('input', function() {
        document.querySelector('.task-title').textContent = this.value || 'Task Title';
    });

    descriptionInput.addEventListener('input', function() {
        document.querySelector('.task-description').textContent = this.value || 'Task description will appear here';
    });

    typeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        document.querySelector('.task-type').textContent = selectedOption.text || 'Task Type';
    });

    xpInput.addEventListener('input', function() {
        document.querySelector('.task-xp').textContent = (this.value || 0) + ' XP';
    });

    difficultySelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        document.querySelector('.task-difficulty').textContent = selectedOption.text || 'Difficulty';
    });
});
</script>
@endpush
