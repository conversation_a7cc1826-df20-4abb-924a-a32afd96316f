<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Stripe\Stripe;
use Stripe\Price;
use App\Notifications\SubscriptionActivated;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        Stripe::setApiKey(config('cashier.secret'));
    }

    /**
     * Display subscription plans
     */
    public function plans()
    {
        $user = Auth::user();

        $plans = [
            'prosper' => [
                'name' => 'Prosper',
                'price' => 49,
                'price_id' => config('services.stripe.prosper_price_id', 'price_prosper'),
                'features' => [
                    'Access to Prosper courses',
                    'Premium chat rooms',
                    'Priority support',
                    'Monthly live sessions',
                    'Course certificates',
                    'Mobile app access',
                ],
                'popular' => false,
            ],
            'conquer' => [
                'name' => 'Conquer',
                'price' => 99,
                'price_id' => config('services.stripe.conquer_price_id', 'price_conquer'),
                'features' => [
                    'All Prosper features',
                    'Access to Conquer courses',
                    'Advanced trading strategies',
                    'Weekly 1-on-1 sessions',
                    'Exclusive events',
                    'Private Discord access',
                    'Advanced analytics',
                ],
                'popular' => true,
            ],
            'champions' => [
                'name' => 'Champions',
                'price' => 199,
                'price_id' => config('services.stripe.champions_price_id', 'price_champions'),
                'features' => [
                    'All Conquer features',
                    'Direct access to mentors',
                    'Private Champions chat',
                    'Daily live sessions',
                    'Personal success coaching',
                    'Exclusive networking events',
                    'Priority feature requests',
                    'Custom learning paths',
                ],
                'popular' => false,
            ],
        ];

        $currentPlan = $user->subscription_plan ?? 'free';
        $hasActiveSubscription = $user->subscribed('default');

        return view('dashboard.subscription.plans', compact('plans', 'currentPlan', 'hasActiveSubscription', 'user'));
    }

    /**
     * Create checkout session
     */
    public function checkout(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:prosper,conquer,champions',
        ]);

        $user = Auth::user();
        $plan = $request->plan;

        // Get plan configuration
        $plans = [
            'prosper' => [
                'price_id' => config('services.stripe.prosper_price_id', 'price_prosper'),
                'name' => 'Prosper Plan',
            ],
            'conquer' => [
                'price_id' => config('services.stripe.conquer_price_id', 'price_conquer'),
                'name' => 'Conquer Plan',
            ],
            'champions' => [
                'price_id' => config('services.stripe.champions_price_id', 'price_champions'),
                'name' => 'Champions Plan',
            ],
        ];

        $selectedPlan = $plans[$plan];

        try {
            // If user already has a subscription, redirect to change plan
            if ($user->subscribed('default')) {
                return $this->changePlan($request);
            }

            // Create new subscription checkout
            $checkout = $user->newSubscription('default', $selectedPlan['price_id'])
                ->checkout([
                    'success_url' => route('dashboard.subscription.success') . '?session_id={CHECKOUT_SESSION_ID}',
                    'cancel_url' => route('dashboard.subscription.cancel'),
                    'metadata' => [
                        'plan' => $plan,
                        'user_id' => $user->id,
                    ],
                ]);

            return redirect($checkout->url);

        } catch (IncompletePayment $exception) {
            return redirect()->route('cashier.payment', [$exception->payment->id, 'redirect' => route('dashboard.subscription.plans')]);
        } catch (\Exception $e) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'Unable to create checkout session. Please try again.');
        }
    }

    /**
     * Handle successful subscription
     */
    public function success(Request $request)
    {
        $user = Auth::user();

        // Update user subscription plan in our database
        if ($user->subscribed('default')) {
            $subscription = $user->subscription('default');
            $priceId = $subscription->stripe_price;

            // Map Stripe price ID to our plan names
            $planMapping = [
                config('services.stripe.prosper_price_id', 'price_prosper') => 'prosper',
                config('services.stripe.conquer_price_id', 'price_conquer') => 'conquer',
                config('services.stripe.champions_price_id', 'price_champions') => 'champions',
            ];

            $planName = $planMapping[$priceId] ?? 'prosper';

            $user->update([
                'subscription_plan' => $planName,
            ]);

            // Award XP for subscribing
            $user->addXp(100);

            // Send subscription activated notification
            $planFeatures = $this->getPlans()[$user->subscription_plan]['features'] ?? [];
            $user->notify(new SubscriptionActivated($user->subscription_plan, $planFeatures));
        }

        return view('dashboard.subscription.success', compact('user'));
    }

    /**
     * Handle cancelled subscription
     */
    public function cancel()
    {
        return view('dashboard.subscription.cancel');
    }

    /**
     * Change subscription plan
     */
    public function changePlan(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:prosper,conquer,champions',
        ]);

        $user = Auth::user();
        $plan = $request->plan;

        if (!$user->subscribed('default')) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'You do not have an active subscription.');
        }

        $plans = [
            'prosper' => config('services.stripe.prosper_price_id', 'price_prosper'),
            'conquer' => config('services.stripe.conquer_price_id', 'price_conquer'),
            'champions' => config('services.stripe.champions_price_id', 'price_champions'),
        ];

        try {
            $user->subscription('default')->swap($plans[$plan]);

            $user->update([
                'subscription_plan' => $plan,
            ]);

            return redirect()->route('dashboard.subscription.plans')
                ->with('success', 'Your subscription plan has been updated successfully!');

        } catch (\Exception $e) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'Unable to change subscription plan. Please try again.');
        }
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(Request $request)
    {
        $user = Auth::user();

        if (!$user->subscribed('default')) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'You do not have an active subscription.');
        }

        try {
            $user->subscription('default')->cancel();

            return redirect()->route('dashboard.subscription.plans')
                ->with('success', 'Your subscription has been cancelled. You will continue to have access until the end of your billing period.');

        } catch (\Exception $e) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'Unable to cancel subscription. Please contact support.');
        }
    }

    /**
     * Resume cancelled subscription
     */
    public function resumeSubscription(Request $request)
    {
        $user = Auth::user();

        if (!$user->subscription('default') || !$user->subscription('default')->cancelled()) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'No cancelled subscription found.');
        }

        try {
            $user->subscription('default')->resume();

            return redirect()->route('dashboard.subscription.plans')
                ->with('success', 'Your subscription has been resumed successfully!');

        } catch (\Exception $e) {
            return redirect()->route('dashboard.subscription.plans')
                ->with('error', 'Unable to resume subscription. Please contact support.');
        }
    }

    /**
     * Download invoice
     */
    public function downloadInvoice(Request $request, $invoiceId)
    {
        $user = Auth::user();

        try {
            return $user->downloadInvoice($invoiceId, [
                'vendor' => 'The Real World',
                'product' => 'Subscription',
            ]);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Unable to download invoice.');
        }
    }
}
