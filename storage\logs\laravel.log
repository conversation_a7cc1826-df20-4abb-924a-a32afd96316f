[2025-07-18 20:41:40] local.ERROR: Route [dashboard.leaderboard.index] not defined. {"view":{"view":"C:\\Users\\<USER>\\Desktop\\LMS\\resources\\views\\errors\\404.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-787914879 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1577</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-787914879\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","exception":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException</span> {<a class=sf-dump-ref>#1569</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"48 characters\">No query results for model [App\\Models\\Campus] 1</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
  #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php
100 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Exceptions\\Handler.php</span></span>\"
  #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>487</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Exception`\">previous</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\ModelNotFoundException
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ModelNotFoundException</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21571 title=\"4 occurrences\">#1571</a><samp data-depth=2 id=sf-dump-*********-ref21571 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"48 characters\">No query results for model [App\\Models\\Campus] 1</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
    #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php
99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\ImplicitRouteBinding.php</span></span>\"
    #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>62</span>
    #<span class=sf-dump-protected title=\"Protected property\">model</span>: \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Campus</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">ids</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"
    </samp>]
    <span class=sf-dump-meta>trace</span>: {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php:62
Stack level 47.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\ImplicitRouteBinding.php:62</span></span> { &#8230;4}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:959
Stack level 46.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:959</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;Illuminate\\Routing\\{closure}()
Stack level 45.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing\\Router-&gt;Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}()</span></span> {}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:961
Stack level 44.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:961</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:41
Stack level 43.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:41</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 42.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57
Stack level 41.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 40.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78
Stack level 39.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 38.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49
Stack level 37.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 36.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121
Stack level 35.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:121</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64
Stack level 34.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:64</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 33.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37
Stack level 32.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:805
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:805</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:784
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:784</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:748
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:748</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:737
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:737</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:200
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:200</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:144
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:144</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php:16
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">app\\Http\\Middleware\\SecurityHeaders.php:16</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php:49
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\HandleCors.php:49</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:175
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:175</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php:51
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">public\\index.php:51</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php:16
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\resources\\server.php:16</span></span> { &#8230;4}
    </samp>}
  </samp>}
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">statusCode</span>: <span class=sf-dump-num>404</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">headers</span>: []
  <span class=sf-dump-meta>trace</span>: {<samp data-depth=2 class=sf-dump-compact>
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:487
Stack level 45.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Exceptions\\Handler.php:487</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareException(Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Handler-&gt;prepareException(Throwable $e)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$e instanceof BackedEnumCaseNotFoundException =&gt; new NotFoundHttpException($e-&gt;getMessage(), $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareException(Throwable $e)\">$e instanceof ModelNotFoundException =&gt; new NotFoundHttpException($e-&gt;getMessage(), $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>$e instanceof AuthorizationException &amp;&amp; $e-&gt;hasStatus() =&gt; new HttpException(</span></code>
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:463
Stack level 44.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Exceptions\\Handler.php:463</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Handler-&gt;render($request, Throwable $e)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)\">$e = $this-&gt;prepareException($e);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php:51
Stack level 43.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Pipeline.php:51</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Pipeline-&gt;handleException($passable, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pipeline-&gt;handleException($passable, Throwable $e)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Pipeline-&gt;handleException($passable, Throwable $e)\">$response = $handler-&gt;render($passable, $e);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:188
Stack level 42.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:188</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">    return $this-&gt;handleException($passable, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57
Stack level 41.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Auth\\Middleware\\Authenticate-&gt;handle($request, Closure $next, ...$guards)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Authenticate-&gt;handle($request, Closure $next, ...$guards)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Auth\\Middleware\\Authenticate-&gt;handle($request, Closure $next, ...$guards)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 40.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78
Stack level 39.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">VerifyCsrfToken-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken-&gt;handle($request, Closure $next)\">    return tap($next($request), function ($response) use ($request) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>        if ($this-&gt;shouldAddXsrfTokenCookie()) {</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 38.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49
Stack level 37.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Middleware\\ShareErrorsFromSession-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShareErrorsFromSession-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\View\\Middleware\\ShareErrorsFromSession-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 36.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121
Stack level 35.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:121</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Middleware\\StartSession-&gt;handleStatefulRequest(Request $request, $session, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">StartSession-&gt;handleStatefulRequest(Request $request, $session, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Session\\Middleware\\StartSession-&gt;handleStatefulRequest(Request $request, $session, Closure $next)\">$response = $next($request);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64
Stack level 34.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:64</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Middleware\\StartSession-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">StartSession-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Session\\Middleware\\StartSession-&gt;handle($request, Closure $next)\">    return $this-&gt;handleStatefulRequest($request, $session, $next);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;3}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 33.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37
Stack level 32.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Cookie\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AddQueuedCookiesToResponse-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse-&gt;handle($request, Closure $next)\">    $response = $next($request);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Cookie\\Middleware\\EncryptCookies-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Cookie\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EncryptCookies-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Cookie\\Middleware\\EncryptCookies-&gt;handle($request, Closure $next)\">    return $this-&gt;encrypt($next($this-&gt;decrypt($request)));</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pipeline-&gt;then(Closure $destination)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\">    return $pipeline($this-&gt;passable);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:805
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:805</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;runRouteWithinStack(Route $route, Request $request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;runRouteWithinStack(Route $route, Request $request)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>-&gt;through($middleware)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;runRouteWithinStack(Route $route, Request $request)\">-&gt;then(fn ($request) =&gt; $this-&gt;prepareResponse(</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request, $route-&gt;run()</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:784
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:784</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;runRoute(Request $request, Route $route)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;runRoute(Request $request, Route $route)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>return $this-&gt;prepareResponse($request,</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;runRoute(Request $request, Route $route)\">    $this-&gt;runRouteWithinStack($route, $request)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:748
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:748</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;dispatchToRoute(Request $request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;dispatchToRoute(Request $request)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;dispatchToRoute(Request $request)\">    return $this-&gt;runRoute($request, $this-&gt;findRoute($request));</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:737
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:737</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;dispatch(Request $request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;dispatch(Request $request)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;dispatch(Request $request)\">    return $this-&gt;dispatchToRoute($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:200
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:200</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http\\{closure}\">    return $this-&gt;router-&gt;dispatch($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>};</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:144
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:144</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>try {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">    return $destination($passable);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php:16
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">app\\Http\\Middleware\\SecurityHeaders.php:16</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Http\\Middleware\\SecurityHeaders-&gt;handle(Request $request, Closure $next): Response
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">SecurityHeaders-&gt;handle(Request $request, Closure $next): Response</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in App\\Http\\Middleware\\SecurityHeaders-&gt;handle(Request $request, Closure $next): Response\">    $response = $next($request);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TransformsRequest-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)\">    return parent::handle($request, $next);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TransformsRequest-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\TrimStrings-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TrimStrings-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TrimStrings-&gt;handle($request, Closure $next)\">    return parent::handle($request, $next);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ValidatePostSize-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php:49
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\HandleCors.php:49</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Http\\Middleware\\HandleCors-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HandleCors-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>if (! $this-&gt;hasMatchingPath($request)) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Http\\Middleware\\HandleCors-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Http\\Middleware\\TrustProxies-&gt;handle(Request $request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TrustProxies-&gt;handle(Request $request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Http\\Middleware\\TrustProxies-&gt;handle(Request $request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pipeline-&gt;then(Closure $destination)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\">    return $pipeline($this-&gt;passable);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:175
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:175</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;sendRequestThroughRouter($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Kernel-&gt;sendRequestThroughRouter($request)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>                -&gt;through($this-&gt;app-&gt;shouldSkipMiddleware() ? [] : $this-&gt;middleware)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;sendRequestThroughRouter($request)\">                -&gt;then($this-&gt;dispatchToRouter());</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Kernel-&gt;handle($request)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\">    $response = $this-&gt;sendRequestThroughRouter($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php:51
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">public\\index.php:51</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note>require_once</span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in require_once\">$response = $kernel-&gt;handle(</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request = Request::capture()</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php:16
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\resources\\server.php:16</span></span> {<samp data-depth=3 class=sf-dump-compact>
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const>require_once $publicPath.&#039;/index.php&#039;;</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [dashboard.leaderboard.index] not defined. at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard.leade...', Array, true)
#1 C:\\Users\\<USER>\\Desktop\\LMS\\resources\\views\\errors\\404.blade.php(105): route('dashboard.leade...')
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 404, Array)
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 404, Array)
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::404', Array, 404, Array)
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#15 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Database\\Eloquent\\ModelNotFoundException))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#60 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard.leaderboard.index] not defined. at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard.leade...', Array, true)
#1 C:\\Users\\<USER>\\Desktop\\LMS\\storage\\framework\\views\\415863571215088524be0b912c95e864.php(103): route('dashboard.leade...')
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 404, Array)
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 404, Array)
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::404', Array, 404, Array)
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#15 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Database\\Eloquent\\ModelNotFoundException))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#60 {main}
"} 
[2025-07-18 20:44:52] local.ERROR: Route [dashboard.leaderboard.index] not defined. {"view":{"view":"C:\\Users\\<USER>\\Desktop\\LMS\\resources\\views\\errors\\404.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-334152862 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1577</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-334152862\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","exception":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException</span> {<a class=sf-dump-ref>#1569</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"48 characters\">No query results for model [App\\Models\\Campus] 1</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
  #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php
100 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Exceptions\\Handler.php</span></span>\"
  #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>487</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Exception`\">previous</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\ModelNotFoundException
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ModelNotFoundException</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref21571 title=\"4 occurrences\">#1571</a><samp data-depth=2 id=sf-dump-*********-ref21571 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">message</span>: \"<span class=sf-dump-str title=\"48 characters\">No query results for model [App\\Models\\Campus] 1</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">code</span>: <span class=sf-dump-num>0</span>
    #<span class=sf-dump-protected title=\"Protected property\">file</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php
99 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\ImplicitRouteBinding.php</span></span>\"
    #<span class=sf-dump-protected title=\"Protected property\">line</span>: <span class=sf-dump-num>62</span>
    #<span class=sf-dump-protected title=\"Protected property\">model</span>: \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Campus</span>\"
    #<span class=sf-dump-protected title=\"Protected property\">ids</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"
    </samp>]
    <span class=sf-dump-meta>trace</span>: {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php:62
Stack level 47.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\ImplicitRouteBinding.php:62</span></span> { &#8230;4}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:959
Stack level 46.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:959</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;Illuminate\\Routing\\{closure}()
Stack level 45.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Illuminate\\Routing\\Router-&gt;Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}()</span></span> {}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:961
Stack level 44.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:961</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:41
Stack level 43.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:41</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 42.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57
Stack level 41.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 40.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78
Stack level 39.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 38.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49
Stack level 37.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 36.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121
Stack level 35.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:121</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64
Stack level 34.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:64</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 33.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37
Stack level 32.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:805
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:805</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:784
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:784</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:748
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:748</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:737
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:737</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:200
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:200</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:144
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:144</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php:16
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">app\\Http\\Middleware\\SecurityHeaders.php:16</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php:49
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\HandleCors.php:49</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:175
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:175</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php:51
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">public\\index.php:51</span></span> { &#8230;5}
      <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php:16
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\resources\\server.php:16</span></span> { &#8230;4}
    </samp>}
  </samp>}
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">statusCode</span>: <span class=sf-dump-num>404</span>
  -<span class=sf-dump-private title=\"Private property defined in class:&#10;`Symfony\\Component\\HttpKernel\\Exception\\HttpException`\">headers</span>: []
  <span class=sf-dump-meta>trace</span>: {<samp data-depth=2 class=sf-dump-compact>
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:487
Stack level 45.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Exceptions\\Handler.php:487</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareException(Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Handler-&gt;prepareException(Throwable $e)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$e instanceof BackedEnumCaseNotFoundException =&gt; new NotFoundHttpException($e-&gt;getMessage(), $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;prepareException(Throwable $e)\">$e instanceof ModelNotFoundException =&gt; new NotFoundHttpException($e-&gt;getMessage(), $e),</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>$e instanceof AuthorizationException &amp;&amp; $e-&gt;hasStatus() =&gt; new HttpException(</span></code>
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php:463
Stack level 44.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Exceptions\\Handler.php:463</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Exceptions</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Handler-&gt;render($request, Throwable $e)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Exceptions\\Handler-&gt;render($request, Throwable $e)\">$e = $this-&gt;prepareException($e);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php:51
Stack level 43.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Pipeline.php:51</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Pipeline-&gt;handleException($passable, Throwable $e)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pipeline-&gt;handleException($passable, Throwable $e)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Pipeline-&gt;handleException($passable, Throwable $e)\">$response = $handler-&gt;render($passable, $e);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:188
Stack level 42.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:188</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">    return $this-&gt;handleException($passable, $e);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57
Stack level 41.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Auth\\Middleware\\Authenticate.php:57</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Auth\\Middleware\\Authenticate-&gt;handle($request, Closure $next, ...$guards)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Auth\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Authenticate-&gt;handle($request, Closure $next, ...$guards)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Auth\\Middleware\\Authenticate-&gt;handle($request, Closure $next, ...$guards)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 40.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78
Stack level 39.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">VerifyCsrfToken-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken-&gt;handle($request, Closure $next)\">    return tap($next($request), function ($response) use ($request) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>        if ($this-&gt;shouldAddXsrfTokenCookie()) {</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 38.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49
Stack level 37.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Middleware\\ShareErrorsFromSession-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ShareErrorsFromSession-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\View\\Middleware\\ShareErrorsFromSession-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 36.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121
Stack level 35.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:121</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Middleware\\StartSession-&gt;handleStatefulRequest(Request $request, $session, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">StartSession-&gt;handleStatefulRequest(Request $request, $session, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Session\\Middleware\\StartSession-&gt;handleStatefulRequest(Request $request, $session, Closure $next)\">$response = $next($request);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64
Stack level 34.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Session\\Middleware\\StartSession.php:64</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Session\\Middleware\\StartSession-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Session\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">StartSession-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Session\\Middleware\\StartSession-&gt;handle($request, Closure $next)\">    return $this-&gt;handleStatefulRequest($request, $session, $next);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;3}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 33.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37
Stack level 32.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Cookie\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AddQueuedCookiesToResponse-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse-&gt;handle($request, Closure $next)\">    $response = $next($request);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 31.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67
Stack level 30.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Cookie\\Middleware\\EncryptCookies-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Cookie\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">EncryptCookies-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Cookie\\Middleware\\EncryptCookies-&gt;handle($request, Closure $next)\">    return $this-&gt;encrypt($next($this-&gt;decrypt($request)));</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 29.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 28.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pipeline-&gt;then(Closure $destination)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\">    return $pipeline($this-&gt;passable);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:805
Stack level 27.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:805</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;runRouteWithinStack(Route $route, Request $request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;runRouteWithinStack(Route $route, Request $request)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>-&gt;through($middleware)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;runRouteWithinStack(Route $route, Request $request)\">-&gt;then(fn ($request) =&gt; $this-&gt;prepareResponse(</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request, $route-&gt;run()</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:784
Stack level 26.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:784</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;runRoute(Request $request, Route $route)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;runRoute(Request $request, Route $route)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>return $this-&gt;prepareResponse($request,</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;runRoute(Request $request, Route $route)\">    $this-&gt;runRouteWithinStack($route, $request)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:748
Stack level 25.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:748</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;dispatchToRoute(Request $request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;dispatchToRoute(Request $request)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;dispatchToRoute(Request $request)\">    return $this-&gt;runRoute($request, $this-&gt;findRoute($request));</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:737
Stack level 24.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Routing\\Router.php:737</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Routing\\Router-&gt;dispatch(Request $request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Routing</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Router-&gt;dispatch(Request $request)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Routing\\Router-&gt;dispatch(Request $request)\">    return $this-&gt;dispatchToRoute($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:200
Stack level 23.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:200</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;Illuminate\\Foundation\\Http\\{closure}\">    return $this-&gt;router-&gt;dispatch($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>};</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:144
Stack level 22.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:144</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>try {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">    return $destination($passable);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php:16
Stack level 21.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">app\\Http\\Middleware\\SecurityHeaders.php:16</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Http\\Middleware\\SecurityHeaders-&gt;handle(Request $request, Closure $next): Response
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">SecurityHeaders-&gt;handle(Request $request, Closure $next): Response</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>{</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in App\\Http\\Middleware\\SecurityHeaders-&gt;handle(Request $request, Closure $next): Response\">    $response = $next($request);</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 20.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 19.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TransformsRequest-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31
Stack level 18.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php:31</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull-&gt;handle($request, Closure $next)\">    return parent::handle($request, $next);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 17.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21
Stack level 16.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php:21</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TransformsRequest-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40
Stack level 15.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php:40</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\TrimStrings-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TrimStrings-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\TrimStrings-&gt;handle($request, Closure $next)\">    return parent::handle($request, $next);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 14.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27
Stack level 13.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php:27</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ValidatePostSize-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 12.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99
Stack level 11.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php:99</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 10.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php:49
Stack level 9.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\HandleCors.php:49</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Http\\Middleware\\HandleCors-&gt;handle($request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HandleCors-&gt;handle($request, Closure $next)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>if (! $this-&gt;hasMatchingPath($request)) {</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Http\\Middleware\\HandleCors-&gt;handle($request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 8.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39
Stack level 7.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Http\\Middleware\\TrustProxies.php:39</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Http\\Middleware\\TrustProxies-&gt;handle(Request $request, Closure $next)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Http\\Middleware</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">TrustProxies-&gt;handle(Request $request, Closure $next)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Http\\Middleware\\TrustProxies-&gt;handle(Request $request, Closure $next)\">    return $next($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:183
Stack level 6.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:183</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">{closure}</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>$carry = method_exists($pipe, $this-&gt;method)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;Illuminate\\Pipeline\\{closure}\">                ? $pipe-&gt;{$this-&gt;method}(...$parameters)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>                : $pipe(...$parameters);</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;2}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:119
Stack level 5.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Pipeline\\Pipeline.php:119</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Pipeline</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Pipeline-&gt;then(Closure $destination)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Pipeline\\Pipeline-&gt;then(Closure $destination)\">    return $pipeline($this-&gt;passable);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:175
Stack level 4.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:175</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;sendRequestThroughRouter($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Kernel-&gt;sendRequestThroughRouter($request)</span></span> &#8230;
      &#8250; <code class=\"php\"><span class=sf-dump-default>                -&gt;through($this-&gt;app-&gt;shouldSkipMiddleware() ? [] : $this-&gt;middleware)</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;sendRequestThroughRouter($request)\">                -&gt;then($this-&gt;dispatchToRouter());</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>}</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php:144
Stack level 3.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\Http\\Kernel.php:144</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation\\Http</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Kernel-&gt;handle($request)</span></span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in Illuminate\\Foundation\\Http\\Kernel-&gt;handle($request)\">    $response = $this-&gt;sendRequestThroughRouter($request);</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>} catch (Throwable $e) {</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php:51
Stack level 2.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\</span><span class=\"sf-dump-ellipsis-tail\">public\\index.php:51</span></span> {<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-note>require_once</span> &#8230;
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const title=\"in require_once\">$response = $kernel-&gt;handle(</span></code>
      &#8250; <code class=\"php\"><span class=sf-dump-default>    $request = Request::capture()</span></code>
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
    <span class=\"sf-dump-meta sf-dump-ellipsization\" title=\"C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php:16
Stack level 1.\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">C:\\Users\\<USER>\\Desktop\\LMS\\vendor</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-path\">\\laravel\\framework\\</span><span class=\"sf-dump-ellipsis-tail\">src\\Illuminate\\Foundation\\resources\\server.php:16</span></span> {<samp data-depth=3 class=sf-dump-compact>
      &#8250; 
      &#8250; <code class=\"php\"><span class=sf-dump-const>require_once $publicPath.&#039;/index.php&#039;;</span></code>
      &#8250; 
      <span class=sf-dump-meta>arguments</span>: { &#8230;1}
    </samp>}
  </samp>}
</samp>}
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [dashboard.leaderboard.index] not defined. at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard.leade...', Array, true)
#1 C:\\Users\\<USER>\\Desktop\\LMS\\resources\\views\\errors\\404.blade.php(105): route('dashboard.leade...')
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 404, Array)
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 404, Array)
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::404', Array, 404, Array)
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#15 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Database\\Eloquent\\ModelNotFoundException))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#60 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [dashboard.leaderboard.index] not defined. at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('dashboard.leade...', Array, true)
#1 C:\\Users\\<USER>\\Desktop\\LMS\\storage\\framework\\views\\415863571215088524be0b912c95e864.php(103): route('dashboard.leade...')
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(58): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 404, Array)
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(88): Illuminate\\Routing\\ResponseFactory->make(Object(Illuminate\\View\\View), 404, Array)
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(723): Illuminate\\Routing\\ResponseFactory->view('errors::404', Array, 404, Array)
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(650): Illuminate\\Foundation\\Exceptions\\Handler->renderHttpException(Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#15 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(556): Illuminate\\Foundation\\Exceptions\\Handler->prepareResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(473): Illuminate\\Foundation\\Exceptions\\Handler->renderExceptionResponse(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(51): Illuminate\\Foundation\\Exceptions\\Handler->render(Object(Illuminate\\Http\\Request), Object(Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(188): Illuminate\\Routing\\Pipeline->handleException(Object(Illuminate\\Http\\Request), Object(Illuminate\\Database\\Eloquent\\ModelNotFoundException))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#60 {main}
"} 
[2025-07-18 20:45:15] local.ERROR: Call to undefined method App\Models\Badge::users() {"userId":2,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Badge::users() at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:67)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('users')
#1 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'users', Array)
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(876): Illuminate\\Database\\Eloquent\\Model->__call('users', Array)
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(110): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(875): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(630): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('users')
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\QueriesRelationships.php(719): Illuminate\\Database\\Eloquent\\Builder->withAggregate(Array, '*', 'count')
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->withCount('users')
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'withCount', Array)
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('withCount', Array)
#10 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Controllers\\Admin\\AdminBadgeController.php(16): Illuminate\\Database\\Eloquent\\Model::__callStatic('withCount', Array)
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Admin\\AdminBadgeController->index()
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('index', Array)
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\AdminBadgeController), 'index')
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#15 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\AdminMiddleware.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#64 {main}
"} 
[2025-07-18 20:45:21] local.ERROR: Route [admin.tasks.index] not defined. {"view":{"view":"C:\\Users\\<USER>\\Desktop\\LMS\\resources\\views\\admin\\campuses\\show.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-120298786 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1538</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-120298786\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","campus":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Campus</span> {<a class=sf-dump-ref>#1579</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">campuses</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">E-commerce</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ecommerce</span>\"
    \"<span class=sf-dump-key>icon_url</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Learn how to build and scale profitable e-commerce businesses from scratch.</span>\"
    \"<span class=sf-dump-key>teaser_description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Master dropshipping, Amazon FBA, and online retail strategies.</span>\"
    \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#10b981</span>\"
    \"<span class=sf-dump-key>sort_order</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_premium</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>required_plans</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[&quot;conquer&quot;,&quot;champions&quot;]</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 20:16:18</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 20:16:18</span>\"
    \"<span class=sf-dump-key>courses_count</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>users_count</span>\" => <span class=sf-dump-num>0</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">E-commerce</span>\"
    \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"9 characters\">ecommerce</span>\"
    \"<span class=sf-dump-key>icon_url</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"75 characters\">Learn how to build and scale profitable e-commerce businesses from scratch.</span>\"
    \"<span class=sf-dump-key>teaser_description</span>\" => \"<span class=sf-dump-str title=\"62 characters\">Master dropshipping, Amazon FBA, and online retail strategies.</span>\"
    \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#10b981</span>\"
    \"<span class=sf-dump-key>sort_order</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>is_premium</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>required_plans</span>\" => \"<span class=sf-dump-str title=\"23 characters\">[&quot;conquer&quot;,&quot;champions&quot;]</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 20:16:18</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-18 20:16:18</span>\"
    \"<span class=sf-dump-key>courses_count</span>\" => <span class=sf-dump-num>2</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>is_active</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>is_premium</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"
    \"<span class=sf-dump-key>required_plans</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>courses</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#1608</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"4 characters\">slug</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">icon_url</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"18 characters\">teaser_description</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">color</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"10 characters\">sort_order</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"10 characters\">is_premium</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">required_plans</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":2,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [admin.tasks.index] not defined. at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('admin.tasks.ind...', Array, true)
#1 C:\\Users\\<USER>\\Desktop\\LMS\\resources\\views\\admin\\campuses\\show.blade.php(161): route('admin.tasks.ind...', Array)
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\AdminMiddleware.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [admin.tasks.index] not defined. at C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('admin.tasks.ind...', Array, true)
#1 C:\\Users\\<USER>\\Desktop\\LMS\\storage\\framework\\views\\3fdf04cdba4a46e37b58805e673c4ea6.php(162): route('admin.tasks.ind...', Array)
#2 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Users\\\\<USER>\\\\...')
#3 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#8 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#9 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#10 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#11 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#12 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#14 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\AdminMiddleware.php(30): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\LMS\\app\\Http\\Middleware\\SecurityHeaders.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SecurityHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\Desktop\\LMS\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\LMS\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}
"} 
