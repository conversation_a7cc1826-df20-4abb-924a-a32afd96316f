@extends('layouts.app')

@section('title', $campus->name . ' Campus - The Real World')

@section('content')
<!-- Campus Hero Section -->
<section class="relative py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-black overflow-hidden">
    <div class="absolute inset-0 bg-black/20"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <!-- Campus Icon -->
            <div class="inline-flex items-center justify-center w-24 h-24 rounded-2xl mb-6 text-5xl"
                 style="background: linear-gradient(135deg, {{ $campus->color }}20, {{ $campus->color }}40); border: 3px solid {{ $campus->color }};">
                @if($campus->icon_url)
                    <img src="{{ $campus->icon_url }}" alt="{{ $campus->name }}" class="w-12 h-12">
                @else
                    <span style="color: {{ $campus->color }};">🏛️</span>
                @endif
            </div>

            <h1 class="text-4xl md:text-6xl font-bold font-display mb-6">
                {{ $campus->name }}
                @if($campus->is_premium)
                    <span class="inline-block bg-red-600 text-white text-sm font-semibold px-3 py-1 rounded-full ml-3">
                        Premium
                    </span>
                @endif
            </h1>
            
            <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
                {{ $campus->teaser_description ?: $campus->description }}
            </p>

            <!-- Campus Stats -->
            <div class="flex flex-wrap justify-center gap-8 mb-8">
                <div class="text-center">
                    <div class="text-3xl font-bold" style="color: {{ $campus->color }};">{{ $stats['total_courses'] }}</div>
                    <div class="text-gray-400">Courses</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold" style="color: {{ $campus->color }};">{{ $stats['total_lessons'] }}</div>
                    <div class="text-gray-400">Lessons</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold" style="color: {{ $campus->color }};">{{ number_format($stats['total_students']) }}</div>
                    <div class="text-gray-400">Students</div>
                </div>
            </div>

            <!-- CTA Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @auth
                    <a href="{{ route('dashboard.campuses.show', $campus) }}" class="btn-primary text-lg px-8 py-4">
                        Access Campus
                    </a>
                @else
                    <a href="{{ route('register') }}" class="btn-primary text-lg px-8 py-4">
                        Join The Real World
                    </a>
                @endauth
                <a href="{{ route('pricing') }}" class="btn-secondary text-lg px-8 py-4">
                    View Pricing
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Campus Description -->
<section class="py-16 bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="prose prose-lg prose-invert mx-auto">
            <h2 class="text-3xl font-bold font-display mb-6 text-center">About {{ $campus->name }}</h2>
            <div class="text-gray-300 leading-relaxed">
                {!! nl2br(e($campus->description)) !!}
            </div>
        </div>
    </div>
</section>

<!-- Courses Section -->
@if($courses->count() > 0)
<section class="py-16 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold font-display mb-4">
                Available Courses
            </h2>
            <p class="text-xl text-gray-400">
                Master these skills to build wealth and success
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($courses as $course)
            <div class="card group hover:transform hover:scale-105 transition-all duration-300">
                @if($course->thumbnail_url)
                <div class="aspect-video bg-gray-700 rounded-t-xl overflow-hidden">
                    <img src="{{ $course->thumbnail_url }}" alt="{{ $course->title }}" 
                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                </div>
                @endif
                
                <div class="p-6">
                    <div class="flex items-center justify-between mb-3">
                        <span class="badge badge-{{ $course->difficulty === 'beginner' ? 'green' : ($course->difficulty === 'intermediate' ? 'yellow' : 'red') }}">
                            {{ ucfirst($course->difficulty) }}
                        </span>
                        @if($course->is_featured)
                            <span class="badge badge-purple">Featured</span>
                        @endif
                    </div>
                    
                    <h3 class="text-xl font-semibold mb-3 group-hover:text-red-400 transition-colors">
                        {{ $course->title }}
                    </h3>
                    
                    <p class="text-gray-400 mb-4 line-clamp-3">
                        {{ $course->description }}
                    </p>
                    
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>{{ $course->lessons_count }} lessons</span>
                        @if($course->duration_minutes)
                            <span>{{ $course->duration_minutes }} min</span>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Access Requirements -->
@if($campus->is_premium)
<section class="py-16 bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="card border-2 border-red-600/30 bg-red-600/10">
            <h3 class="text-2xl font-bold font-display mb-4 text-red-400">
                Premium Campus Access Required
            </h3>
            <p class="text-gray-300 mb-6">
                This campus requires a premium subscription to access all courses and content.
            </p>
            
            @if($campus->required_plans)
            <div class="mb-6">
                <p class="text-sm text-gray-400 mb-3">Available with these plans:</p>
                <div class="flex flex-wrap justify-center gap-2">
                    @foreach($campus->required_plans as $plan)
                        <span class="badge badge-red">{{ ucfirst($plan) }}</span>
                    @endforeach
                </div>
            </div>
            @endif
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('pricing') }}" class="btn-primary">
                    Upgrade Now
                </a>
                <a href="{{ route('campuses') }}" class="btn-secondary">
                    View All Campuses
                </a>
            </div>
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-red-600 to-red-700">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold font-display mb-6">
            Ready to Master {{ $campus->name }}?
        </h2>
        <p class="text-xl text-red-100 mb-8">
            Join thousands of students who are already building wealth with real-world skills.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            @auth
                <a href="{{ route('dashboard.campuses.show', $campus) }}" class="bg-white text-red-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-xl transition-colors text-lg">
                    Access Campus Now
                </a>
            @else
                <a href="{{ route('register') }}" class="bg-white text-red-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-xl transition-colors text-lg">
                    Join The Real World
                </a>
            @endauth
            <a href="{{ route('campuses') }}" class="border-2 border-white text-white hover:bg-white hover:text-red-600 font-semibold py-4 px-8 rounded-xl transition-colors text-lg">
                Explore All Campuses
            </a>
        </div>
    </div>
</section>
@endsection
