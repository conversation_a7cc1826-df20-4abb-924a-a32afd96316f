<?php $__env->startSection('title', 'Lesson Details - ' . $lesson->title); ?>
<?php $__env->startSection('page-title', 'Lesson Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Lesson Details</h1>
            <p class="text-gray-400 mt-1"><?php echo e($lesson->title); ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.lessons.edit', $lesson)); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Lesson
            </a>
            <a href="<?php echo e(route('admin.courses.show', $lesson->course)); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Course
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Lesson Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Lesson Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Lesson Title</label>
                            <p class="text-white"><?php echo e($lesson->title); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Lesson Order</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <?php echo e($lesson->sort_order ?? 'N/A'); ?>

                            </span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300"><?php echo e($lesson->description); ?></p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Course</label>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium text-white" 
                                      style="background-color: <?php echo e($lesson->course->campus->color); ?>">
                                    <?php echo e($lesson->course->campus->name); ?>

                                </span>
                                <span class="text-white"><?php echo e($lesson->course->title); ?></span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            <div class="flex items-center space-x-2">
                                <?php if($lesson->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                <?php endif; ?>
                                <?php if($lesson->is_preview): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Preview</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if($lesson->content): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Lesson Content</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <p class="text-gray-300 whitespace-pre-wrap"><?php echo e($lesson->content); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Duration</label>
                            <p class="text-white">
                                <?php if($lesson->duration_seconds): ?>
                                    <?php echo e(gmdate('H:i:s', $lesson->duration_seconds)); ?>

                                <?php else: ?>
                                    <span class="text-gray-500">Not specified</span>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">XP Reward</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <?php echo e($lesson->xp_reward ?? 0); ?> XP
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Created</label>
                            <p class="text-gray-300"><?php echo e($lesson->created_at->format('M j, Y g:i A')); ?></p>
                        </div>
                    </div>

                    <?php if($lesson->video_url): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Video</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white mb-1"><strong>URL:</strong> 
                                        <a href="<?php echo e($lesson->video_url); ?>" target="_blank" class="text-primary-400 hover:text-primary-300"><?php echo e($lesson->video_url); ?></a>
                                    </p>
                                    <?php if($lesson->video_type): ?>
                                        <p class="text-gray-300"><strong>Type:</strong> <?php echo e(ucfirst($lesson->video_type)); ?></p>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo e($lesson->video_url); ?>" target="_blank" class="btn-outline">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h10a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2v-8a2 2 0 012-2z"></path>
                                    </svg>
                                    Watch Video
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($lesson->resource_url): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Additional Resources</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <a href="<?php echo e($lesson->resource_url); ?>" target="_blank" class="btn-outline">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Download Resource
                            </a>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Student Progress -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Student Progress</h3>
                </div>
                <div class="p-6">
                    <?php if($lesson->userProgress()->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-700">
                                <thead class="bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Student</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Progress</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Last Activity</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-gray-800 divide-y divide-gray-700">
                                    <?php $__currentLoopData = $lesson->userProgress()->with('user')->latest()->take(10)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $progress): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr class="hover:bg-gray-700">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-8 w-8">
                                                    <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                                                        <span class="text-xs font-medium text-white"><?php echo e(substr($progress->user->name, 0, 1)); ?></span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-white"><?php echo e($progress->user->name); ?></div>
                                                    <div class="text-sm text-gray-400"><?php echo e($progress->user->email); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <div class="w-full bg-gray-700 rounded-full h-2">
                                                <div class="bg-primary-600 h-2 rounded-full" style="width: <?php echo e($progress->progress_percentage); ?>%"></div>
                                            </div>
                                            <span class="text-xs text-gray-400 mt-1"><?php echo e($progress->progress_percentage); ?>%</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            <?php if($progress->is_completed): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Completed</span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-300">
                                            <?php echo e($progress->updated_at->diffForHumans()); ?>

                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if($lesson->userProgress()->count() > 10): ?>
                        <div class="text-center mt-4">
                            <p class="text-sm text-gray-400">Showing 10 of <?php echo e($lesson->userProgress()->count()); ?> students</p>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-300">No student progress yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Students haven't started this lesson yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Lesson Statistics -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Lesson Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center mb-4">
                        <div>
                            <div class="text-2xl font-bold text-primary-400"><?php echo e($lesson->userProgress()->count()); ?></div>
                            <div class="text-sm text-gray-400">Total Views</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400"><?php echo e($lesson->userProgress()->where('is_completed', true)->count()); ?></div>
                            <div class="text-sm text-gray-400">Completions</div>
                        </div>
                    </div>
                    
                    <?php if($lesson->userProgress()->count() > 0): ?>
                    <div class="text-center">
                        <div class="text-xl font-bold text-blue-400">
                            <?php echo e(round(($lesson->userProgress()->where('is_completed', true)->count() / $lesson->userProgress()->count()) * 100, 1)); ?>%
                        </div>
                        <div class="text-sm text-gray-400">Completion Rate</div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('admin.lessons.edit', $lesson)); ?>" class="w-full btn-primary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Lesson
                    </a>
                    <a href="<?php echo e(route('admin.courses.show', $lesson->course)); ?>" class="w-full btn-outline text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        View Course
                    </a>
                    <?php if($lesson->video_url): ?>
                    <a href="<?php echo e($lesson->video_url); ?>" class="w-full btn-outline text-center" target="_blank">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h10a2 2 0 012 2v8a2 2 0 01-2 2H6a2 2 0 01-2-2v-8a2 2 0 012-2z"></path>
                        </svg>
                        Watch Video
                    </a>
                    <?php endif; ?>
                    <?php if($lesson->resource_url): ?>
                    <a href="<?php echo e($lesson->resource_url); ?>" class="w-full btn-outline text-center" target="_blank">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download Resource
                    </a>
                    <?php endif; ?>
                    <?php if($lesson->userProgress()->count() == 0): ?>
                    <form action="<?php echo e(route('admin.lessons.destroy', $lesson)); ?>" method="POST" class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this lesson?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-danger text-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Lesson
                        </button>
                    </form>
                    <?php else: ?>
                    <button class="w-full btn-outline text-center opacity-50 cursor-not-allowed" disabled title="Cannot delete lesson with student progress">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Cannot Delete (Has Progress)
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/lessons/show.blade.php ENDPATH**/ ?>