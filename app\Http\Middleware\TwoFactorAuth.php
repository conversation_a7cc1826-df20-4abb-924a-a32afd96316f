<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class TwoFactorAuth
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        // Skip if user is not authenticated
        if (!$user) {
            return $next($request);
        }
        
        // Skip if 2FA is not enabled for this user
        if (!$user->two_factor_enabled) {
            return $next($request);
        }
        
        // Skip if user has already verified 2FA in this session
        if ($request->session()->get('2fa_verified', false)) {
            return $next($request);
        }
        
        // Skip for 2FA verification routes
        if ($this->isExemptRoute($request)) {
            return $next($request);
        }
        
        // Redirect to 2FA verification page
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Two-factor authentication required',
                'redirect' => route('2fa.verify')
            ], 403);
        }
        
        return redirect()->route('2fa.verify')
            ->with('message', 'Please verify your identity with two-factor authentication.');
    }
    
    /**
     * Check if the current route is exempt from 2FA verification
     */
    protected function isExemptRoute(Request $request): bool
    {
        $exemptRoutes = [
            '2fa/verify',
            '2fa/challenge',
            'logout',
            'api/2fa/*',
        ];
        
        $currentPath = $request->path();
        
        foreach ($exemptRoutes as $route) {
            if ($route === $currentPath || fnmatch($route, $currentPath)) {
                return true;
            }
        }
        
        return false;
    }
}
