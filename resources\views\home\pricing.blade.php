@extends('layouts.app')

@section('title', 'Pricing - The Real World')
@section('description', 'Choose the perfect plan for your journey to financial freedom. Flexible pricing with access to all campuses and mentors.')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold font-display mb-6">
            Choose Your <span class="text-gradient">Plan</span>
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Invest in yourself and your future. All plans include access to our community, 
            courses, and mentorship. Cancel anytime.
        </p>
        
        <!-- Billing Toggle -->
        <div class="flex items-center justify-center mb-12" x-data="{ yearly: false }">
            <span class="text-gray-300 mr-3">Monthly</span>
            <button @click="yearly = !yearly" class="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900">
                <span :class="yearly ? 'translate-x-6' : 'translate-x-1'" class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"></span>
            </button>
            <span class="text-gray-300 ml-3">Yearly</span>
            <span class="ml-2 text-sm text-green-400 font-medium">(Save 17%)</span>
        </div>
    </div>
</section>

<!-- Pricing Cards -->
<section class="py-20 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8" x-data="{ yearly: false }">
            @foreach($plans as $planKey => $plan)
            <div class="card relative {{ $planKey === 'conquer' ? 'ring-2 ring-primary-500 transform scale-105' : '' }}">
                @if($planKey === 'conquer')
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-primary-600 text-white text-sm font-semibold px-4 py-1 rounded-full">
                        Most Popular
                    </span>
                </div>
                @endif

                <!-- Plan Header -->
                <div class="text-center mb-8">
                    <h3 class="text-2xl font-bold mb-2">{{ $plan['name'] }}</h3>
                    <div class="mb-4">
                        <span class="text-4xl font-bold" x-show="!yearly">${{ $plan['monthly_price'] }}</span>
                        <span class="text-4xl font-bold" x-show="yearly">${{ $plan['yearly_price'] }}</span>
                        <span class="text-gray-400" x-show="!yearly">/month</span>
                        <span class="text-gray-400" x-show="yearly">/year</span>
                    </div>
                    <p class="text-gray-400" x-show="yearly">
                        Save ${{ ($plan['monthly_price'] * 12) - $plan['yearly_price'] }} per year
                    </p>
                </div>

                <!-- Features -->
                <ul class="space-y-4 mb-8">
                    @foreach($plan['features'] as $feature)
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-300">{{ $feature }}</span>
                    </li>
                    @endforeach
                </ul>

                <!-- CTA Button -->
                <div class="mt-auto">
                    @auth
                        <a href="{{ route('subscription.plans') }}" class="btn-{{ $planKey === 'conquer' ? 'primary' : 'secondary' }} w-full text-center">
                            Choose {{ $plan['name'] }}
                        </a>
                    @else
                        <a href="{{ route('register') }}" class="btn-{{ $planKey === 'conquer' ? 'primary' : 'secondary' }} w-full text-center">
                            Get Started
                        </a>
                    @endauth
                </div>
            </div>
            @endforeach
        </div>

        <!-- Free Trial Notice -->
        <div class="text-center mt-12">
            <p class="text-gray-400 mb-4">
                🎉 <strong>7-day free trial</strong> for all new members. Cancel anytime during trial period.
            </p>
            <div class="flex items-center justify-center space-x-6 text-sm text-gray-500">
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    Secure Payment
                </span>
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Cancel Anytime
                </span>
                <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Money Back Guarantee
                </span>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-center mb-12">Frequently Asked Questions</h2>
        
        <div class="space-y-6" x-data="{ openFaq: null }">
            <div class="border border-gray-700 rounded-lg">
                <button @click="openFaq = openFaq === 1 ? null : 1" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800 transition-colors">
                    <span class="font-semibold">What's included in each plan?</span>
                    <svg :class="openFaq === 1 ? 'rotate-180' : ''" class="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 1" x-collapse class="px-6 pb-4">
                    <p class="text-gray-400">Each plan includes access to courses, community chat, live events, and mentorship. Higher tiers unlock more campuses and exclusive features like 1-on-1 coaching.</p>
                </div>
            </div>

            <div class="border border-gray-700 rounded-lg">
                <button @click="openFaq = openFaq === 2 ? null : 2" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800 transition-colors">
                    <span class="font-semibold">Can I change my plan later?</span>
                    <svg :class="openFaq === 2 ? 'rotate-180' : ''" class="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 2" x-collapse class="px-6 pb-4">
                    <p class="text-gray-400">Yes! You can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle.</p>
                </div>
            </div>

            <div class="border border-gray-700 rounded-lg">
                <button @click="openFaq = openFaq === 3 ? null : 3" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800 transition-colors">
                    <span class="font-semibold">Is there a money-back guarantee?</span>
                    <svg :class="openFaq === 3 ? 'rotate-180' : ''" class="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 3" x-collapse class="px-6 pb-4">
                    <p class="text-gray-400">We offer a 7-day free trial for all new members. If you're not satisfied, you can cancel within the trial period at no cost.</p>
                </div>
            </div>

            <div class="border border-gray-700 rounded-lg">
                <button @click="openFaq = openFaq === 4 ? null : 4" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800 transition-colors">
                    <span class="font-semibold">How do I cancel my subscription?</span>
                    <svg :class="openFaq === 4 ? 'rotate-180' : ''" class="w-5 h-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 4" x-collapse class="px-6 pb-4">
                    <p class="text-gray-400">You can cancel your subscription anytime from your account settings. You'll continue to have access until the end of your current billing period.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Final CTA -->
<section class="py-20 bg-gradient-to-r from-primary-600 to-primary-800">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold font-display mb-6 text-white">
            Start Your Journey Today
        </h2>
        <p class="text-xl text-primary-100 mb-8">
            Join thousands of students who are already building wealth with The Real World. 
            Your financial freedom is just one click away.
        </p>
        <a href="{{ route('register') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors text-lg">
            Join The Real World
        </a>
    </div>
</section>
@endsection

@push('scripts')
<script src="https://unpkg.com/@alpinejs/collapse@3.x.x/dist/cdn.min.js"></script>
@endpush
