@extends('layouts.admin')

@section('title', 'Edit Lesson - ' . $lesson->title)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Lesson</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.lessons.index') }}">Lessons</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.courses.show', $lesson->course) }}">{{ $lesson->course->title }}</a></li>
                    <li class="breadcrumb-item active">Edit {{ $lesson->title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.courses.show', $lesson->course) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Course
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Lesson Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lesson Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.lessons.update', $lesson) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="course_id" class="form-label">Course <span class="text-danger">*</span></label>
                                    <select class="form-select @error('course_id') is-invalid @enderror" id="course_id" name="course_id" required>
                                        <option value="">Select a course</option>
                                        @foreach($courses as $course)
                                            <option value="{{ $course->id }}" 
                                                    {{ old('course_id', $lesson->course_id) == $course->id ? 'selected' : '' }}>
                                                {{ $course->campus->name }} - {{ $course->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('course_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Lesson Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', $lesson->sort_order) }}" min="0">
                                    <small class="form-text text-muted">Order within the course (1, 2, 3...)</small>
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Lesson Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $lesson->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" required>{{ old('description', $lesson->description) }}</textarea>
                            <small class="form-text text-muted">Brief description of what students will learn</small>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Video Section -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Video Content</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="video_url" class="form-label">Video URL</label>
                                            <input type="url" class="form-control @error('video_url') is-invalid @enderror" 
                                                   id="video_url" name="video_url" value="{{ old('video_url', $lesson->video_url) }}" 
                                                   placeholder="https://youtube.com/watch?v=...">
                                            @error('video_url')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="video_type" class="form-label">Video Type</label>
                                            <select class="form-select @error('video_type') is-invalid @enderror" id="video_type" name="video_type">
                                                <option value="">Auto-detect</option>
                                                <option value="youtube" {{ old('video_type', $lesson->video_type) === 'youtube' ? 'selected' : '' }}>YouTube</option>
                                                <option value="vimeo" {{ old('video_type', $lesson->video_type) === 'vimeo' ? 'selected' : '' }}>Vimeo</option>
                                                <option value="direct" {{ old('video_type', $lesson->video_type) === 'direct' ? 'selected' : '' }}>Direct Link</option>
                                            </select>
                                            @error('video_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="duration_seconds" class="form-label">Duration (seconds)</label>
                                    <input type="number" class="form-control @error('duration_seconds') is-invalid @enderror" 
                                           id="duration_seconds" name="duration_seconds" value="{{ old('duration_seconds', $lesson->duration_seconds) }}" min="1">
                                    <small class="form-text text-muted">Video duration in seconds (e.g., 300 for 5 minutes)</small>
                                    @error('duration_seconds')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Content Section -->
                        <div class="mb-3">
                            <label for="content" class="form-label">Lesson Content</label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="6">{{ old('content', $lesson->content) }}</textarea>
                            <small class="form-text text-muted">Additional text content, notes, or instructions</small>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="resource_url" class="form-label">Resource URL</label>
                                    <input type="url" class="form-control @error('resource_url') is-invalid @enderror" 
                                           id="resource_url" name="resource_url" value="{{ old('resource_url', $lesson->resource_url) }}" 
                                           placeholder="https://example.com/resource.pdf">
                                    <small class="form-text text-muted">Link to additional resources (PDF, document, etc.)</small>
                                    @error('resource_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="xp_reward" class="form-label">XP Reward</label>
                                    <input type="number" class="form-control @error('xp_reward') is-invalid @enderror" 
                                           id="xp_reward" name="xp_reward" value="{{ old('xp_reward', $lesson->xp_reward) }}" min="0" max="1000">
                                    <small class="form-text text-muted">XP points awarded for completing this lesson</small>
                                    @error('xp_reward')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', $lesson->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            <strong>Active</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Lesson is visible and accessible to students</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_preview" name="is_preview" value="1" 
                                               {{ old('is_preview', $lesson->is_preview) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_preview">
                                            <strong>Preview Lesson</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Can be viewed without course enrollment</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.courses.show', $lesson->course) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Lesson
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Lesson Stats -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lesson Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $lesson->userProgress()->count() }}</h4>
                                <small class="text-muted">Total Views</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $lesson->userProgress()->where('is_completed', true)->count() }}</h4>
                            <small class="text-muted">Completions</small>
                        </div>
                    </div>
                    
                    @if($lesson->userProgress()->count() > 0)
                    <hr>
                    <div class="text-center">
                        <h5 class="text-info">
                            {{ round(($lesson->userProgress()->where('is_completed', true)->count() / $lesson->userProgress()->count()) * 100, 1) }}%
                        </h5>
                        <small class="text-muted">Completion Rate</small>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.lessons.show', $lesson) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="{{ route('admin.courses.show', $lesson->course) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-book"></i> View Course
                        </a>
                        @if($lesson->video_url)
                        <a href="{{ $lesson->video_url }}" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-play"></i> Watch Video
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-detect video type from URL
    const videoUrlInput = document.getElementById('video_url');
    const videoTypeSelect = document.getElementById('video_type');
    
    videoUrlInput.addEventListener('input', function() {
        const url = this.value.toLowerCase();
        
        if (url.includes('youtube.com') || url.includes('youtu.be')) {
            videoTypeSelect.value = 'youtube';
        } else if (url.includes('vimeo.com')) {
            videoTypeSelect.value = 'vimeo';
        } else if (url.includes('.mp4') || url.includes('.webm') || url.includes('.ogg')) {
            videoTypeSelect.value = 'direct';
        }
    });
});
</script>
@endpush
