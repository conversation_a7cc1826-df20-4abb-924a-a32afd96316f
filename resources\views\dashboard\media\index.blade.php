@extends('layouts.dashboard')

@section('title', 'Media Library - The Real World')
@section('page-title', 'Media Library')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold mb-2">Media Library</h2>
            <p class="text-gray-400">Manage your uploaded files and media</p>
        </div>
        <button onclick="openUploadModal()" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Upload Files
        </button>
    </div>

    <!-- Filters -->
    <div class="card mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" 
                       id="searchInput"
                       placeholder="Search files..."
                       class="input-field">
            </div>
            
            <select id="typeFilter" class="input-field w-auto">
                <option value="">All Types</option>
                <option value="image">Images</option>
                <option value="video">Videos</option>
                <option value="audio">Audio</option>
                <option value="document">Documents</option>
            </select>
            
            <select id="collectionFilter" class="input-field w-auto">
                <option value="">All Collections</option>
                <option value="avatar">Avatars</option>
                <option value="course">Course Materials</option>
                <option value="event">Event Media</option>
                <option value="general">General</option>
            </select>
            
            <button onclick="loadMedia()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Filter
            </button>
        </div>
    </div>

    <!-- Media Grid -->
    <div id="mediaGrid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        <!-- Media items will be loaded here -->
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p class="text-gray-400">Loading media...</p>
    </div>

    <!-- Empty State -->
    <div id="emptyState" class="text-center py-12 hidden">
        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">No media files found</h3>
        <p class="text-gray-400 mb-6">Upload your first file to get started</p>
        <button onclick="openUploadModal()" class="btn-primary">Upload Files</button>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="mt-8 flex items-center justify-center space-x-2 hidden">
        <!-- Pagination will be loaded here -->
    </div>
</div>

<!-- Upload Modal -->
<div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-xl font-semibold mb-4">Upload Files</h3>
        
        <form id="uploadForm" enctype="multipart/form-data">
            @csrf
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Select Files</label>
                <input type="file" 
                       id="fileInput"
                       name="file"
                       multiple
                       accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
                       class="input-field">
                <p class="text-xs text-gray-400 mt-1">Max file size: 10MB. Supported: Images, Videos, Audio, Documents</p>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Collection (Optional)</label>
                <select name="collection" class="input-field">
                    <option value="">No Collection</option>
                    <option value="avatar">Avatar</option>
                    <option value="course">Course Materials</option>
                    <option value="event">Event Media</option>
                    <option value="general">General</option>
                </select>
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-300 mb-2">Description (Optional)</label>
                <textarea name="description" 
                          rows="3"
                          placeholder="Describe this file..."
                          class="input-field"></textarea>
            </div>
            
            <div class="mb-6">
                <label class="flex items-center">
                    <input type="checkbox" name="is_public" checked class="w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500">
                    <span class="ml-3 text-sm">Make file public</span>
                </label>
            </div>
            
            <div class="flex items-center justify-end space-x-4">
                <button type="button" onclick="closeUploadModal()" class="btn-secondary">Cancel</button>
                <button type="submit" class="btn-primary">
                    <span id="uploadButtonText">Upload</span>
                    <div id="uploadSpinner" class="hidden animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                </button>
            </div>
        </form>
        
        <!-- Upload Progress -->
        <div id="uploadProgress" class="hidden mt-4">
            <div class="bg-gray-700 rounded-full h-2">
                <div id="progressBar" class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
            </div>
            <p id="progressText" class="text-sm text-gray-400 mt-2">Uploading...</p>
        </div>
    </div>
</div>

<!-- Media Detail Modal -->
<div id="mediaModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold">Media Details</h3>
            <button onclick="closeMediaModal()" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div id="mediaContent">
            <!-- Media content will be loaded here -->
        </div>
    </div>
</div>

<script>
let currentPage = 1;
let currentMedia = [];

// Load media on page load
document.addEventListener('DOMContentLoaded', function() {
    loadMedia();
});

// Upload modal functions
function openUploadModal() {
    document.getElementById('uploadModal').classList.remove('hidden');
    document.getElementById('uploadModal').classList.add('flex');
}

function closeUploadModal() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('uploadModal').classList.remove('flex');
    document.getElementById('uploadForm').reset();
    document.getElementById('uploadProgress').classList.add('hidden');
}

// Media modal functions
function openMediaModal(mediaId) {
    const media = currentMedia.find(m => m.id === mediaId);
    if (!media) return;
    
    document.getElementById('mediaModal').classList.remove('hidden');
    document.getElementById('mediaModal').classList.add('flex');
    
    // Load media details
    loadMediaDetails(mediaId);
}

function closeMediaModal() {
    document.getElementById('mediaModal').classList.add('hidden');
    document.getElementById('mediaModal').classList.remove('flex');
}

// Load media function
async function loadMedia(page = 1) {
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const collectionFilter = document.getElementById('collectionFilter');
    
    const params = new URLSearchParams({
        page: page,
        per_page: 24,
        search: searchInput.value,
        type: typeFilter.value,
        collection: collectionFilter.value,
    });
    
    // Show loading state
    document.getElementById('loadingState').classList.remove('hidden');
    document.getElementById('mediaGrid').innerHTML = '';
    document.getElementById('emptyState').classList.add('hidden');
    
    try {
        const response = await fetch(`/dashboard/media/library?${params}`);
        const data = await response.json();
        
        if (data.success) {
            currentMedia = data.media;
            renderMediaGrid(data.media);
            renderPagination(data.pagination);
            
            if (data.media.length === 0) {
                document.getElementById('emptyState').classList.remove('hidden');
            }
        }
    } catch (error) {
        console.error('Error loading media:', error);
    } finally {
        document.getElementById('loadingState').classList.add('hidden');
    }
}

// Render media grid
function renderMediaGrid(media) {
    const grid = document.getElementById('mediaGrid');
    
    grid.innerHTML = media.map(item => `
        <div class="bg-gray-700 rounded-lg overflow-hidden hover:bg-gray-600 transition-colors cursor-pointer"
             onclick="openMediaModal(${item.id})">
            <div class="aspect-square bg-gray-800 flex items-center justify-center">
                ${item.type === 'image' ? 
                    `<img src="${item.url}" alt="${item.name}" class="w-full h-full object-cover">` :
                    `<div class="text-4xl">${getTypeIcon(item.type)}</div>`
                }
            </div>
            <div class="p-3">
                <h4 class="font-medium text-sm truncate mb-1">${item.name}</h4>
                <p class="text-xs text-gray-400">${item.size}</p>
            </div>
        </div>
    `).join('');
}

// Get type icon
function getTypeIcon(type) {
    const icons = {
        image: '🖼️',
        video: '🎥',
        audio: '🎵',
        document: '📄',
        file: '📁'
    };
    return icons[type] || icons.file;
}

// Handle file upload
document.getElementById('uploadForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const files = document.getElementById('fileInput').files;
    
    if (files.length === 0) {
        alert('Please select at least one file');
        return;
    }
    
    // Show upload progress
    document.getElementById('uploadProgress').classList.remove('hidden');
    document.getElementById('uploadSpinner').classList.remove('hidden');
    document.getElementById('uploadButtonText').textContent = 'Uploading...';
    
    try {
        for (let i = 0; i < files.length; i++) {
            const fileFormData = new FormData();
            fileFormData.append('file', files[i]);
            fileFormData.append('collection', formData.get('collection'));
            fileFormData.append('description', formData.get('description'));
            fileFormData.append('is_public', formData.get('is_public') ? '1' : '0');
            fileFormData.append('_token', formData.get('_token'));
            
            const response = await fetch('/dashboard/media/upload', {
                method: 'POST',
                body: fileFormData
            });
            
            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Upload failed');
            }
            
            // Update progress
            const progress = ((i + 1) / files.length) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('progressText').textContent = `Uploaded ${i + 1} of ${files.length} files`;
        }
        
        // Success
        closeUploadModal();
        loadMedia(); // Reload media grid
        
    } catch (error) {
        alert('Upload failed: ' + error.message);
    } finally {
        document.getElementById('uploadSpinner').classList.add('hidden');
        document.getElementById('uploadButtonText').textContent = 'Upload';
    }
});

// Load media details
async function loadMediaDetails(mediaId) {
    try {
        const response = await fetch(`/dashboard/media/${mediaId}`);
        const data = await response.json();
        
        if (data.success) {
            const media = data.media;
            document.getElementById('mediaContent').innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        ${media.type === 'image' ? 
                            `<img src="${media.url}" alt="${media.name}" class="w-full rounded-lg">` :
                            `<div class="bg-gray-700 rounded-lg p-8 text-center">
                                <div class="text-6xl mb-4">${getTypeIcon(media.type)}</div>
                                <p class="text-gray-400">${media.mime_type}</p>
                            </div>`
                        }
                    </div>
                    <div>
                        <h4 class="font-semibold mb-4">File Information</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-400">Name:</span>
                                <span>${media.name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Size:</span>
                                <span>${media.size}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Type:</span>
                                <span>${media.mime_type}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">Uploaded:</span>
                                <span>${media.uploaded_at}</span>
                            </div>
                        </div>
                        
                        <div class="mt-6 space-y-3">
                            <a href="${media.url}" target="_blank" class="btn-primary w-full text-center block">
                                View Full Size
                            </a>
                            <button onclick="copyToClipboard('${media.url}')" class="btn-secondary w-full">
                                Copy URL
                            </button>
                            <button onclick="deleteMedia(${media.id})" class="btn-secondary w-full text-red-400 hover:text-red-300">
                                Delete File
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading media details:', error);
    }
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        alert('URL copied to clipboard!');
    });
}

// Delete media
async function deleteMedia(mediaId) {
    if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch(`/dashboard/media/${mediaId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            closeMediaModal();
            loadMedia(); // Reload media grid
        } else {
            alert('Delete failed: ' + data.message);
        }
    } catch (error) {
        alert('Delete failed: ' + error.message);
    }
}

// Render pagination
function renderPagination(pagination) {
    const container = document.getElementById('pagination');
    
    if (pagination.last_page <= 1) {
        container.classList.add('hidden');
        return;
    }
    
    container.classList.remove('hidden');
    
    let html = '';
    
    // Previous button
    if (pagination.current_page > 1) {
        html += `<button onclick="loadMedia(${pagination.current_page - 1})" class="btn-secondary">Previous</button>`;
    }
    
    // Page numbers
    for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.last_page, pagination.current_page + 2); i++) {
        if (i === pagination.current_page) {
            html += `<button class="btn-primary">${i}</button>`;
        } else {
            html += `<button onclick="loadMedia(${i})" class="btn-secondary">${i}</button>`;
        }
    }
    
    // Next button
    if (pagination.current_page < pagination.last_page) {
        html += `<button onclick="loadMedia(${pagination.current_page + 1})" class="btn-secondary">Next</button>`;
    }
    
    container.innerHTML = html;
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => loadMedia(), 500);
});
</script>
@endsection
