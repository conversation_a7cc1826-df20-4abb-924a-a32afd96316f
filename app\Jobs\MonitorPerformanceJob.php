<?php

namespace App\Jobs;

use App\Services\CacheService;
use App\Services\DatabaseOptimizationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class MonitorPerformanceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $thresholds;
    protected bool $sendAlerts;

    /**
     * Create a new job instance.
     */
    public function __construct(array $thresholds = [], bool $sendAlerts = true)
    {
        $this->thresholds = array_merge([
            'response_time' => 2000, // milliseconds
            'memory_usage' => 80, // percentage
            'cache_hit_rate' => 70, // percentage
            'database_connections' => 80, // percentage of max
            'queue_size' => 1000, // number of jobs
        ], $thresholds);
        
        $this->sendAlerts = $sendAlerts;
    }

    /**
     * Execute the job.
     */
    public function handle(CacheService $cacheService, DatabaseOptimizationService $dbService): void
    {
        $metrics = $this->collectMetrics($cacheService, $dbService);
        $alerts = $this->analyzeMetrics($metrics);
        
        // Store metrics for historical analysis
        $this->storeMetrics($metrics);
        
        // Send alerts if any issues detected
        if (!empty($alerts) && $this->sendAlerts) {
            $this->sendPerformanceAlerts($alerts, $metrics);
        }
        
        // Log performance summary
        $this->logPerformanceSummary($metrics, $alerts);
    }

    /**
     * Collect performance metrics
     */
    protected function collectMetrics(CacheService $cacheService, DatabaseOptimizationService $dbService): array
    {
        $startTime = microtime(true);
        
        $metrics = [
            'timestamp' => now(),
            'response_time' => $this->measureResponseTime(),
            'memory_usage' => $this->getMemoryUsage(),
            'cache_metrics' => $this->getCacheMetrics($cacheService),
            'database_metrics' => $this->getDatabaseMetrics($dbService),
            'queue_metrics' => $this->getQueueMetrics(),
            'system_metrics' => $this->getSystemMetrics(),
        ];
        
        $metrics['collection_time'] = (microtime(true) - $startTime) * 1000;
        
        return $metrics;
    }

    /**
     * Measure average response time
     */
    protected function measureResponseTime(): array
    {
        $times = [];
        $urls = [
            '/',
            '/courses',
            '/events',
            '/dashboard',
        ];

        foreach ($urls as $url) {
            $start = microtime(true);
            
            try {
                // Simulate internal request
                $response = app('router')->dispatch(
                    \Illuminate\Http\Request::create($url, 'GET')
                );
                
                $times[$url] = (microtime(true) - $start) * 1000;
            } catch (\Exception $e) {
                $times[$url] = null;
                Log::warning("Failed to measure response time for {$url}: " . $e->getMessage());
            }
        }

        return [
            'individual' => $times,
            'average' => array_sum(array_filter($times)) / count(array_filter($times)),
            'max' => max(array_filter($times)),
        ];
    }

    /**
     * Get memory usage metrics
     */
    protected function getMemoryUsage(): array
    {
        $current = memory_get_usage(true);
        $peak = memory_get_peak_usage(true);
        $limit = $this->parseMemoryLimit(ini_get('memory_limit'));

        return [
            'current' => $current,
            'peak' => $peak,
            'limit' => $limit,
            'current_percentage' => ($current / $limit) * 100,
            'peak_percentage' => ($peak / $limit) * 100,
        ];
    }

    /**
     * Get cache performance metrics
     */
    protected function getCacheMetrics(CacheService $cacheService): array
    {
        $stats = $cacheService->getCacheStatistics();
        
        return [
            'hit_rate' => $stats['hit_rate'] ?? 0,
            'miss_rate' => $stats['miss_rate'] ?? 0,
            'total_keys' => $stats['total_keys'] ?? 0,
            'memory_usage' => $stats['memory_usage'] ?? 0,
            'evictions' => $stats['evictions'] ?? 0,
        ];
    }

    /**
     * Get database performance metrics
     */
    protected function getDatabaseMetrics(DatabaseOptimizationService $dbService): array
    {
        try {
            $connections = $this->getActiveConnections();
            $maxConnections = $this->getMaxConnections();
            $slowQueries = $this->getSlowQueryCount();
            
            return [
                'active_connections' => $connections,
                'max_connections' => $maxConnections,
                'connection_percentage' => ($connections / $maxConnections) * 100,
                'slow_queries' => $slowQueries,
                'table_sizes' => $this->getTableSizes(),
            ];
        } catch (\Exception $e) {
            Log::error('Failed to collect database metrics: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get queue performance metrics
     */
    protected function getQueueMetrics(): array
    {
        try {
            $defaultQueue = config('queue.default');
            
            $metrics = [
                'default_driver' => $defaultQueue,
                'pending_jobs' => 0,
                'failed_jobs' => DB::table('failed_jobs')->count(),
            ];

            // Get pending jobs count based on driver
            switch ($defaultQueue) {
                case 'database':
                    $metrics['pending_jobs'] = DB::table('jobs')->count();
                    break;
                case 'redis':
                    // Redis queue size would need Redis connection
                    break;
            }

            return $metrics;
        } catch (\Exception $e) {
            Log::error('Failed to collect queue metrics: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get system performance metrics
     */
    protected function getSystemMetrics(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_load' => $this->getServerLoad(),
            'disk_usage' => $this->getDiskUsage(),
            'opcache_status' => $this->getOpcacheStatus(),
        ];
    }

    /**
     * Analyze metrics and generate alerts
     */
    protected function analyzeMetrics(array $metrics): array
    {
        $alerts = [];

        // Check response time
        if (($metrics['response_time']['average'] ?? 0) > $this->thresholds['response_time']) {
            $alerts[] = [
                'type' => 'response_time',
                'severity' => 'warning',
                'message' => 'Average response time is above threshold',
                'value' => $metrics['response_time']['average'],
                'threshold' => $this->thresholds['response_time'],
            ];
        }

        // Check memory usage
        if (($metrics['memory_usage']['current_percentage'] ?? 0) > $this->thresholds['memory_usage']) {
            $alerts[] = [
                'type' => 'memory_usage',
                'severity' => 'critical',
                'message' => 'Memory usage is above threshold',
                'value' => $metrics['memory_usage']['current_percentage'],
                'threshold' => $this->thresholds['memory_usage'],
            ];
        }

        // Check cache hit rate
        if (($metrics['cache_metrics']['hit_rate'] ?? 100) < $this->thresholds['cache_hit_rate']) {
            $alerts[] = [
                'type' => 'cache_hit_rate',
                'severity' => 'warning',
                'message' => 'Cache hit rate is below threshold',
                'value' => $metrics['cache_metrics']['hit_rate'],
                'threshold' => $this->thresholds['cache_hit_rate'],
            ];
        }

        // Check database connections
        if (($metrics['database_metrics']['connection_percentage'] ?? 0) > $this->thresholds['database_connections']) {
            $alerts[] = [
                'type' => 'database_connections',
                'severity' => 'critical',
                'message' => 'Database connection usage is above threshold',
                'value' => $metrics['database_metrics']['connection_percentage'],
                'threshold' => $this->thresholds['database_connections'],
            ];
        }

        // Check queue size
        if (($metrics['queue_metrics']['pending_jobs'] ?? 0) > $this->thresholds['queue_size']) {
            $alerts[] = [
                'type' => 'queue_size',
                'severity' => 'warning',
                'message' => 'Queue size is above threshold',
                'value' => $metrics['queue_metrics']['pending_jobs'],
                'threshold' => $this->thresholds['queue_size'],
            ];
        }

        return $alerts;
    }

    /**
     * Store metrics for historical analysis
     */
    protected function storeMetrics(array $metrics): void
    {
        try {
            // Store in cache for recent access
            Cache::put('performance_metrics_latest', $metrics, now()->addHours(1));
            
            // Store in database for historical analysis
            DB::table('performance_metrics')->insert([
                'timestamp' => $metrics['timestamp'],
                'metrics' => json_encode($metrics),
                'created_at' => now(),
            ]);
            
            // Clean up old metrics (keep last 30 days)
            DB::table('performance_metrics')
                ->where('created_at', '<', now()->subDays(30))
                ->delete();
                
        } catch (\Exception $e) {
            Log::error('Failed to store performance metrics: ' . $e->getMessage());
        }
    }

    /**
     * Send performance alerts
     */
    protected function sendPerformanceAlerts(array $alerts, array $metrics): void
    {
        $criticalAlerts = array_filter($alerts, fn($alert) => $alert['severity'] === 'critical');
        
        if (!empty($criticalAlerts)) {
            // Send immediate notification for critical alerts
            $this->sendCriticalAlert($criticalAlerts, $metrics);
        }
        
        // Log all alerts
        foreach ($alerts as $alert) {
            Log::warning('Performance alert: ' . $alert['message'], $alert);
        }
    }

    /**
     * Send critical performance alert
     */
    protected function sendCriticalAlert(array $alerts, array $metrics): void
    {
        $adminEmail = config('mail.admin_email', '<EMAIL>');
        
        try {
            Mail::raw(
                $this->formatAlertEmail($alerts, $metrics),
                function ($message) use ($adminEmail) {
                    $message->to($adminEmail)
                           ->subject('Critical Performance Alert - ' . config('app.name'));
                }
            );
        } catch (\Exception $e) {
            Log::error('Failed to send performance alert email: ' . $e->getMessage());
        }
    }

    /**
     * Format alert email content
     */
    protected function formatAlertEmail(array $alerts, array $metrics): string
    {
        $content = "Critical Performance Alert\n";
        $content .= "========================\n\n";
        $content .= "Time: " . $metrics['timestamp'] . "\n";
        $content .= "Server: " . config('app.url') . "\n\n";
        
        foreach ($alerts as $alert) {
            $content .= "Alert: {$alert['message']}\n";
            $content .= "Type: {$alert['type']}\n";
            $content .= "Severity: {$alert['severity']}\n";
            $content .= "Current Value: {$alert['value']}\n";
            $content .= "Threshold: {$alert['threshold']}\n\n";
        }
        
        $content .= "Please investigate immediately.\n";
        
        return $content;
    }

    /**
     * Log performance summary
     */
    protected function logPerformanceSummary(array $metrics, array $alerts): void
    {
        $summary = [
            'timestamp' => $metrics['timestamp'],
            'response_time_avg' => $metrics['response_time']['average'] ?? 0,
            'memory_usage_pct' => $metrics['memory_usage']['current_percentage'] ?? 0,
            'cache_hit_rate' => $metrics['cache_metrics']['hit_rate'] ?? 0,
            'alerts_count' => count($alerts),
            'critical_alerts' => count(array_filter($alerts, fn($a) => $a['severity'] === 'critical')),
        ];
        
        Log::info('Performance monitoring summary', $summary);
    }

    // Helper methods
    protected function getActiveConnections(): int
    {
        $result = DB::select('SHOW STATUS LIKE "Threads_connected"');
        return $result[0]->Value ?? 0;
    }

    protected function getMaxConnections(): int
    {
        $result = DB::select('SHOW VARIABLES LIKE "max_connections"');
        return $result[0]->Value ?? 151;
    }

    protected function getSlowQueryCount(): int
    {
        $result = DB::select('SHOW STATUS LIKE "Slow_queries"');
        return $result[0]->Value ?? 0;
    }

    protected function getTableSizes(): array
    {
        try {
            $results = DB::select("
                SELECT table_name, 
                       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY size_mb DESC
                LIMIT 10
            ");
            
            return collect($results)->pluck('size_mb', 'table_name')->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    protected function getServerLoad(): ?float
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return $load[0] ?? null;
        }
        return null;
    }

    protected function getDiskUsage(): ?float
    {
        $path = base_path();
        if (function_exists('disk_free_space') && function_exists('disk_total_space')) {
            $free = disk_free_space($path);
            $total = disk_total_space($path);
            return $total > 0 ? (($total - $free) / $total) * 100 : null;
        }
        return null;
    }

    protected function getOpcacheStatus(): ?array
    {
        if (function_exists('opcache_get_status')) {
            return opcache_get_status();
        }
        return null;
    }

    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;

        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }

        return $limit;
    }
}
