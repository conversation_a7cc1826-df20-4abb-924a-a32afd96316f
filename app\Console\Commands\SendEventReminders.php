<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Event;
use App\Models\EventRsvp;
use App\Notifications\EventReminder;
use Carbon\Carbon;

class SendEventReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'events:send-reminders {--type=24h : Reminder type (1h, 24h, 1w)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send event reminders to users who have RSVP\'d';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        $this->info("Sending {$type} event reminders...");

        // Calculate the time range based on reminder type
        $timeRange = $this->getTimeRange($type);

        if (!$timeRange) {
            $this->error('Invalid reminder type. Use: 1h, 24h, or 1w');
            return 1;
        }

        // Find events that need reminders
        $events = Event::where('start_time', '>=', $timeRange['start'])
                      ->where('start_time', '<=', $timeRange['end'])
                      ->where('is_active', true)
                      ->get();

        $remindersSent = 0;

        foreach ($events as $event) {
            // Get users who have RSVP'd to this event
            $rsvps = EventRsvp::where('event_id', $event->id)
                             ->where('rsvp_status', 'going')
                             ->with('user')
                             ->get();

            foreach ($rsvps as $rsvp) {
                if ($rsvp->user) {
                    // Check if reminder was already sent for this type
                    $alreadySent = $rsvp->user->notifications()
                        ->where('type', EventReminder::class)
                        ->where('data->event_id', $event->id)
                        ->where('data->reminder_type', $type)
                        ->exists();

                    if (!$alreadySent) {
                        $rsvp->user->notify(new EventReminder($event, $type));
                        $remindersSent++;
                    }
                }
            }
        }

        $this->info("Sent {$remindersSent} event reminders for {$events->count()} events.");
        return 0;
    }

    /**
     * Get time range for reminder type
     */
    private function getTimeRange(string $type): ?array
    {
        $now = Carbon::now();

        return match ($type) {
            '1h' => [
                'start' => $now->copy()->addHour()->subMinutes(5),
                'end' => $now->copy()->addHour()->addMinutes(5),
            ],
            '24h' => [
                'start' => $now->copy()->addDay()->subMinutes(30),
                'end' => $now->copy()->addDay()->addMinutes(30),
            ],
            '1w' => [
                'start' => $now->copy()->addWeek()->subHours(2),
                'end' => $now->copy()->addWeek()->addHours(2),
            ],
            default => null,
        };
    }
}
