<?php $__env->startSection('title', 'Subscription Plans - The Real World'); ?>
<?php $__env->startSection('page-title', 'Subscription Plans'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Current Subscription Status -->
    <?php if($hasActiveSubscription): ?>
    <div class="card mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-semibold mb-2">Current Subscription</h3>
                <p class="text-gray-400">You are currently subscribed to the <?php echo e(ucfirst($currentPlan)); ?> plan</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold text-primary-400 mb-1"><?php echo e(ucfirst($currentPlan)); ?></div>
                <?php if($user->subscription('default')->cancelled()): ?>
                    <p class="text-red-400 text-sm">Cancelled - Expires <?php echo e($user->subscription('default')->ends_at->format('M j, Y')); ?></p>
                <?php else: ?>
                    <p class="text-green-400 text-sm">Active - Renews <?php echo e($user->subscription('default')->asStripeSubscription()->current_period_end ? \Carbon\Carbon::createFromTimestamp($user->subscription('default')->asStripeSubscription()->current_period_end)->format('M j, Y') : 'N/A'); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <div class="mt-6 flex items-center space-x-4">
            <?php if($user->subscription('default')->cancelled()): ?>
                <form action="<?php echo e(route('dashboard.subscription.resume-subscription')); ?>" method="POST" class="inline">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="btn-primary">Resume Subscription</button>
                </form>
            <?php else: ?>
                <form action="<?php echo e(route('dashboard.subscription.cancel-subscription')); ?>" method="POST" class="inline" 
                      onsubmit="return confirm('Are you sure you want to cancel your subscription? You will continue to have access until the end of your billing period.')">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="btn-secondary text-red-400 hover:text-red-300">Cancel Subscription</button>
                </form>
            <?php endif; ?>
            
            <a href="<?php echo e(route('dashboard.settings.billing')); ?>" class="btn-secondary">
                View Billing History
            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- Plans Grid -->
    <div class="mb-8">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold mb-4">Choose Your Plan</h2>
            <p class="text-gray-400 max-w-2xl mx-auto">
                Unlock your potential with our comprehensive learning programs. Each plan is designed to accelerate your success.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Free Plan -->
            <div class="card <?php echo e($currentPlan === 'free' ? 'ring-2 ring-primary-500' : ''); ?>">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold mb-2">Free</h3>
                    <div class="text-3xl font-bold mb-4">$0<span class="text-lg text-gray-400">/month</span></div>
                    <p class="text-gray-400">Get started with basic access</p>
                </div>

                <ul class="space-y-3 mb-8">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Access to basic courses
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Community chat access
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Basic support
                    </li>
                </ul>

                <?php if($currentPlan === 'free'): ?>
                    <button class="btn-secondary w-full" disabled>Current Plan</button>
                <?php else: ?>
                    <form action="<?php echo e(route('dashboard.subscription.cancel-subscription')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="btn-secondary w-full">Downgrade to Free</button>
                    </form>
                <?php endif; ?>
            </div>

            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $planKey => $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <!-- <?php echo e(ucfirst($planKey)); ?> Plan -->
            <div class="card relative <?php echo e($plan['popular'] ? 'ring-2 ring-primary-500' : ''); ?> <?php echo e($currentPlan === $planKey ? 'ring-2 ring-green-500' : ''); ?>">
                <?php if($plan['popular']): ?>
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">Most Popular</span>
                    </div>
                <?php endif; ?>

                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold mb-2"><?php echo e($plan['name']); ?></h3>
                    <div class="text-3xl font-bold mb-4">$<?php echo e($plan['price']); ?><span class="text-lg text-gray-400">/month</span></div>
                    <p class="text-gray-400"><?php echo e($plan['name']); ?> your success</p>
                </div>

                <ul class="space-y-3 mb-8">
                    <?php $__currentLoopData = $plan['features']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <?php echo e($feature); ?>

                    </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>

                <?php if($currentPlan === $planKey): ?>
                    <button class="btn-primary w-full" disabled>Current Plan</button>
                <?php elseif($hasActiveSubscription): ?>
                    <form action="<?php echo e(route('subscription.change-plan')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="plan" value="<?php echo e($planKey); ?>">
                        <button type="submit" class="btn-primary w-full">
                            <?php if($currentPlan === 'free' || array_search($planKey, array_keys($plans)) > array_search($currentPlan, array_keys($plans))): ?>
                                Upgrade to <?php echo e($plan['name']); ?>

                            <?php else: ?>
                                Downgrade to <?php echo e($plan['name']); ?>

                            <?php endif; ?>
                        </button>
                    </form>
                <?php else: ?>
                    <form action="<?php echo e(route('subscription.checkout')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="plan" value="<?php echo e($planKey); ?>">
                        <button type="submit" class="btn-primary w-full">Get Started</button>
                    </form>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Features Comparison -->
    <div class="card">
        <h3 class="text-2xl font-bold mb-6 text-center">Feature Comparison</h3>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="border-b border-gray-700">
                        <th class="text-left py-4 px-6">Features</th>
                        <th class="text-center py-4 px-6">Free</th>
                        <th class="text-center py-4 px-6">Prosper</th>
                        <th class="text-center py-4 px-6">Conquer</th>
                        <th class="text-center py-4 px-6">Champions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
                    <tr>
                        <td class="py-4 px-6 font-medium">Basic Courses</td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-4 px-6 font-medium">Premium Courses</td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-red-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-4 px-6 font-medium">Live Sessions</td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-red-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center text-sm">Monthly</td>
                        <td class="py-4 px-6 text-center text-sm">Weekly</td>
                        <td class="py-4 px-6 text-center text-sm">Daily</td>
                    </tr>
                    <tr>
                        <td class="py-4 px-6 font-medium">1-on-1 Sessions</td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-red-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-red-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr>
                        <td class="py-4 px-6 font-medium">Priority Support</td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-red-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="py-4 px-6 text-center">
                            <svg class="w-5 h-5 text-green-400 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="mt-8 card">
        <h3 class="text-2xl font-bold mb-6 text-center">Frequently Asked Questions</h3>
        
        <div class="space-y-4">
            <div class="border-b border-gray-700 pb-4">
                <h4 class="font-semibold mb-2">Can I change my plan anytime?</h4>
                <p class="text-gray-400">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately for upgrades and at the end of your billing cycle for downgrades.</p>
            </div>
            
            <div class="border-b border-gray-700 pb-4">
                <h4 class="font-semibold mb-2">What happens if I cancel my subscription?</h4>
                <p class="text-gray-400">You'll continue to have access to your plan features until the end of your current billing period. After that, your account will revert to the free plan.</p>
            </div>
            
            <div class="border-b border-gray-700 pb-4">
                <h4 class="font-semibold mb-2">Do you offer refunds?</h4>
                <p class="text-gray-400">We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, contact our support team for a full refund.</p>
            </div>
            
            <div>
                <h4 class="font-semibold mb-2">Is there a free trial?</h4>
                <p class="text-gray-400">Yes! All paid plans come with a 7-day free trial. You can cancel anytime during the trial period without being charged.</p>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/subscription/plans.blade.php ENDPATH**/ ?>