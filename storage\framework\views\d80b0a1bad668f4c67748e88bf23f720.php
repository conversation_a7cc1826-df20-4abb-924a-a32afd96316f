<?php $__env->startSection('title', 'Campuses - The Real World'); ?>
<?php $__env->startSection('page-title', 'Campuses'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">Choose Your Learning Path</h2>
        <p class="text-gray-400">Select a campus to start learning real-world skills that generate income.</p>
    </div>

    <!-- Accessible Campuses -->
    <?php if($accessibleCampuses->count() > 0): ?>
    <div class="mb-12">
        <h3 class="text-xl font-semibold mb-6 flex items-center">
            <svg class="w-6 h-6 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Available Campuses
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $accessibleCampuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card hover:transform hover:scale-105 transition-all duration-300 cursor-pointer group">
                <a href="<?php echo e(route('dashboard.campuses.show', $campus)); ?>" class="block">
                    <!-- Campus Header -->
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 rounded-xl mr-4 flex items-center justify-center text-3xl flex-shrink-0" 
                             style="background-color: <?php echo e($campus->color); ?>20; border: 2px solid <?php echo e($campus->color); ?>;">
                            <?php if($campus->icon_url): ?>
                                <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-10 h-10">
                            <?php else: ?>
                                <span style="color: <?php echo e($campus->color); ?>">🎯</span>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-xl font-bold group-hover:text-primary-400 transition-colors">
                                <?php echo e($campus->name); ?>

                            </h4>
                            <p class="text-sm text-gray-400"><?php echo e($campus->courses->count()); ?> courses available</p>
                        </div>
                    </div>

                    <!-- Description -->
                    <p class="text-gray-300 mb-4 leading-relaxed">
                        <?php echo e($campus->teaser_description ?: Str::limit($campus->description, 120)); ?>

                    </p>

                    <!-- Progress Bar -->
                    <?php
                        $totalCourses = $campus->courses->count();
                        $completedCourses = $campus->courses->filter(function($course) use ($user) {
                            return $course->isCompletedBy($user);
                        })->count();
                        $progressPercentage = $totalCourses > 0 ? ($completedCourses / $totalCourses) * 100 : 0;
                    ?>
                    
                    <div class="mb-4">
                        <div class="flex items-center justify-between text-sm text-gray-400 mb-2">
                            <span>Progress</span>
                            <span><?php echo e($completedCourses); ?>/<?php echo e($totalCourses); ?> courses</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="h-2 rounded-full transition-all duration-300" 
                                 style="width: <?php echo e($progressPercentage); ?>%; background-color: <?php echo e($campus->color); ?>"></div>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium" style="color: <?php echo e($campus->color); ?>">
                            <?php echo e($progressPercentage > 0 ? 'Continue Learning' : 'Start Learning'); ?>

                        </span>
                        <svg class="w-5 h-5 text-gray-400 group-hover:text-primary-400 transition-colors" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </a>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Restricted Campuses -->
    <?php if($restrictedCampuses->count() > 0): ?>
    <div>
        <h3 class="text-xl font-semibold mb-6 flex items-center">
            <svg class="w-6 h-6 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
            Premium Campuses
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $restrictedCampuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card relative opacity-75 hover:opacity-90 transition-opacity">
                <!-- Premium Badge -->
                <div class="absolute top-4 right-4 z-10">
                    <span class="bg-yellow-600 text-white text-xs font-semibold px-2 py-1 rounded-full flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                        </svg>
                        Premium
                    </span>
                </div>

                <!-- Campus Header -->
                <div class="flex items-center mb-4">
                    <div class="w-16 h-16 rounded-xl mr-4 flex items-center justify-center text-3xl flex-shrink-0" 
                         style="background-color: <?php echo e($campus->color); ?>20; border: 2px solid <?php echo e($campus->color); ?>;">
                        <?php if($campus->icon_url): ?>
                            <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-10 h-10 opacity-60">
                        <?php else: ?>
                            <span style="color: <?php echo e($campus->color); ?>" class="opacity-60">🎯</span>
                        <?php endif; ?>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-xl font-bold text-gray-300"><?php echo e($campus->name); ?></h4>
                        <p class="text-sm text-gray-500"><?php echo e($campus->courses->count()); ?> courses available</p>
                    </div>
                </div>

                <!-- Description -->
                <p class="text-gray-400 mb-4 leading-relaxed">
                    <?php echo e($campus->teaser_description ?: Str::limit($campus->description, 120)); ?>

                </p>

                <!-- Required Plans -->
                <?php if($campus->required_plans): ?>
                <div class="mb-4">
                    <p class="text-xs text-gray-500 mb-2">Required plan:</p>
                    <div class="flex flex-wrap gap-1">
                        <?php $__currentLoopData = $campus->required_plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span class="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded">
                            <?php echo e(ucfirst($plan)); ?>

                        </span>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Upgrade Button -->
                <a href="<?php echo e(route('subscription.plans')); ?>" 
                   class="btn-primary w-full text-center">
                    Upgrade to Access
                </a>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Empty State -->
    <?php if($accessibleCampuses->count() === 0 && $restrictedCampuses->count() === 0): ?>
    <div class="text-center py-12">
        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">No Campuses Available</h3>
        <p class="text-gray-400 mb-6">Check back soon for new learning opportunities!</p>
        <a href="<?php echo e(route('home')); ?>" class="btn-primary">
            Back to Homepage
        </a>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/campuses/index.blade.php ENDPATH**/ ?>