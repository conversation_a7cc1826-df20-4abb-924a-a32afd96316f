@extends('layouts.dashboard')

@section('title', 'Badges & Achievements - The Real World')
@section('page-title', 'Badges & Achievements')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">Your Badges & Achievements</h2>
        <p class="text-gray-400">Track your progress and unlock exclusive rewards</p>
    </div>

    <!-- Achievement Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="card text-center">
            <div class="text-3xl mb-2">🏆</div>
            <div class="text-2xl font-bold text-primary-400">{{ $userBadges->count() }}</div>
            <p class="text-gray-400 text-sm">Badges Earned</p>
        </div>
        <div class="card text-center">
            <div class="text-3xl mb-2">⭐</div>
            <div class="text-2xl font-bold text-yellow-400">{{ $achievements->count() }}</div>
            <p class="text-gray-400 text-sm">Achievements</p>
        </div>
        <div class="card text-center">
            <div class="text-3xl mb-2">🎯</div>
            <div class="text-2xl font-bold text-green-400">{{ auth()->user()->xp }}</div>
            <p class="text-gray-400 text-sm">Total XP</p>
        </div>
        <div class="card text-center">
            <div class="text-3xl mb-2">📈</div>
            <div class="text-2xl font-bold text-blue-400">{{ auth()->user()->level }}</div>
            <p class="text-gray-400 text-sm">Current Level</p>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6" x-data="{ activeTab: 'badges' }">
        <div class="flex space-x-1 bg-gray-800 p-1 rounded-lg">
            <button @click="activeTab = 'badges'" 
                    :class="activeTab === 'badges' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                    class="flex-1 py-2 px-4 rounded-md transition-colors">
                Badges
            </button>
            <button @click="activeTab = 'achievements'" 
                    :class="activeTab === 'achievements' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                    class="flex-1 py-2 px-4 rounded-md transition-colors">
                Achievements
            </button>
            <button @click="activeTab = 'progress'" 
                    :class="activeTab === 'progress' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                    class="flex-1 py-2 px-4 rounded-md transition-colors">
                Progress
            </button>
        </div>

        <!-- Earned Badges Tab -->
        <div x-show="activeTab === 'badges'" class="mt-6">
            <h3 class="text-lg font-semibold mb-4">Earned Badges ({{ $userBadges->count() }})</h3>
            
            @if($userBadges->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    @foreach($userBadges as $badge)
                        <div class="card hover:bg-gray-700 transition-colors">
                            <div class="flex items-center space-x-4">
                                <div class="text-4xl">{{ $badge->icon ?? '🏆' }}</div>
                                <div class="flex-1">
                                    <h4 class="font-semibold">{{ $badge->name }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $badge->description }}</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span>Earned {{ $badge->pivot->earned_at->diffForHumans() }}</span>
                                        @if($badge->xp_reward)
                                            <span class="ml-2 bg-primary-600 px-2 py-1 rounded">+{{ $badge->xp_reward }} XP</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">🏆</div>
                    <h3 class="text-xl font-semibold mb-2">No badges earned yet</h3>
                    <p class="text-gray-400">Complete courses and activities to earn your first badge!</p>
                </div>
            @endif

            <!-- Available Badges -->
            <h3 class="text-lg font-semibold mb-4">Available Badges ({{ $availableBadges->count() }})</h3>
            
            @if($availableBadges->count() > 0)
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($availableBadges as $badge)
                        <div class="card opacity-75 hover:opacity-100 transition-opacity">
                            <div class="flex items-center space-x-4">
                                <div class="text-4xl grayscale">{{ $badge->icon ?? '🏆' }}</div>
                                <div class="flex-1">
                                    <h4 class="font-semibold">{{ $badge->name }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $badge->description }}</p>
                                    @if(isset($badgeProgress[$badge->id]))
                                        <div class="mt-2">
                                            @foreach($badgeProgress[$badge->id] as $req => $progress)
                                                <div class="text-xs text-gray-500 mb-1">
                                                    {{ ucfirst(str_replace('_', ' ', $req)) }}: {{ $progress['current'] }}/{{ $progress['target'] }}
                                                </div>
                                                <div class="w-full bg-gray-700 rounded-full h-1 mb-2">
                                                    <div class="bg-primary-600 h-1 rounded-full" style="width: {{ $progress['percentage'] }}%"></div>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                    @if($badge->xp_reward)
                                        <span class="inline-block mt-2 bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">+{{ $badge->xp_reward }} XP</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif
        </div>

        <!-- Achievements Tab -->
        <div x-show="activeTab === 'achievements'" class="mt-6">
            <h3 class="text-lg font-semibold mb-4">Recent Achievements ({{ $achievements->count() }})</h3>
            
            @if($achievements->count() > 0)
                <div class="space-y-4">
                    @foreach($achievements->take(20) as $achievement)
                        <div class="card">
                            <div class="flex items-center space-x-4">
                                <div class="text-3xl">{{ $achievement->icon ?? '⭐' }}</div>
                                <div class="flex-1">
                                    <h4 class="font-semibold">{{ $achievement->achievement_name }}</h4>
                                    <p class="text-gray-400 text-sm">{{ $achievement->description }}</p>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span>{{ $achievement->earned_at->format('M j, Y') }}</span>
                                        @if($achievement->xp_reward)
                                            <span class="ml-2 bg-green-600 px-2 py-1 rounded">+{{ $achievement->xp_reward }} XP</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">⭐</div>
                    <h3 class="text-xl font-semibold mb-2">No achievements yet</h3>
                    <p class="text-gray-400">Start learning and engaging to unlock achievements!</p>
                </div>
            @endif
        </div>

        <!-- Progress Tab -->
        <div x-show="activeTab === 'progress'" class="mt-6">
            <h3 class="text-lg font-semibold mb-4">Your Progress</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Level Progress -->
                <div class="card">
                    <h4 class="font-semibold mb-4">Level Progress</h4>
                    <div class="text-center mb-4">
                        <div class="text-4xl font-bold text-primary-400">{{ auth()->user()->level }}</div>
                        <p class="text-gray-400">Current Level</p>
                    </div>
                    @php
                        $currentXp = auth()->user()->xp;
                        $currentLevel = auth()->user()->level;
                        $xpForCurrentLevel = ($currentLevel - 1) * 1000;
                        $xpForNextLevel = $currentLevel * 1000;
                        $progressToNext = $currentXp - $xpForCurrentLevel;
                        $xpNeededForNext = $xpForNextLevel - $xpForCurrentLevel;
                        $progressPercentage = ($progressToNext / $xpNeededForNext) * 100;
                    @endphp
                    <div class="mb-2">
                        <div class="flex justify-between text-sm text-gray-400">
                            <span>{{ $progressToNext }} XP</span>
                            <span>{{ $xpNeededForNext }} XP</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-3">
                            <div class="bg-gradient-to-r from-primary-600 to-primary-400 h-3 rounded-full transition-all duration-500" 
                                 style="width: {{ $progressPercentage }}%"></div>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-400">
                        {{ $xpNeededForNext - $progressToNext }} XP to level {{ $currentLevel + 1 }}
                    </p>
                </div>

                <!-- Activity Summary -->
                <div class="card">
                    <h4 class="font-semibold mb-4">Activity Summary</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Courses Completed</span>
                            <span class="font-semibold">{{ auth()->user()->courseEnrollments()->where('progress_percentage', 100)->count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Events Attended</span>
                            <span class="font-semibold">{{ auth()->user()->eventRsvps()->where('attended', true)->count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Login Streak</span>
                            <span class="font-semibold">{{ auth()->user()->login_streak ?? 0 }} days</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Total XP</span>
                            <span class="font-semibold text-primary-400">{{ number_format(auth()->user()->xp) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.grayscale {
    filter: grayscale(100%);
}
</style>
@endsection
