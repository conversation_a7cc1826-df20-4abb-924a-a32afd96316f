<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('avatar_url')->nullable()->after('email');
            $table->text('bio')->nullable()->after('avatar_url');
            $table->string('location')->nullable()->after('bio');
            $table->string('social_twitter')->nullable()->after('location');
            $table->string('social_instagram')->nullable()->after('social_twitter');
            $table->string('social_linkedin')->nullable()->after('social_instagram');
            $table->integer('xp')->default(0)->after('social_linkedin');
            $table->integer('level')->default(1)->after('xp');
            $table->string('subscription_plan')->nullable()->after('level');
            // trial_ends_at already exists from Cashier migration
            $table->boolean('is_active')->default(true)->after('trial_ends_at');
            $table->timestamp('last_activity_at')->nullable()->after('is_active');
            $table->string('referral_code', 20)->unique()->nullable()->after('last_activity_at');
            $table->unsignedBigInteger('referred_by')->nullable()->after('referral_code');

            $table->foreign('referred_by')->references('id')->on('users')->onDelete('set null');
            $table->index(['xp', 'level']);
            $table->index('subscription_plan');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['referred_by']);
            $table->dropIndex(['xp', 'level']);
            $table->dropIndex(['subscription_plan']);
            $table->dropIndex(['is_active']);

            $table->dropColumn([
                'avatar_url', 'bio', 'location', 'social_twitter', 'social_instagram',
                'social_linkedin', 'xp', 'level', 'subscription_plan',
                'is_active', 'last_activity_at', 'referral_code', 'referred_by'
            ]);
        });
    }
};
