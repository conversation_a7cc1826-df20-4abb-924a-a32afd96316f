<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PerformanceMonitoring
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);

        // Enable query logging for this request
        DB::enableQueryLog();

        $response = $next($request);

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryUsage = $endMemory - $startMemory;
        $queries = DB::getQueryLog();
        $queryCount = count($queries);
        $totalQueryTime = collect($queries)->sum('time');

        // Add performance headers
        $response->headers->set('X-Response-Time', round($executionTime, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', $this->formatBytes($memoryUsage));
        $response->headers->set('X-Query-Count', $queryCount);
        $response->headers->set('X-Query-Time', round($totalQueryTime, 2) . 'ms');

        // Log slow requests
        if ($executionTime > 1000) { // Requests slower than 1 second
            $this->logSlowRequest($request, $executionTime, $memoryUsage, $queryCount, $totalQueryTime);
        }

        // Log requests with many queries
        if ($queryCount > 20) {
            $this->logQueryHeavyRequest($request, $queryCount, $queries);
        }

        // Log high memory usage
        if ($memoryUsage > 50 * 1024 * 1024) { // More than 50MB
            $this->logHighMemoryRequest($request, $memoryUsage);
        }

        // Store performance metrics (in production, you'd send this to a monitoring service)
        $this->storePerformanceMetrics($request, [
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'query_count' => $queryCount,
            'query_time' => $totalQueryTime,
        ]);

        return $response;
    }

    /**
     * Log slow requests
     */
    private function logSlowRequest(Request $request, float $executionTime, int $memoryUsage, int $queryCount, float $queryTime): void
    {
        Log::warning('Slow request detected', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'execution_time' => round($executionTime, 2) . 'ms',
            'memory_usage' => $this->formatBytes($memoryUsage),
            'query_count' => $queryCount,
            'query_time' => round($queryTime, 2) . 'ms',
            'user_id' => auth()->id(),
            'ip' => $request->ip(),
        ]);
    }

    /**
     * Log requests with many database queries
     */
    private function logQueryHeavyRequest(Request $request, int $queryCount, array $queries): void
    {
        Log::warning('Query-heavy request detected', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'query_count' => $queryCount,
            'queries' => collect($queries)->map(function ($query) {
                return [
                    'sql' => $query['query'],
                    'time' => $query['time'] . 'ms',
                ];
            })->toArray(),
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * Log high memory usage requests
     */
    private function logHighMemoryRequest(Request $request, int $memoryUsage): void
    {
        Log::warning('High memory usage detected', [
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'memory_usage' => $this->formatBytes($memoryUsage),
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * Store performance metrics
     */
    private function storePerformanceMetrics(Request $request, array $metrics): void
    {
        // In production, you would send these metrics to a monitoring service
        // like New Relic, DataDog, or store them in a time-series database

        // For now, we'll just log them for debugging
        if (config('app.debug')) {
            Log::debug('Performance metrics', [
                'url' => $request->path(),
                'method' => $request->method(),
                'metrics' => $metrics,
                'timestamp' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
