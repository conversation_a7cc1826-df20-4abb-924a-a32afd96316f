<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class LevelUp extends Notification implements ShouldQueue
{
    use Queueable;

    protected $newLevel;
    protected $previousLevel;
    protected $xpEarned;

    /**
     * Create a new notification instance.
     */
    public function __construct(int $newLevel, int $previousLevel, int $xpEarned = 0)
    {
        $this->newLevel = $newLevel;
        $this->previousLevel = $previousLevel;
        $this->xpEarned = $xpEarned;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('🎉 Level Up! You\'ve reached Level ' . $this->newLevel)
            ->greeting('Congratulations, ' . $notifiable->name . '!')
            ->line("You've just leveled up from Level {$this->previousLevel} to Level {$this->newLevel}!")
            ->line('This is a testament to your dedication and hard work in The Real World.')
            ->when($this->xpEarned > 0, function ($mail) {
                return $mail->line("You've earned {$this->xpEarned} XP for reaching this milestone!");
            })
            ->line('Keep pushing forward and continue building your empire!')
            ->action('View Your Progress', route('dashboard.profile.show'))
            ->line('Remember: Success is not final, failure is not fatal. It\'s the courage to continue that counts.');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Level Up!',
            'message' => "Congratulations! You've reached Level {$this->newLevel}!",
            'type' => 'level_up',
            'icon' => '⭐',
            'action_url' => route('dashboard.profile.show'),
            'action_text' => 'View Progress',
            'new_level' => $this->newLevel,
            'previous_level' => $this->previousLevel,
            'xp_earned' => $this->xpEarned,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
