<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\Course;
use Illuminate\Http\Request;

class AdminLessonController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Lesson::with(['course.campus']);

        // Filter by course if provided
        if ($request->has('course') && $request->course) {
            $query->where('course_id', $request->course);
        }

        // Filter by status
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $lessons = $query->orderBy('course_id')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        $courses = Course::with('campus')->orderBy('title')->get();

        return view('admin.lessons.index', compact('lessons', 'courses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $courses = Course::with('campus')->where('is_active', true)->orderBy('title')->get();
        
        // Pre-select course if provided
        $selectedCourse = null;
        if ($request->has('course')) {
            $selectedCourse = Course::find($request->course);
        }

        return view('admin.lessons.create', compact('courses', 'selectedCourse'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url|max:500',
            'video_type' => 'nullable|in:youtube,vimeo,direct',
            'resource_url' => 'nullable|url|max:500',
            'duration_seconds' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
            'xp_reward' => 'nullable|integer|min:0|max:1000',
            'is_active' => 'boolean',
            'is_preview' => 'boolean',
        ]);

        $lesson = Lesson::create($request->all());

        return redirect()->route('admin.courses.show', $lesson->course)
            ->with('success', 'Lesson created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Lesson $lesson)
    {
        $lesson->load(['course.campus', 'userProgress.user']);

        // Get lesson statistics
        $stats = [
            'total_views' => $lesson->userProgress()->count(),
            'completed_views' => $lesson->userProgress()->where('is_completed', true)->count(),
            'average_watch_time' => $lesson->userProgress()->avg('watch_time_seconds'),
            'completion_rate' => $lesson->userProgress()->count() > 0 
                ? round(($lesson->userProgress()->where('is_completed', true)->count() / $lesson->userProgress()->count()) * 100, 1)
                : 0,
        ];

        // Get recent progress
        $recentProgress = $lesson->userProgress()
            ->with('user')
            ->latest('updated_at')
            ->take(10)
            ->get();

        return view('admin.lessons.show', compact('lesson', 'stats', 'recentProgress'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Lesson $lesson)
    {
        $courses = Course::with('campus')->where('is_active', true)->orderBy('title')->get();
        return view('admin.lessons.edit', compact('lesson', 'courses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Lesson $lesson)
    {
        $request->validate([
            'course_id' => 'required|exists:courses,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'content' => 'nullable|string',
            'video_url' => 'nullable|url|max:500',
            'video_type' => 'nullable|in:youtube,vimeo,direct',
            'resource_url' => 'nullable|url|max:500',
            'duration_seconds' => 'nullable|integer|min:1',
            'sort_order' => 'nullable|integer|min:0',
            'xp_reward' => 'nullable|integer|min:0|max:1000',
            'is_active' => 'boolean',
            'is_preview' => 'boolean',
        ]);

        $lesson->update($request->all());

        return redirect()->route('admin.courses.show', $lesson->course)
            ->with('success', 'Lesson updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Lesson $lesson)
    {
        $course = $lesson->course;
        
        // Check if lesson has user progress
        if ($lesson->userProgress()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Cannot delete lesson that has user progress. Deactivate it instead.');
        }

        $lesson->delete();

        return redirect()->route('admin.courses.show', $course)
            ->with('success', 'Lesson deleted successfully.');
    }

    /**
     * Toggle lesson active status
     */
    public function toggleStatus(Lesson $lesson)
    {
        $lesson->update(['is_active' => !$lesson->is_active]);

        $status = $lesson->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "Lesson {$status} successfully.");
    }

    /**
     * Reorder lessons within a course
     */
    public function reorder(Request $request, Course $course)
    {
        $request->validate([
            'lessons' => 'required|array',
            'lessons.*' => 'exists:lessons,id',
        ]);

        foreach ($request->lessons as $index => $lessonId) {
            Lesson::where('id', $lessonId)
                ->where('course_id', $course->id)
                ->update(['sort_order' => $index + 1]);
        }

        return response()->json(['success' => true]);
    }
}
