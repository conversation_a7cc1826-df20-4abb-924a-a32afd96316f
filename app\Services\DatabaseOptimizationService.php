<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Collection;

class DatabaseOptimizationService
{
    /**
     * Analyze database performance and suggest optimizations
     */
    public function analyzePerformance(): array
    {
        $analysis = [
            'table_analysis' => $this->analyzeTableSizes(),
            'index_analysis' => $this->analyzeIndexes(),
            'query_analysis' => $this->analyzeSlowQueries(),
            'recommendations' => $this->generateRecommendations(),
        ];

        return $analysis;
    }

    /**
     * Analyze table sizes and row counts
     */
    public function analyzeTableSizes(): array
    {
        try {
            $tables = DB::select("
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    ROUND((data_length / 1024 / 1024), 2) AS data_mb,
                    ROUND((index_length / 1024 / 1024), 2) AS index_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
            ");

            return collect($tables)->map(function ($table) {
                return [
                    'table' => $table->table_name,
                    'rows' => $table->table_rows,
                    'total_size_mb' => $table->size_mb,
                    'data_size_mb' => $table->data_mb,
                    'index_size_mb' => $table->index_mb,
                ];
            })->toArray();
        } catch (\Exception $e) {
            return ['error' => 'Could not analyze table sizes: ' . $e->getMessage()];
        }
    }

    /**
     * Analyze database indexes
     */
    public function analyzeIndexes(): array
    {
        $indexAnalysis = [];
        $tables = $this->getApplicationTables();

        foreach ($tables as $table) {
            try {
                $indexes = DB::select("SHOW INDEX FROM {$table}");
                $indexAnalysis[$table] = collect($indexes)->map(function ($index) {
                    return [
                        'name' => $index->Key_name,
                        'column' => $index->Column_name,
                        'unique' => $index->Non_unique == 0,
                        'type' => $index->Index_type,
                        'cardinality' => $index->Cardinality,
                    ];
                })->groupBy('name')->toArray();
            } catch (\Exception $e) {
                $indexAnalysis[$table] = ['error' => $e->getMessage()];
            }
        }

        return $indexAnalysis;
    }

    /**
     * Analyze slow queries (simulated - in production you'd use performance_schema)
     */
    public function analyzeSlowQueries(): array
    {
        // In production, you would query the slow query log or performance_schema
        // For this demo, we'll return common slow query patterns to watch for
        
        return [
            'common_slow_patterns' => [
                'SELECT * FROM large_table WITHOUT WHERE clause',
                'Queries without proper indexes',
                'N+1 query problems in relationships',
                'Subqueries that could be JOINs',
                'ORDER BY without LIMIT on large tables',
            ],
            'recommendations' => [
                'Add indexes on frequently queried columns',
                'Use LIMIT clauses for pagination',
                'Avoid SELECT * in production queries',
                'Use eager loading for relationships',
                'Consider query caching for expensive operations',
            ],
        ];
    }

    /**
     * Generate optimization recommendations
     */
    public function generateRecommendations(): array
    {
        $recommendations = [];

        // Check for missing indexes on important columns
        $missingIndexes = $this->checkMissingIndexes();
        if (!empty($missingIndexes)) {
            $recommendations[] = [
                'type' => 'missing_indexes',
                'priority' => 'high',
                'description' => 'Add missing indexes for better query performance',
                'details' => $missingIndexes,
            ];
        }

        // Check for large tables without proper indexing
        $largeTablesAnalysis = $this->analyzeLargeTables();
        if (!empty($largeTablesAnalysis)) {
            $recommendations[] = [
                'type' => 'large_tables',
                'priority' => 'medium',
                'description' => 'Optimize large tables for better performance',
                'details' => $largeTablesAnalysis,
            ];
        }

        // General performance recommendations
        $recommendations[] = [
            'type' => 'general',
            'priority' => 'low',
            'description' => 'General performance optimizations',
            'details' => [
                'Enable query caching',
                'Use connection pooling',
                'Regular ANALYZE TABLE operations',
                'Monitor slow query log',
                'Consider read replicas for heavy read workloads',
            ],
        ];

        return $recommendations;
    }

    /**
     * Check for missing indexes on important columns
     */
    private function checkMissingIndexes(): array
    {
        $missingIndexes = [];

        $importantColumns = [
            'users' => ['email', 'username', 'subscription_plan', 'is_active'],
            'courses' => ['campus_id', 'is_published', 'slug', 'created_at'],
            'events' => ['start_time', 'is_active', 'slug', 'type'],
            'chat_messages' => ['room_id', 'user_id', 'created_at'],
            'notifications' => ['notifiable_id', 'notifiable_type', 'read_at'],
            'course_enrollments' => ['user_id', 'course_id', 'completed_at'],
            'event_rsvps' => ['user_id', 'event_id', 'rsvp_status'],
        ];

        foreach ($importantColumns as $table => $columns) {
            if (!Schema::hasTable($table)) {
                continue;
            }

            try {
                $existingIndexes = collect(DB::select("SHOW INDEX FROM {$table}"))
                    ->pluck('Column_name')
                    ->toArray();

                foreach ($columns as $column) {
                    if (Schema::hasColumn($table, $column) && !in_array($column, $existingIndexes)) {
                        $missingIndexes[] = [
                            'table' => $table,
                            'column' => $column,
                            'suggested_sql' => "ALTER TABLE {$table} ADD INDEX idx_{$column} ({$column});",
                        ];
                    }
                }
            } catch (\Exception $e) {
                // Skip if table doesn't exist or can't be analyzed
            }
        }

        return $missingIndexes;
    }

    /**
     * Analyze large tables for optimization opportunities
     */
    private function analyzeLargeTables(): array
    {
        $largeTableThreshold = 100000; // 100k rows
        $analysis = [];

        $tables = ['users', 'courses', 'events', 'chat_messages', 'notifications'];

        foreach ($tables as $table) {
            if (!Schema::hasTable($table)) {
                continue;
            }

            try {
                $rowCount = DB::table($table)->count();
                
                if ($rowCount > $largeTableThreshold) {
                    $analysis[] = [
                        'table' => $table,
                        'row_count' => $rowCount,
                        'recommendations' => $this->getTableSpecificRecommendations($table, $rowCount),
                    ];
                }
            } catch (\Exception $e) {
                // Skip if can't analyze
            }
        }

        return $analysis;
    }

    /**
     * Get table-specific optimization recommendations
     */
    private function getTableSpecificRecommendations(string $table, int $rowCount): array
    {
        $recommendations = [];

        switch ($table) {
            case 'chat_messages':
                $recommendations[] = 'Consider partitioning by date';
                $recommendations[] = 'Archive old messages periodically';
                $recommendations[] = 'Add composite index on (room_id, created_at)';
                break;

            case 'notifications':
                $recommendations[] = 'Clean up old read notifications';
                $recommendations[] = 'Add composite index on (notifiable_id, notifiable_type, read_at)';
                break;

            case 'users':
                $recommendations[] = 'Consider soft deletes for inactive users';
                $recommendations[] = 'Add index on subscription_plan for billing queries';
                break;

            default:
                $recommendations[] = 'Monitor query performance';
                $recommendations[] = 'Consider adding appropriate indexes';
                break;
        }

        return $recommendations;
    }

    /**
     * Get list of application tables
     */
    private function getApplicationTables(): array
    {
        try {
            $tables = DB::select("SHOW TABLES");
            $tableColumn = 'Tables_in_' . config('database.connections.mysql.database');
            
            return collect($tables)->pluck($tableColumn)->toArray();
        } catch (\Exception $e) {
            // Fallback to known tables
            return [
                'users', 'courses', 'events', 'campuses', 'chat_messages', 
                'notifications', 'course_enrollments', 'event_rsvps', 'media'
            ];
        }
    }

    /**
     * Optimize database tables
     */
    public function optimizeTables(): array
    {
        $results = [];
        $tables = $this->getApplicationTables();

        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE {$table}");
                $results[$table] = 'optimized';
            } catch (\Exception $e) {
                $results[$table] = 'error: ' . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Analyze table statistics
     */
    public function analyzeTableStatistics(): array
    {
        $results = [];
        $tables = $this->getApplicationTables();

        foreach ($tables as $table) {
            try {
                DB::statement("ANALYZE TABLE {$table}");
                $results[$table] = 'analyzed';
            } catch (\Exception $e) {
                $results[$table] = 'error: ' . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Get database configuration recommendations
     */
    public function getDatabaseConfigRecommendations(): array
    {
        return [
            'mysql_settings' => [
                'innodb_buffer_pool_size' => '70% of available RAM',
                'query_cache_size' => '256M (if using MySQL < 8.0)',
                'max_connections' => 'Based on concurrent user load',
                'slow_query_log' => 'Enable for monitoring',
                'long_query_time' => '2 seconds or less',
            ],
            'laravel_settings' => [
                'database.connections.mysql.options.PDO::MYSQL_ATTR_USE_BUFFERED_QUERY' => true,
                'database.connections.mysql.options.PDO::MYSQL_ATTR_INIT_COMMAND' => 'SET sql_mode="STRICT_TRANS_TABLES"',
                'cache.default' => 'redis (for production)',
                'session.driver' => 'redis (for production)',
                'queue.default' => 'redis (for production)',
            ],
            'monitoring' => [
                'Enable slow query logging',
                'Monitor connection pool usage',
                'Track query execution times',
                'Monitor cache hit rates',
                'Set up database performance alerts',
            ],
        ];
    }
}
