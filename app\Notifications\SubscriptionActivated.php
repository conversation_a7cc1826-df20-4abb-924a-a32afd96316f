<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class SubscriptionActivated extends Notification implements ShouldQueue
{
    use Queueable;

    protected $plan;
    protected $features;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $plan, array $features = [])
    {
        $this->plan = $plan;
        $this->features = $features;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
                    ->subject('🚀 Your ' . ucfirst($this->plan) . ' Subscription is Active!')
                    ->greeting('Congratulations, ' . $notifiable->name . '!')
                    ->line('Your ' . ucfirst($this->plan) . ' subscription has been successfully activated. You now have access to exclusive content and features that will accelerate your journey to success.')
                    ->line('**What\'s included in your ' . ucfirst($this->plan) . ' plan:**');

        // Add plan features
        foreach ($this->features as $feature) {
            $message->line('✅ ' . $feature);
        }

        return $message->line('Your subscription will automatically renew monthly. You can manage your subscription anytime from your account settings.')
                      ->action('Access Your Premium Content', route('dashboard.campuses'))
                      ->line('Time to level up your game. Let\'s make some money!')
                      ->salutation('To your success,')
                      ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => ucfirst($this->plan) . ' Subscription Activated',
            'message' => 'Your ' . ucfirst($this->plan) . ' subscription is now active. Access your premium content and start earning.',
            'action_url' => route('dashboard.campuses'),
            'action_text' => 'Access Premium Content',
            'type' => 'subscription',
            'icon' => '🚀',
            'data' => [
                'plan' => $this->plan,
                'features' => $this->features,
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => ucfirst($this->plan) . ' Subscription Activated',
            'message' => 'Your premium subscription is now active.',
            'type' => 'subscription',
            'plan' => $this->plan,
        ];
    }
}
