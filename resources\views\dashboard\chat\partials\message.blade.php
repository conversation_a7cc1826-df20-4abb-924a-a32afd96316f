<div class="flex items-start space-x-3 {{ $message->user_id === $user->id ? 'flex-row-reverse space-x-reverse' : '' }}">
    <!-- User Avatar -->
    <div class="flex-shrink-0">
        <img src="{{ $message->user->getAvatarUrl() }}" 
             alt="{{ $message->user->name }}" 
             class="w-8 h-8 rounded-full">
    </div>

    <!-- Message Content -->
    <div class="flex-1 min-w-0">
        <!-- Message Header -->
        <div class="flex items-center space-x-2 mb-1 {{ $message->user_id === $user->id ? 'justify-end' : '' }}">
            <span class="text-sm font-medium {{ $message->user_id === $user->id ? 'text-primary-400' : 'text-white' }}">
                {{ $message->user->name }}
            </span>
            
            <!-- User Level Badge -->
            <span class="bg-gray-600 text-gray-300 text-xs px-2 py-0.5 rounded">
                Level {{ $message->user->level }}
            </span>
            
            <!-- User Role Badge -->
            @if($message->user->hasRole('admin'))
                <span class="bg-red-600 text-white text-xs px-2 py-0.5 rounded">Admin</span>
            @elseif($message->user->hasRole('mentor'))
                <span class="bg-blue-600 text-white text-xs px-2 py-0.5 rounded">Mentor</span>
            @elseif($message->user->hasRole('champion'))
                <span class="bg-yellow-600 text-white text-xs px-2 py-0.5 rounded">Champion</span>
            @endif
            
            <!-- Timestamp -->
            <span class="text-xs text-gray-500">{{ $message->getFormattedTime() }}</span>
            
            <!-- Edited Indicator -->
            @if($message->is_edited)
                <span class="text-xs text-gray-500 italic">(edited)</span>
            @endif
        </div>

        <!-- Message Text -->
        <div class="p-3 rounded-lg {{ $message->user_id === $user->id ? 'bg-primary-600 text-white ml-auto max-w-xs' : 'bg-gray-700 text-gray-100 max-w-md' }}">
            <p class="text-sm break-words">{!! $message->getProcessedMessage() !!}</p>
        </div>

        <!-- Message Actions (for own messages) -->
        @if($message->user_id === $user->id)
        <div class="flex items-center justify-end space-x-2 mt-1">
            @if($message->canBeEditedBy($user))
                <button onclick="editMessage({{ $message->id }})" 
                        class="text-xs text-gray-500 hover:text-gray-300">
                    Edit
                </button>
            @endif
            
            @if($message->canBeDeletedBy($user))
                <button onclick="deleteMessage({{ $message->id }})" 
                        class="text-xs text-gray-500 hover:text-red-400">
                    Delete
                </button>
            @endif
        </div>
        @endif
    </div>
</div>
