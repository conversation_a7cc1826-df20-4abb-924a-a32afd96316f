<?php

namespace App\Console\Commands;

use App\Services\CacheService;
use App\Services\DatabaseOptimizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class AnalyzePerformance extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'performance:analyze 
                            {--detailed : Show detailed analysis}
                            {--export= : Export results to file}
                            {--threshold=1000 : Slow query threshold in milliseconds}';

    /**
     * The console command description.
     */
    protected $description = 'Analyze application performance and identify bottlenecks';

    protected CacheService $cacheService;
    protected DatabaseOptimizationService $dbService;

    public function __construct(CacheService $cacheService, DatabaseOptimizationService $dbService)
    {
        parent::__construct();
        $this->cacheService = $cacheService;
        $this->dbService = $dbService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Starting Performance Analysis...');
        $this->newLine();

        $analysis = [
            'timestamp' => now()->toISOString(),
            'cache_analysis' => $this->analyzeCachePerformance(),
            'database_analysis' => $this->analyzeDatabasePerformance(),
            'memory_analysis' => $this->analyzeMemoryUsage(),
            'query_analysis' => $this->analyzeQueryPerformance(),
            'recommendations' => $this->generateRecommendations(),
        ];

        $this->displayResults($analysis);

        if ($this->option('export')) {
            $this->exportResults($analysis);
        }

        return 0;
    }

    /**
     * Analyze cache performance
     */
    protected function analyzeCachePerformance(): array
    {
        $this->info('📊 Analyzing Cache Performance...');

        $cacheStats = $this->cacheService->getCacheStatistics();
        
        $analysis = [
            'cache_driver' => config('cache.default'),
            'hit_rate' => $cacheStats['hit_rate'] ?? 0,
            'miss_rate' => $cacheStats['miss_rate'] ?? 0,
            'total_keys' => $cacheStats['total_keys'] ?? 0,
            'memory_usage' => $cacheStats['memory_usage'] ?? 0,
            'performance_score' => $this->calculateCacheScore($cacheStats),
        ];

        $this->displayCacheAnalysis($analysis);
        return $analysis;
    }

    /**
     * Analyze database performance
     */
    protected function analyzeDatabasePerformance(): array
    {
        $this->info('🗄️ Analyzing Database Performance...');

        $dbAnalysis = $this->dbService->analyzePerformance();
        
        $analysis = [
            'connection_count' => $this->getActiveConnections(),
            'slow_queries' => $this->getSlowQueries(),
            'table_sizes' => $dbAnalysis['table_analysis'] ?? [],
            'index_efficiency' => $dbAnalysis['index_analysis'] ?? [],
            'performance_score' => $this->calculateDatabaseScore($dbAnalysis),
        ];

        $this->displayDatabaseAnalysis($analysis);
        return $analysis;
    }

    /**
     * Analyze memory usage
     */
    protected function analyzeMemoryUsage(): array
    {
        $this->info('💾 Analyzing Memory Usage...');

        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));

        $analysis = [
            'current_usage' => $memoryUsage,
            'peak_usage' => $memoryPeak,
            'memory_limit' => $memoryLimit,
            'usage_percentage' => ($memoryUsage / $memoryLimit) * 100,
            'peak_percentage' => ($memoryPeak / $memoryLimit) * 100,
            'performance_score' => $this->calculateMemoryScore($memoryUsage, $memoryLimit),
        ];

        $this->displayMemoryAnalysis($analysis);
        return $analysis;
    }

    /**
     * Analyze query performance
     */
    protected function analyzeQueryPerformance(): array
    {
        $this->info('⚡ Analyzing Query Performance...');

        $threshold = $this->option('threshold');
        
        // Enable query logging temporarily
        DB::enableQueryLog();
        
        // Run some test queries to analyze
        $this->runTestQueries();
        
        $queries = DB::getQueryLog();
        $slowQueries = collect($queries)->filter(fn($query) => $query['time'] > $threshold);

        $analysis = [
            'total_queries' => count($queries),
            'slow_queries' => $slowQueries->count(),
            'average_time' => collect($queries)->avg('time'),
            'total_time' => collect($queries)->sum('time'),
            'slowest_query' => $slowQueries->sortByDesc('time')->first(),
            'performance_score' => $this->calculateQueryScore($queries, $slowQueries),
        ];

        $this->displayQueryAnalysis($analysis);
        return $analysis;
    }

    /**
     * Run test queries for analysis
     */
    protected function runTestQueries(): void
    {
        try {
            // Test common queries
            DB::table('users')->count();
            DB::table('courses')->where('is_published', true)->count();
            DB::table('events')->where('start_time', '>=', now())->count();
            
            // Test joins
            DB::table('course_enrollments')
                ->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
                ->join('users', 'course_enrollments.user_id', '=', 'users.id')
                ->select('courses.title', 'users.name')
                ->limit(10)
                ->get();
        } catch (\Exception $e) {
            $this->warn('Some test queries failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate performance recommendations
     */
    protected function generateRecommendations(): array
    {
        $recommendations = [];

        // Cache recommendations
        $cacheStats = $this->cacheService->getCacheStatistics();
        if (($cacheStats['hit_rate'] ?? 0) < 80) {
            $recommendations[] = [
                'category' => 'cache',
                'priority' => 'high',
                'issue' => 'Low cache hit rate',
                'recommendation' => 'Review caching strategy and increase cache duration for frequently accessed data',
            ];
        }

        // Database recommendations
        $slowQueries = $this->getSlowQueries();
        if (count($slowQueries) > 0) {
            $recommendations[] = [
                'category' => 'database',
                'priority' => 'high',
                'issue' => 'Slow queries detected',
                'recommendation' => 'Optimize slow queries by adding indexes or rewriting queries',
            ];
        }

        // Memory recommendations
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        if (($memoryUsage / $memoryLimit) > 0.8) {
            $recommendations[] = [
                'category' => 'memory',
                'priority' => 'medium',
                'issue' => 'High memory usage',
                'recommendation' => 'Consider increasing memory limit or optimizing memory-intensive operations',
            ];
        }

        return $recommendations;
    }

    /**
     * Display analysis results
     */
    protected function displayResults(array $analysis): void
    {
        $this->newLine();
        $this->info('📈 Performance Analysis Results');
        $this->line('================================');

        // Overall performance score
        $overallScore = $this->calculateOverallScore($analysis);
        $this->displayScore('Overall Performance', $overallScore);

        if ($this->option('detailed')) {
            $this->displayDetailedResults($analysis);
        }

        // Display recommendations
        $this->newLine();
        $this->info('💡 Recommendations');
        $this->line('==================');

        foreach ($analysis['recommendations'] as $recommendation) {
            $priority = strtoupper($recommendation['priority']);
            $color = $recommendation['priority'] === 'high' ? 'error' : 
                    ($recommendation['priority'] === 'medium' ? 'warn' : 'info');
            
            $this->$color("[{$priority}] {$recommendation['issue']}");
            $this->line("   → {$recommendation['recommendation']}");
            $this->newLine();
        }
    }

    /**
     * Display cache analysis
     */
    protected function displayCacheAnalysis(array $analysis): void
    {
        $this->line("   Cache Driver: {$analysis['cache_driver']}");
        $this->line("   Hit Rate: {$analysis['hit_rate']}%");
        $this->line("   Total Keys: {$analysis['total_keys']}");
        $this->displayScore('Cache Performance', $analysis['performance_score']);
        $this->newLine();
    }

    /**
     * Display database analysis
     */
    protected function displayDatabaseAnalysis(array $analysis): void
    {
        $this->line("   Active Connections: {$analysis['connection_count']}");
        $this->line("   Slow Queries: " . count($analysis['slow_queries']));
        $this->displayScore('Database Performance', $analysis['performance_score']);
        $this->newLine();
    }

    /**
     * Display memory analysis
     */
    protected function displayMemoryAnalysis(array $analysis): void
    {
        $this->line("   Current Usage: " . $this->formatBytes($analysis['current_usage']));
        $this->line("   Peak Usage: " . $this->formatBytes($analysis['peak_usage']));
        $this->line("   Memory Limit: " . $this->formatBytes($analysis['memory_limit']));
        $this->line("   Usage: {$analysis['usage_percentage']}%");
        $this->displayScore('Memory Performance', $analysis['performance_score']);
        $this->newLine();
    }

    /**
     * Display query analysis
     */
    protected function displayQueryAnalysis(array $analysis): void
    {
        $this->line("   Total Queries: {$analysis['total_queries']}");
        $this->line("   Slow Queries: {$analysis['slow_queries']}");
        $this->line("   Average Time: " . round($analysis['average_time'], 2) . 'ms');
        $this->displayScore('Query Performance', $analysis['performance_score']);
        $this->newLine();
    }

    /**
     * Display performance score
     */
    protected function displayScore(string $category, float $score): void
    {
        $color = $score >= 80 ? 'info' : ($score >= 60 ? 'warn' : 'error');
        $this->$color("   {$category} Score: {$score}/100");
    }

    /**
     * Calculate cache performance score
     */
    protected function calculateCacheScore(array $stats): float
    {
        $hitRate = $stats['hit_rate'] ?? 0;
        return min(100, $hitRate);
    }

    /**
     * Calculate database performance score
     */
    protected function calculateDatabaseScore(array $analysis): float
    {
        // Simple scoring based on slow queries and table sizes
        $slowQueries = count($analysis['query_analysis']['slow_queries'] ?? []);
        $score = 100 - ($slowQueries * 10); // Deduct 10 points per slow query
        return max(0, $score);
    }

    /**
     * Calculate memory performance score
     */
    protected function calculateMemoryScore(int $usage, int $limit): float
    {
        $percentage = ($usage / $limit) * 100;
        return max(0, 100 - $percentage);
    }

    /**
     * Calculate query performance score
     */
    protected function calculateQueryScore(array $queries, $slowQueries): float
    {
        if (empty($queries)) return 100;
        
        $slowPercentage = ($slowQueries->count() / count($queries)) * 100;
        return max(0, 100 - $slowPercentage);
    }

    /**
     * Calculate overall performance score
     */
    protected function calculateOverallScore(array $analysis): float
    {
        $scores = [
            $analysis['cache_analysis']['performance_score'],
            $analysis['database_analysis']['performance_score'],
            $analysis['memory_analysis']['performance_score'],
            $analysis['query_analysis']['performance_score'],
        ];

        return array_sum($scores) / count($scores);
    }

    /**
     * Get active database connections
     */
    protected function getActiveConnections(): int
    {
        try {
            $result = DB::select('SHOW STATUS LIKE "Threads_connected"');
            return $result[0]->Value ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get slow queries
     */
    protected function getSlowQueries(): array
    {
        try {
            return DB::select('SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10');
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Parse memory limit string
     */
    protected function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;

        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }

        return $limit;
    }

    /**
     * Format bytes to human readable
     */
    protected function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Export results to file
     */
    protected function exportResults(array $analysis): void
    {
        $filename = $this->option('export');
        $content = json_encode($analysis, JSON_PRETTY_PRINT);
        
        file_put_contents($filename, $content);
        $this->info("Results exported to: {$filename}");
    }

    /**
     * Display detailed results
     */
    protected function displayDetailedResults(array $analysis): void
    {
        // Implementation for detailed view
        $this->newLine();
        $this->info('📋 Detailed Analysis');
        $this->line('===================');
        
        // Show detailed cache info
        if (!empty($analysis['cache_analysis'])) {
            $this->line('Cache Details:');
            foreach ($analysis['cache_analysis'] as $key => $value) {
                if (!is_array($value)) {
                    $this->line("  {$key}: {$value}");
                }
            }
        }
    }
}
