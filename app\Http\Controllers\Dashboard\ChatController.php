<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ChatRoom;
use App\Models\ChatMessage;
use App\Models\Campus;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    /**
     * Display the chat interface
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $selectedRoomId = $request->get('room');

        // Get available chat rooms based on user's access
        $availableRooms = $this->getAvailableRooms($user);

        // Select default room if none specified
        if (!$selectedRoomId && $availableRooms->count() > 0) {
            $selectedRoomId = $availableRooms->first()->id;
        }

        $selectedRoom = null;
        $messages = collect();

        if ($selectedRoomId) {
            $selectedRoom = $availableRooms->find($selectedRoomId);

            if ($selectedRoom) {
                // Get recent messages for the selected room
                $messages = ChatMessage::where('chat_room_id', $selectedRoom->id)
                    ->with(['user'])
                    ->orderBy('created_at', 'desc')
                    ->take(50)
                    ->get()
                    ->reverse()
                    ->values();
            }
        }

        return view('dashboard.chat.index', compact(
            'availableRooms', 'selectedRoom', 'messages', 'user'
        ));
    }

    /**
     * Send a new message
     */
    public function sendMessage(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'room_id' => 'required|exists:chat_rooms,id',
            'message' => 'required|string|max:1000',
        ]);

        $room = ChatRoom::findOrFail($request->room_id);

        // Check if user can access this room
        if (!$this->canUserAccessRoom($user, $room)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have access to this chat room.'
            ], 403);
        }

        // Create the message
        $message = ChatMessage::create([
            'chat_room_id' => $room->id,
            'user_id' => $user->id,
            'message' => $request->message,
            'type' => 'text',
        ]);

        // Load user relationship
        $message->load('user');

        // Award XP for sending message (daily task completion)
        $user->addXp(2);

        // Broadcast the message (we'll implement this later)
        // broadcast(new MessageSent($message))->toOthers();

        return response()->json([
            'success' => true,
            'message' => $message,
            'html' => view('dashboard.chat.partials.message', compact('message', 'user'))->render()
        ]);
    }

    /**
     * Get available chat rooms for user
     */
    private function getAvailableRooms($user)
    {
        $query = ChatRoom::where('is_active', true);

        // Filter rooms based on user access
        $accessibleRooms = $query->get()->filter(function($room) use ($user) {
            return $this->canUserAccessRoom($user, $room);
        });

        return $accessibleRooms;
    }

    /**
     * Check if user can access a specific room
     */
    private function canUserAccessRoom($user, $room)
    {
        // Use the model's canUserAccess method
        return $room->canUserAccess($user);
    }

    /**
     * Load more messages (for infinite scroll)
     */
    public function loadMessages(Request $request)
    {
        $user = Auth::user();
        $roomId = $request->get('room_id');
        $before = $request->get('before'); // Message ID to load before

        $room = ChatRoom::findOrFail($roomId);

        if (!$this->canUserAccessRoom($user, $room)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $query = ChatMessage::where('chat_room_id', $roomId)
            ->with(['user'])
            ->orderBy('created_at', 'desc');

        if ($before) {
            $beforeMessage = ChatMessage::find($before);
            if ($beforeMessage) {
                $query->where('created_at', '<', $beforeMessage->created_at);
            }
        }

        $messages = $query->take(20)->get()->reverse()->values();

        return response()->json([
            'success' => true,
            'messages' => $messages,
            'html' => $messages->map(function($message) use ($user) {
                return view('dashboard.chat.partials.message', compact('message', 'user'))->render();
            })->join('')
        ]);
    }
}
