@extends('layouts.dashboard')

@section('title', 'Search Results - The Real World')
@section('page-title', 'Search Results')

@section('content')
<div class="p-6">
    <!-- Search Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h2 class="text-2xl font-bold">Search Results</h2>
                @if(request('q'))
                    <p class="text-gray-400">Results for "<span class="text-white">{{ request('q') }}</span>"</p>
                @endif
            </div>
            <a href="{{ route('dashboard.search.advanced') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
                Advanced Search
            </a>
        </div>

        <!-- Search Form -->
        <form method="GET" action="{{ route('dashboard.search.index') }}" class="mb-6">
            <div class="flex gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="q" 
                           value="{{ request('q') }}" 
                           placeholder="Search courses, events, users..."
                           class="input-field">
                </div>
                <select name="type" class="input-field w-auto">
                    <option value="">All Types</option>
                    <option value="courses" {{ request('type') === 'courses' ? 'selected' : '' }}>Courses</option>
                    <option value="events" {{ request('type') === 'events' ? 'selected' : '' }}>Events</option>
                    <option value="users" {{ request('type') === 'users' ? 'selected' : '' }}>Users</option>
                </select>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
            </div>
        </form>
    </div>

    @if(request('q'))
        <!-- Results Tabs -->
        <div class="mb-6" x-data="{ activeTab: '{{ request('type') ?: 'all' }}' }">
            <div class="flex space-x-1 bg-gray-800 p-1 rounded-lg mb-6">
                <button @click="activeTab = 'all'" 
                        :class="activeTab === 'all' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                        class="flex-1 py-2 px-4 rounded-md transition-colors">
                    All Results ({{ ($courses->total() ?? 0) + ($events->total() ?? 0) + ($users->total() ?? 0) }})
                </button>
                <button @click="activeTab = 'courses'" 
                        :class="activeTab === 'courses' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                        class="flex-1 py-2 px-4 rounded-md transition-colors">
                    Courses ({{ $courses->total() ?? 0 }})
                </button>
                <button @click="activeTab = 'events'" 
                        :class="activeTab === 'events' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                        class="flex-1 py-2 px-4 rounded-md transition-colors">
                    Events ({{ $events->total() ?? 0 }})
                </button>
                <button @click="activeTab = 'users'" 
                        :class="activeTab === 'users' ? 'bg-primary-600 text-white' : 'text-gray-400 hover:text-white'"
                        class="flex-1 py-2 px-4 rounded-md transition-colors">
                    Users ({{ $users->total() ?? 0 }})
                </button>
            </div>

            <!-- All Results -->
            <div x-show="activeTab === 'all'">
                @if(isset($courses) && $courses->count() > 0)
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Courses</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($courses->take(6) as $course)
                                <div class="card hover:bg-gray-700 transition-colors">
                                    <div class="flex items-center space-x-4">
                                        <div class="text-3xl">📚</div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold">{{ $course->title }}</h4>
                                            <p class="text-gray-400 text-sm">{{ Str::limit($course->description, 100) }}</p>
                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <span>{{ $course->campus->name ?? 'General' }}</span>
                                                @if($course->difficulty_level)
                                                    <span class="ml-2 capitalize">{{ $course->difficulty_level }}</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-4">
                                        <a href="{{ route('dashboard.courses.show', $course) }}" class="btn-primary w-full text-center">
                                            View Course
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @if($courses->count() > 6)
                            <div class="text-center mt-4">
                                <a href="{{ route('dashboard.search.index', array_merge(request()->all(), ['type' => 'courses'])) }}" 
                                   class="btn-secondary">
                                    View All Courses ({{ $courses->total() }})
                                </a>
                            </div>
                        @endif
                    </div>
                @endif

                @if(isset($events) && $events->count() > 0)
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Events</h3>
                        <div class="space-y-4">
                            @foreach($events->take(5) as $event)
                                <div class="card hover:bg-gray-700 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-3xl">🎪</div>
                                            <div>
                                                <h4 class="font-semibold">{{ $event->title }}</h4>
                                                <p class="text-gray-400 text-sm">{{ Str::limit($event->description, 150) }}</p>
                                                <div class="flex items-center mt-2 text-xs text-gray-500">
                                                    <span>{{ $event->start_time->format('M j, Y g:i A') }}</span>
                                                    @if($event->category)
                                                        <span class="ml-2 capitalize">{{ $event->category }}</span>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                        <a href="{{ route('dashboard.events.show', $event) }}" class="btn-primary">
                                            View Event
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @if($events->count() > 5)
                            <div class="text-center mt-4">
                                <a href="{{ route('dashboard.search.index', array_merge(request()->all(), ['type' => 'events'])) }}" 
                                   class="btn-secondary">
                                    View All Events ({{ $events->total() }})
                                </a>
                            </div>
                        @endif
                    </div>
                @endif

                @if(isset($users) && $users->count() > 0)
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold mb-4">Users</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($users->take(6) as $user)
                                <div class="card hover:bg-gray-700 transition-colors">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold">
                                            {{ substr($user->name, 0, 1) }}
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="font-semibold">{{ $user->name }}</h4>
                                            <p class="text-gray-400 text-sm">Level {{ $user->level }} • {{ number_format($user->xp) }} XP</p>
                                            @if($user->bio)
                                                <p class="text-gray-500 text-xs mt-1">{{ Str::limit($user->bio, 80) }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @if($users->count() > 6)
                            <div class="text-center mt-4">
                                <a href="{{ route('dashboard.search.index', array_merge(request()->all(), ['type' => 'users'])) }}" 
                                   class="btn-secondary">
                                    View All Users ({{ $users->total() }})
                                </a>
                            </div>
                        @endif
                    </div>
                @endif

                @if((!isset($courses) || $courses->count() === 0) && 
                    (!isset($events) || $events->count() === 0) && 
                    (!isset($users) || $users->count() === 0))
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">No results found</h3>
                        <p class="text-gray-400 mb-4">Try adjusting your search terms or browse our content</p>
                        <div class="flex justify-center space-x-4">
                            <a href="{{ route('dashboard.courses.index') }}" class="btn-secondary">Browse Courses</a>
                            <a href="{{ route('dashboard.events.index') }}" class="btn-secondary">Browse Events</a>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Courses Tab -->
            <div x-show="activeTab === 'courses'">
                @if(isset($courses) && $courses->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($courses as $course)
                            <div class="card hover:bg-gray-700 transition-colors">
                                <div class="aspect-video bg-gray-800 flex items-center justify-center mb-4">
                                    <div class="text-6xl">📚</div>
                                </div>
                                <h4 class="font-semibold mb-2">{{ $course->title }}</h4>
                                <p class="text-gray-400 text-sm mb-4">{{ Str::limit($course->description, 120) }}</p>
                                <div class="flex items-center justify-between mb-4">
                                    <span class="text-xs bg-primary-600 text-white px-2 py-1 rounded">
                                        {{ $course->campus->name ?? 'General' }}
                                    </span>
                                    <span class="text-xs text-gray-400 capitalize">
                                        {{ $course->difficulty_level }}
                                    </span>
                                </div>
                                <a href="{{ route('dashboard.courses.show', $course) }}" class="btn-primary w-full text-center">
                                    View Course
                                </a>
                            </div>
                        @endforeach
                    </div>
                    
                    @if(isset($courses) && $courses->hasPages())
                        <div class="mt-8">
                            {{ $courses->appends(request()->query())->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">📚</div>
                        <h3 class="text-xl font-semibold mb-2">No courses found</h3>
                        <p class="text-gray-400">Try different search terms or browse all courses</p>
                    </div>
                @endif
            </div>

            <!-- Events Tab -->
            <div x-show="activeTab === 'events'">
                @if(isset($events) && $events->count() > 0)
                    <div class="space-y-4">
                        @foreach($events as $event)
                            <div class="card hover:bg-gray-700 transition-colors">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="text-4xl">🎪</div>
                                        <div>
                                            <h4 class="font-semibold">{{ $event->title }}</h4>
                                            <p class="text-gray-400 text-sm mb-2">{{ Str::limit($event->description, 200) }}</p>
                                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                                <span>{{ $event->start_time->format('M j, Y g:i A') }}</span>
                                                @if($event->category)
                                                    <span class="capitalize">{{ $event->category }}</span>
                                                @endif
                                                <span>{{ $event->rsvps()->count() }} attending</span>
                                            </div>
                                        </div>
                                    </div>
                                    <a href="{{ route('dashboard.events.show', $event) }}" class="btn-primary">
                                        View Event
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    @if(isset($events) && $events->hasPages())
                        <div class="mt-8">
                            {{ $events->appends(request()->query())->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">🎪</div>
                        <h3 class="text-xl font-semibold mb-2">No events found</h3>
                        <p class="text-gray-400">Try different search terms or browse all events</p>
                    </div>
                @endif
            </div>

            <!-- Users Tab -->
            <div x-show="activeTab === 'users'">
                @if(isset($users) && $users->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($users as $user)
                            <div class="card hover:bg-gray-700 transition-colors">
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                                        {{ substr($user->name, 0, 1) }}
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold">{{ $user->name }}</h4>
                                        <p class="text-gray-400 text-sm">Level {{ $user->level }}</p>
                                        <p class="text-gray-500 text-xs">{{ number_format($user->xp) }} XP</p>
                                    </div>
                                </div>
                                @if($user->bio)
                                    <p class="text-gray-400 text-sm mb-4">{{ Str::limit($user->bio, 120) }}</p>
                                @endif
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span>{{ $user->courseEnrollments()->count() }} courses</span>
                                    <span>{{ $user->badges()->count() }} badges</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    @if(isset($users) && $users->hasPages())
                        <div class="mt-8">
                            {{ $users->appends(request()->query())->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <div class="text-6xl mb-4">👥</div>
                        <h3 class="text-xl font-semibold mb-2">No users found</h3>
                        <p class="text-gray-400">Try different search terms</p>
                    </div>
                @endif
            </div>
        </div>
    @else
        <!-- No Search Query -->
        <div class="text-center py-12">
            <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold mb-2">Search The Real World</h3>
            <p class="text-gray-400 mb-6">Find courses, events, and connect with other members</p>
            <div class="flex justify-center space-x-4">
                <a href="{{ route('dashboard.courses.index') }}" class="btn-secondary">Browse Courses</a>
                <a href="{{ route('dashboard.events.index') }}" class="btn-secondary">Browse Events</a>
                <a href="{{ route('dashboard.leaderboard.index') }}" class="btn-secondary">View Leaderboard</a>
            </div>
        </div>
    @endif
</div>
@endsection
