<?php

namespace Tests\Traits;

use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;

trait TestHelpers
{
    /**
     * Create and authenticate an admin user
     */
    protected function createAdmin(array $attributes = []): User
    {
        $admin = User::factory()->create($attributes);
        $admin->assignRole('admin');
        
        return $admin;
    }

    /**
     * Create and authenticate a regular user
     */
    protected function createUser(array $attributes = []): User
    {
        return User::factory()->create($attributes);
    }

    /**
     * Create and authenticate a user with subscription
     */
    protected function createSubscribedUser(array $attributes = []): User
    {
        $user = User::factory()->create($attributes);
        
        // Create active subscription
        $user->subscriptions()->create([
            'name' => 'default',
            'stripe_id' => 'sub_' . \Str::random(14),
            'stripe_status' => 'active',
            'stripe_price' => 'price_' . \Str::random(14),
            'quantity' => 1,
            'trial_ends_at' => null,
            'ends_at' => null,
        ]);
        
        return $user;
    }

    /**
     * Act as an admin user
     */
    protected function actingAsAdmin(array $attributes = []): self
    {
        $admin = $this->createAdmin($attributes);
        return $this->actingAs($admin);
    }

    /**
     * Act as a subscribed user
     */
    protected function actingAsSubscribedUser(array $attributes = []): self
    {
        $user = $this->createSubscribedUser($attributes);
        return $this->actingAs($user);
    }

    /**
     * Act as user via API (Sanctum)
     */
    protected function actingAsApiUser(User $user = null): self
    {
        $user = $user ?: $this->createUser();
        Sanctum::actingAs($user);
        
        return $this;
    }

    /**
     * Act as admin via API (Sanctum)
     */
    protected function actingAsApiAdmin(User $admin = null): self
    {
        $admin = $admin ?: $this->createAdmin();
        Sanctum::actingAs($admin);
        
        return $this;
    }

    /**
     * Create a fake image file for testing
     */
    protected function createFakeImage(string $name = 'test.jpg', int $width = 100, int $height = 100): UploadedFile
    {
        return UploadedFile::fake()->image($name, $width, $height);
    }

    /**
     * Create a fake document file for testing
     */
    protected function createFakeDocument(string $name = 'test.pdf', int $kilobytes = 100): UploadedFile
    {
        return UploadedFile::fake()->create($name, $kilobytes);
    }

    /**
     * Create a fake video file for testing
     */
    protected function createFakeVideo(string $name = 'test.mp4', int $kilobytes = 1000): UploadedFile
    {
        return UploadedFile::fake()->create($name, $kilobytes, 'video/mp4');
    }

    /**
     * Assert that a file was uploaded to storage
     */
    protected function assertFileUploaded(string $disk, string $path): void
    {
        Storage::disk($disk)->assertExists($path);
    }

    /**
     * Assert that a file was not uploaded to storage
     */
    protected function assertFileNotUploaded(string $disk, string $path): void
    {
        Storage::disk($disk)->assertMissing($path);
    }

    /**
     * Clean up uploaded files after test
     */
    protected function cleanupUploads(string $disk = 'public'): void
    {
        Storage::fake($disk);
    }

    /**
     * Assert that an email was sent
     */
    protected function assertEmailSent(string $mailable = null): void
    {
        if ($mailable) {
            \Mail::assertSent($mailable);
        } else {
            \Mail::assertSent();
        }
    }

    /**
     * Assert that no emails were sent
     */
    protected function assertNoEmailSent(): void
    {
        \Mail::assertNothingSent();
    }

    /**
     * Assert that a notification was sent
     */
    protected function assertNotificationSent(User $user, string $notification): void
    {
        \Notification::assertSentTo($user, $notification);
    }

    /**
     * Assert that a job was dispatched
     */
    protected function assertJobDispatched(string $job): void
    {
        \Queue::assertPushed($job);
    }

    /**
     * Assert that an event was dispatched
     */
    protected function assertEventDispatched(string $event): void
    {
        \Event::assertDispatched($event);
    }

    /**
     * Assert that a cache key exists
     */
    protected function assertCacheHas(string $key): void
    {
        $this->assertTrue(\Cache::has($key), "Cache key '{$key}' does not exist");
    }

    /**
     * Assert that a cache key does not exist
     */
    protected function assertCacheMissing(string $key): void
    {
        $this->assertFalse(\Cache::has($key), "Cache key '{$key}' exists when it shouldn't");
    }

    /**
     * Assert that a log entry was created
     */
    protected function assertLogContains(string $level, string $message): void
    {
        $this->assertTrue(
            \Log::hasMessage($level, $message),
            "Log does not contain {$level} message: {$message}"
        );
    }

    /**
     * Assert that a model has specific attributes
     */
    protected function assertModelHasAttributes($model, array $attributes): void
    {
        foreach ($attributes as $key => $value) {
            $this->assertEquals(
                $value,
                $model->getAttribute($key),
                "Model attribute '{$key}' does not match expected value"
            );
        }
    }

    /**
     * Assert that a response contains validation errors for specific fields
     */
    protected function assertValidationErrors(array $fields, $response = null): void
    {
        $response = $response ?: $this->response;
        
        foreach ($fields as $field) {
            $response->assertSessionHasErrors($field);
        }
    }

    /**
     * Assert that a response does not contain validation errors
     */
    protected function assertNoValidationErrors($response = null): void
    {
        $response = $response ?: $this->response;
        $response->assertSessionHasNoErrors();
    }

    /**
     * Assert that a user has a specific role
     */
    protected function assertUserHasRole(User $user, string $role): void
    {
        $this->assertTrue(
            $user->hasRole($role),
            "User does not have role: {$role}"
        );
    }

    /**
     * Assert that a user has a specific permission
     */
    protected function assertUserHasPermission(User $user, string $permission): void
    {
        $this->assertTrue(
            $user->can($permission),
            "User does not have permission: {$permission}"
        );
    }

    /**
     * Assert that a user is enrolled in a course
     */
    protected function assertUserEnrolledInCourse(User $user, $course): void
    {
        $this->assertTrue(
            $user->enrolledCourses->contains($course),
            "User is not enrolled in the specified course"
        );
    }

    /**
     * Assert that a user has RSVP'd to an event
     */
    protected function assertUserRsvpedToEvent(User $user, $event): void
    {
        $this->assertDatabaseHas('event_rsvps', [
            'user_id' => $user->id,
            'event_id' => $event->id,
            'status' => 'confirmed',
        ]);
    }

    /**
     * Create test data for a complete course with lessons
     */
    protected function createCourseWithLessons(int $lessonCount = 3): \App\Models\Course
    {
        $course = \App\Models\Course::factory()->create();
        
        \App\Models\Lesson::factory()
            ->count($lessonCount)
            ->for($course)
            ->sequence(fn($sequence) => ['order' => $sequence->index + 1])
            ->create();
        
        return $course;
    }

    /**
     * Create test data for an event with RSVPs
     */
    protected function createEventWithRsvps(int $rsvpCount = 5): \App\Models\Event
    {
        $event = \App\Models\Event::factory()->upcoming()->create();
        
        $users = User::factory()->count($rsvpCount)->create();
        
        foreach ($users as $user) {
            $event->rsvps()->create([
                'user_id' => $user->id,
                'status' => 'confirmed',
            ]);
        }
        
        return $event;
    }

    /**
     * Simulate time passage for testing time-dependent features
     */
    protected function travelTo(\DateTimeInterface $date): void
    {
        \Carbon\Carbon::setTestNow($date);
    }

    /**
     * Reset time after time travel
     */
    protected function travelBack(): void
    {
        \Carbon\Carbon::setTestNow();
    }
}
