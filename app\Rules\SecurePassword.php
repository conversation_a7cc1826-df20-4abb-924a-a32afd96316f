<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SecurePassword implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $password = $value;
        
        // Check minimum length
        if (strlen($password) < config('security.password.min_length', 8)) {
            $fail('The password must be at least ' . config('security.password.min_length', 8) . ' characters long.');
            return;
        }
        
        // Check for uppercase letters
        if (config('security.password.require_uppercase', true) && !preg_match('/[A-Z]/', $password)) {
            $fail('The password must contain at least one uppercase letter.');
            return;
        }
        
        // Check for lowercase letters
        if (config('security.password.require_lowercase', true) && !preg_match('/[a-z]/', $password)) {
            $fail('The password must contain at least one lowercase letter.');
            return;
        }
        
        // Check for numbers
        if (config('security.password.require_numbers', true) && !preg_match('/[0-9]/', $password)) {
            $fail('The password must contain at least one number.');
            return;
        }
        
        // Check for symbols
        if (config('security.password.require_symbols', true) && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $fail('The password must contain at least one special character.');
            return;
        }
        
        // Check for common patterns
        if ($this->hasCommonPatterns($password)) {
            $fail('The password contains common patterns that make it weak.');
            return;
        }
        
        // Check against common passwords
        if ($this->isCommonPassword($password)) {
            $fail('This password is too common. Please choose a more unique password.');
            return;
        }
        
        // Check for personal information (if available)
        if ($this->containsPersonalInfo($password)) {
            $fail('The password should not contain personal information.');
            return;
        }
        
        // Check against known breaches (if enabled)
        if (config('security.password.check_breaches', false) && $this->isBreachedPassword($password)) {
            $fail('This password has been found in data breaches. Please choose a different password.');
            return;
        }
    }
    
    /**
     * Check for common weak patterns
     */
    protected function hasCommonPatterns(string $password): bool
    {
        $patterns = [
            '/^(.)\1{2,}$/',           // Repeated characters (aaa, 111)
            '/^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)/i', // Sequential
            '/^(.{1,2})\1+$/',         // Repeated patterns (abab, 1212)
            '/^(qwerty|asdf|zxcv|qaz|wsx|edc)/i', // Keyboard patterns
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $password)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if password is in common passwords list
     */
    protected function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', 'password123', '123456', '123456789', 'qwerty',
            'abc123', 'password1', 'admin', 'letmein', 'welcome',
            'monkey', '1234567890', 'dragon', 'master', 'login',
            'princess', 'solo', 'qwertyuiop', 'starwars', 'superman',
            'iloveyou', 'trustno1', 'football', 'baseball', 'whatever',
            'michael', 'shadow', 'mustang', 'jordan', 'hunter',
        ];
        
        return in_array(strtolower($password), $commonPasswords);
    }
    
    /**
     * Check if password contains personal information
     */
    protected function containsPersonalInfo(string $password): bool
    {
        $user = request()->user();
        
        if (!$user) {
            return false;
        }
        
        $personalInfo = [
            strtolower($user->name ?? ''),
            strtolower($user->first_name ?? ''),
            strtolower($user->last_name ?? ''),
            strtolower($user->email ?? ''),
        ];
        
        // Remove empty values
        $personalInfo = array_filter($personalInfo);
        
        $passwordLower = strtolower($password);
        
        foreach ($personalInfo as $info) {
            if (strlen($info) >= 3 && str_contains($passwordLower, $info)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if password has been found in data breaches
     */
    protected function isBreachedPassword(string $password): bool
    {
        try {
            // Use HaveIBeenPwned API to check for breached passwords
            $sha1Hash = strtoupper(sha1($password));
            $prefix = substr($sha1Hash, 0, 5);
            $suffix = substr($sha1Hash, 5);
            
            $response = Http::timeout(5)->get("https://api.pwnedpasswords.com/range/{$prefix}");
            
            if ($response->successful()) {
                $hashes = $response->body();
                
                // Check if our hash suffix appears in the response
                foreach (explode("\n", $hashes) as $line) {
                    $parts = explode(':', trim($line));
                    if (count($parts) === 2 && $parts[0] === $suffix) {
                        return true; // Password found in breach
                    }
                }
            }
        } catch (\Exception $e) {
            // Log the error but don't fail validation if API is unavailable
            Log::warning('Password breach check failed', [
                'error' => $e->getMessage(),
            ]);
        }
        
        return false;
    }
}
