<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if media table exists and fix duplicate index issue
        if (Schema::hasTable('media')) {
            // Check if the duplicate index exists and drop it
            $indexExists = $this->indexExists('media', 'media_model_type_model_id_index');
            
            if ($indexExists) {
                // The morphs() method already creates this index, so we can safely drop the duplicate
                Schema::table('media', function (Blueprint $table) {
                    // Only drop if it's a duplicate (morphs already creates model_type, model_id index)
                    try {
                        $table->dropIndex(['model_type', 'model_id']);
                    } catch (\Exception $e) {
                        // Index might not exist or already dropped, ignore
                    }
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to reverse - we're just fixing a duplicate index issue
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM `{$table}` WHERE Key_name = ?", [$indexName]);
            return count($indexes) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
};
