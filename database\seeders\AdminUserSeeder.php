<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'subscription_plan' => 'champions',
                'is_active' => true,
                'xp' => 10000,
                'level' => 50,
            ]
        );

        // Assign admin role
        $admin->assignRole('admin');

        // Create a mentor user
        $mentor = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Mentor User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'subscription_plan' => 'conquer',
                'is_active' => true,
                'xp' => 5000,
                'level' => 25,
            ]
        );

        // Assign mentor role
        $mentor->assignRole('mentor');

        // Create a champion user
        $champion = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Champion User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'subscription_plan' => 'champions',
                'is_active' => true,
                'xp' => 7500,
                'level' => 35,
            ]
        );

        // Assign champion role
        $champion->assignRole('champion');
    }
}
