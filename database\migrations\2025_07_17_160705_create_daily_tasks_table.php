<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description');
            $table->string('type'); // 'login', 'course_progress', 'chat_message', 'event_attend', etc.
            $table->integer('target_value')->default(1); // How many times to complete
            $table->integer('xp_reward');
            $table->string('icon')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('difficulty_level')->default(1); // 1-5
            $table->json('requirements')->nullable(); // Additional requirements
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['difficulty_level', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_tasks');
    }
};
