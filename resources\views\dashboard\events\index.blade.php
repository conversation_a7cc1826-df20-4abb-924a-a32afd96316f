@extends('layouts.dashboard')

@section('title', 'Live Events - The Real World')
@section('page-title', 'Live Events')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold mb-2">Live Events & Workshops</h2>
            <p class="text-gray-400">Join exclusive sessions with successful entrepreneurs and mentors</p>
        </div>
        
        <!-- Filter Tabs -->
        <div class="flex space-x-2">
            <a href="{{ route('dashboard.events.index', ['filter' => 'upcoming']) }}"
               class="btn-{{ $filter === 'upcoming' ? 'primary' : 'secondary' }} px-4 py-2">
                Upcoming
            </a>
            <a href="{{ route('dashboard.events.index', ['filter' => 'my_events']) }}"
               class="btn-{{ $filter === 'my_events' ? 'primary' : 'secondary' }} px-4 py-2">
                My Events
            </a>
            <a href="{{ route('dashboard.events.index', ['filter' => 'past']) }}"
               class="btn-{{ $filter === 'past' ? 'primary' : 'secondary' }} px-4 py-2">
                Past Events
            </a>
        </div>
    </div>

    <!-- Events Grid -->
    @if($events->count() > 0)
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @foreach($events as $event)
        <div class="card hover:transform hover:scale-105 transition-all duration-300 overflow-hidden">
            <!-- Event Thumbnail -->
            <div class="relative">
                @if($event->thumbnail_url)
                    <img src="{{ $event->thumbnail_url }}" alt="{{ $event->title }}" 
                         class="w-full h-48 object-cover">
                @else
                    <div class="w-full h-48 bg-gradient-to-br from-primary-600 to-primary-800 flex items-center justify-center">
                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                @endif

                <!-- Status Badge -->
                @php $statusBadge = $event->getStatusBadge(); @endphp
                <div class="absolute top-4 right-4">
                    <span class="px-3 py-1 rounded-full text-sm font-medium {{ $statusBadge['class'] }}">
                        {{ $statusBadge['text'] }}
                    </span>
                </div>

                <!-- Campus Badge -->
                @if($event->campus)
                <div class="absolute top-4 left-4">
                    <span class="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                        {{ $event->campus->name }}
                    </span>
                </div>
                @endif
            </div>

            <!-- Event Content -->
            <div class="p-6">
                <!-- Event Title -->
                <h3 class="text-xl font-bold mb-2 line-clamp-2">{{ $event->title }}</h3>
                
                <!-- Event Description -->
                <p class="text-gray-400 text-sm mb-4 line-clamp-3">{{ $event->description }}</p>

                <!-- Event Details -->
                <div class="space-y-2 mb-4 text-sm">
                    <!-- Date & Time -->
                    <div class="flex items-center text-gray-300">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        {{ $event->start_time->format('M j, Y • g:i A') }}
                    </div>

                    <!-- Duration -->
                    <div class="flex items-center text-gray-300">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ $event->getFormattedDuration() }}
                    </div>

                    <!-- Attendees -->
                    <div class="flex items-center text-gray-300">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        {{ $event->rsvp_count }}/{{ $event->max_attendees ?? '∞' }} attending
                    </div>
                </div>

                <!-- Countdown Timer -->
                @if($event->countdown['status'] === 'upcoming')
                <div class="bg-primary-600/20 rounded-lg p-3 mb-4">
                    <p class="text-primary-400 text-sm font-medium mb-2">{{ $event->countdown['message'] }}</p>
                    <div class="grid grid-cols-4 gap-2 text-center">
                        <div>
                            <div class="text-lg font-bold text-primary-400">{{ $event->countdown['days'] }}</div>
                            <div class="text-xs text-gray-400">Days</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-primary-400">{{ $event->countdown['hours'] }}</div>
                            <div class="text-xs text-gray-400">Hours</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-primary-400">{{ $event->countdown['minutes'] }}</div>
                            <div class="text-xs text-gray-400">Min</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-primary-400">{{ $event->countdown['seconds'] }}</div>
                            <div class="text-xs text-gray-400">Sec</div>
                        </div>
                    </div>
                </div>
                @elseif($event->countdown['status'] === 'live')
                <div class="bg-red-600/20 rounded-lg p-3 mb-4">
                    <p class="text-red-400 text-sm font-medium text-center">
                        🔴 LIVE NOW - {{ $event->countdown['message'] }}
                    </p>
                </div>
                @endif

                <!-- Premium Badge -->
                @if($event->is_premium)
                <div class="flex items-center mb-4">
                    <svg class="w-4 h-4 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-yellow-400 text-sm font-medium">Premium Event</span>
                </div>
                @endif

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    @if($event->can_join)
                        <a href="{{ route('dashboard.events.join', $event) }}" 
                           class="btn-primary flex-1 text-center">
                            Join Live
                        </a>
                    @elseif($event->is_user_registered)
                        @if($event->countdown['status'] === 'upcoming')
                            <button class="btn-secondary flex-1" disabled>
                                RSVP'd ✓
                            </button>
                        @else
                            <button class="btn-secondary flex-1" disabled>
                                Event Ended
                            </button>
                        @endif
                    @else
                        @if($event->countdown['status'] === 'upcoming' && $event->canUserAccess($user))
                            <button onclick="rsvpToEvent({{ $event->id }})" 
                                    class="btn-primary flex-1" 
                                    id="rsvp-btn-{{ $event->id }}">
                                RSVP Now
                            </button>
                        @elseif(!$event->canUserAccess($user))
                            <a href="{{ route('subscription.plans') }}" 
                               class="btn-secondary flex-1 text-center">
                                Upgrade to Access
                            </a>
                        @else
                            <button class="btn-secondary flex-1" disabled>
                                Event Ended
                            </button>
                        @endif
                    @endif
                    
                    <a href="{{ route('dashboard.events.show', $event) }}" 
                       class="btn-secondary px-4 py-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <!-- Pagination -->
    <div class="mt-8">
        {{ $events->appends(request()->query())->links() }}
    </div>
    @else
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">
            @if($filter === 'upcoming')
                No Upcoming Events
            @elseif($filter === 'my_events')
                No Events RSVP'd
            @else
                No Past Events
            @endif
        </h3>
        <p class="text-gray-400 mb-6">
            @if($filter === 'upcoming')
                Check back soon for new live events and workshops!
            @elseif($filter === 'my_events')
                RSVP to upcoming events to see them here.
            @else
                Past events will appear here after they're completed.
            @endif
        </p>
        @if($filter !== 'upcoming')
        <a href="{{ route('dashboard.events.index') }}" class="btn-primary">
            View Upcoming Events
        </a>
        @endif
    </div>
    @endif
</div>

<script>
function rsvpToEvent(eventId) {
    const button = document.getElementById(`rsvp-btn-${eventId}`);
    button.disabled = true;
    button.textContent = 'RSVP\'ing...';

    fetch(`/dashboard/events/${eventId}/rsvp`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            button.textContent = 'RSVP\'d ✓';
            button.classList.remove('btn-primary');
            button.classList.add('btn-secondary');
        } else {
            showNotification(data.message, 'error');
            button.disabled = false;
            button.textContent = 'RSVP Now';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        button.disabled = false;
        button.textContent = 'RSVP Now';
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
