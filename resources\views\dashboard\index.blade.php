@extends('layouts.dashboard')

@section('title', 'Dashboard - The Real World')
@section('page-title', 'Dashboard')

@section('content')
<div class="p-6">
    <!-- Welcome Message -->
    @if(!auth()->user()->subscribed())
    <div class="bg-primary-600 rounded-lg p-6 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-white">Upgrade to unlock full access!</h3>
                <p class="text-primary-100">Get access to all campuses, courses, and exclusive content.</p>
            </div>
            <div class="ml-auto">
                <a href="{{ route('subscription.plans') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-2 px-4 rounded-lg transition-colors">
                    Upgrade Now
                </a>
            </div>
        </div>
    </div>
    @endif

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- XP Card -->
        <div class="card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total XP</p>
                    <p class="text-2xl font-bold text-yellow-400">{{ auth()->user()->xp }}</p>
                </div>
            </div>
        </div>

        <!-- Level Card -->
        <div class="card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Current Level</p>
                    <p class="text-2xl font-bold text-blue-400">{{ auth()->user()->level }}</p>
                </div>
            </div>
        </div>

        <!-- Subscription Card -->
        <div class="card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Plan</p>
                    <p class="text-2xl font-bold text-green-400">{{ ucfirst(auth()->user()->subscription_plan ?? 'Trial') }}</p>
                </div>
            </div>
        </div>

        <!-- Days Active Card -->
        <div class="card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Days Active</p>
                    <p class="text-2xl font-bold text-purple-400">{{ auth()->user()->created_at->diffInDays(now()) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Recent Activity -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
            <div class="space-y-4">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-300">Account created</p>
                        <p class="text-xs text-gray-500">{{ auth()->user()->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                @if(auth()->user()->email_verified_at)
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-300">Email verified</p>
                        <p class="text-xs text-gray-500">{{ auth()->user()->email_verified_at->diffForHumans() }}</p>
                    </div>
                </div>
                @endif
                @if(auth()->user()->subscription_plan)
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-primary-400 rounded-full mr-3"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-300">Subscribed to {{ ucfirst(auth()->user()->subscription_plan) }}</p>
                        <p class="text-xs text-gray-500">Active subscription</p>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
            <div class="grid grid-cols-2 gap-4">
                <a href="{{ route('campuses') }}" class="btn-secondary text-center">
                    <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Explore Campuses
                </a>

                <a href="{{ route('dashboard.profile.show') }}" class="btn-secondary text-center">
                    <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    Edit Profile
                </a>

                @if(!auth()->user()->subscribed())
                <a href="{{ route('subscription.plans') }}" class="btn-primary text-center">
                    <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Upgrade Plan
                </a>
                @endif

                <a href="{{ route('testimonials') }}" class="btn-secondary text-center">
                    <svg class="w-5 h-5 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    Success Stories
                </a>
            </div>
        </div>
    </div>

    <!-- Progress Section -->
    <div class="card">
        <h3 class="text-lg font-semibold mb-4">Your Progress</h3>
        <div class="space-y-4">
            <!-- XP Progress -->
            <div>
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-300">Level {{ auth()->user()->level }} Progress</span>
                    <span class="text-sm text-gray-400">{{ auth()->user()->xp % 100 }}/100 XP</span>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-3">
                    <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-500"
                         style="width: {{ min(100, (auth()->user()->xp % 100)) }}%"></div>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ auth()->user()->getXpForNextLevel() }} XP needed for next level</p>
            </div>
        </div>
    </div>
</div>
@endsection
