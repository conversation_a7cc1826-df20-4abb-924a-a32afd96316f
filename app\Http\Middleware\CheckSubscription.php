<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $requiredPlan = null): Response
    {
        $user = $request->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has any subscription
        if (!$user->hasActiveSubscription()) {
            return $this->redirectToSubscription($request, 'You need an active subscription to access this content.');
        }

        // Check specific plan requirement
        if ($requiredPlan && !$user->hasSubscriptionPlan($requiredPlan)) {
            return $this->redirectToSubscription($request, "You need a {$requiredPlan} subscription to access this content.");
        }

        return $next($request);
    }

    /**
     * Redirect to subscription page with message
     */
    protected function redirectToSubscription(Request $request, string $message): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Subscription required',
                'message' => $message,
                'redirect' => route('pricing')
            ], 403);
        }

        return redirect()->route('pricing')->with('error', $message);
    }
}
