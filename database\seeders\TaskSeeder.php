<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Task;

class TaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, let's create a general campus for daily tasks
        $generalCampus = \App\Models\Campus::firstOrCreate([
            'name' => 'Daily Tasks',
            'slug' => 'daily-tasks',
        ], [
            'description' => 'Complete daily tasks to earn XP and maintain your streak',
            'color' => '#10B981',
            'is_active' => true,
            'sort_order' => 999,
        ]);

        $tasks = [
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Daily Login',
                'description' => 'Log in to your account to start your day',
                'task_type' => 'login',
                'xp_reward' => 5,
                'sort_order' => 1,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'easy',
            ],
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Complete a Lesson',
                'description' => 'Watch and complete any lesson from your courses',
                'task_type' => 'lesson',
                'xp_reward' => 20,
                'sort_order' => 2,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'medium',
            ],
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Update Your Profile',
                'description' => 'Keep your profile information up to date',
                'task_type' => 'profile',
                'xp_reward' => 10,
                'sort_order' => 3,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'easy',
            ],
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Share on Social Media',
                'description' => 'Share your progress or a lesson on social media',
                'task_type' => 'social',
                'xp_reward' => 15,
                'sort_order' => 4,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'easy',
            ],
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Invite a Friend',
                'description' => 'Share your referral code with someone new',
                'task_type' => 'referral',
                'xp_reward' => 25,
                'sort_order' => 5,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'medium',
            ],
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Join a Live Event',
                'description' => 'Participate in a live mentoring session or event',
                'task_type' => 'event',
                'xp_reward' => 30,
                'sort_order' => 6,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'hard',
            ],
            [
                'campus_id' => $generalCampus->id,
                'title' => 'Send a Chat Message',
                'description' => 'Engage with the community in campus chat rooms',
                'task_type' => 'chat',
                'xp_reward' => 8,
                'sort_order' => 7,
                'is_active' => true,
                'is_daily' => true,
                'difficulty' => 'easy',
            ],
        ];

        foreach ($tasks as $taskData) {
            Task::updateOrCreate(
                ['title' => $taskData['title']],
                $taskData
            );
        }
    }
}
