@extends('layouts.admin')

@section('title', 'Edit Course - ' . $course->title)
@section('page-title', 'Edit Course')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Edit Course</h1>
            <p class="text-gray-400 mt-1">Update course information and settings</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.courses.show', $course) }}" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Course
            </a>
            <a href="{{ route('admin.courses.index') }}" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Courses
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-gray-800 rounded-lg p-6">
        <form method="POST" action="{{ route('admin.courses.update', $course) }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Course Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Course Title</label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="{{ old('title', $course->title) }}"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                               placeholder="Enter course title"
                               required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Campus -->
                    <div>
                        <label for="campus_id" class="block text-sm font-medium text-gray-300 mb-2">Campus</label>
                        <select name="campus_id" 
                                id="campus_id" 
                                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                required>
                            <option value="">Select a campus</option>
                            @foreach($campuses as $campus)
                                <option value="{{ $campus->id }}" {{ old('campus_id', $course->campus_id) == $campus->id ? 'selected' : '' }}>
                                    {{ $campus->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('campus_id')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Difficulty Level -->
                    <div>
                        <label for="difficulty_level" class="block text-sm font-medium text-gray-300 mb-2">Difficulty Level</label>
                        <select name="difficulty_level" 
                                id="difficulty_level" 
                                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                required>
                            <option value="">Select difficulty</option>
                            <option value="beginner" {{ old('difficulty_level', $course->difficulty_level) == 'beginner' ? 'selected' : '' }}>Beginner</option>
                            <option value="intermediate" {{ old('difficulty_level', $course->difficulty_level) == 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                            <option value="advanced" {{ old('difficulty_level', $course->difficulty_level) == 'advanced' ? 'selected' : '' }}>Advanced</option>
                        </select>
                        @error('difficulty_level')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Estimated Duration -->
                    <div>
                        <label for="estimated_duration" class="block text-sm font-medium text-gray-300 mb-2">Estimated Duration (hours)</label>
                        <input type="number" 
                               name="estimated_duration" 
                               id="estimated_duration" 
                               value="{{ old('estimated_duration', $course->estimated_duration) }}"
                               min="1"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                               placeholder="e.g., 10">
                        @error('estimated_duration')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course Prerequisites -->
                    <div>
                        <label for="prerequisites" class="block text-sm font-medium text-gray-300 mb-2">Prerequisites</label>
                        <textarea name="prerequisites" 
                                  id="prerequisites" 
                                  rows="3"
                                  class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                  placeholder="List any prerequisites for this course...">{{ old('prerequisites', $course->prerequisites) }}</textarea>
                        @error('prerequisites')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Course Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Course Description</label>
                        <textarea name="description" 
                                  id="description" 
                                  rows="6"
                                  class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                  placeholder="Describe what students will learn in this course..."
                                  required>{{ old('description', $course->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Learning Objectives -->
                    <div>
                        <label for="learning_objectives" class="block text-sm font-medium text-gray-300 mb-2">Learning Objectives</label>
                        <textarea name="learning_objectives" 
                                  id="learning_objectives" 
                                  rows="4"
                                  class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                  placeholder="What will students achieve after completing this course?">{{ old('learning_objectives', $course->learning_objectives) }}</textarea>
                        @error('learning_objectives')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Course Options -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-white">Course Options</h3>
                        
                        <!-- Premium Course -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_premium" 
                                   id="is_premium" 
                                   value="1"
                                   {{ old('is_premium', $course->is_premium) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_premium" class="ml-2 text-sm text-gray-300">
                                Premium Course (requires subscription)
                            </label>
                        </div>

                        <!-- Active -->
                        <div class="flex items-center">
                            <input type="checkbox"
                                   name="is_active"
                                   id="is_active"
                                   value="1"
                                   {{ old('is_active', $course->is_active) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_active" class="ml-2 text-sm text-gray-300">
                                Published (visible to students)
                            </label>
                        </div>

                        <!-- Featured Course -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_featured" 
                                   id="is_featured" 
                                   value="1"
                                   {{ old('is_featured', $course->is_featured) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_featured" class="ml-2 text-sm text-gray-300">
                                Featured Course (show on homepage)
                            </label>
                        </div>
                    </div>

                    <!-- Course Image -->
                    <div>
                        <label for="image" class="block text-sm font-medium text-gray-300 mb-2">Course Image</label>
                        
                        @if($course->thumbnail_url)
                            <div class="mb-4">
                                <img src="{{ $course->thumbnail_url }}" alt="Current course image" class="w-32 h-20 object-cover rounded-lg">
                                <p class="text-xs text-gray-400 mt-1">Current image</p>
                            </div>
                        @endif
                        
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-600 border-dashed rounded-lg hover:border-gray-500 transition-colors">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-400">
                                    <label for="image" class="relative cursor-pointer bg-gray-800 rounded-md font-medium text-primary-400 hover:text-primary-300 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                        <span>Upload a new image</span>
                                        <input id="image" name="image" type="file" accept="image/*" class="sr-only">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-400">PNG, JPG, GIF up to 10MB</p>
                            </div>
                        </div>
                        @error('image')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-700">
                <a href="{{ route('admin.courses.show', $course) }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Course
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// File upload preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // You can add image preview functionality here
            console.log('Image selected:', file.name);
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endsection
