<?php

namespace Tests\Feature;

use App\Models\Campus;
use App\Models\Course;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CourseTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a campus for testing
        Campus::factory()->create();
    }

    public function test_guest_can_view_published_courses(): void
    {
        Course::factory()->published()->count(3)->create();
        Course::factory()->unpublished()->count(2)->create();

        $response = $this->get('/courses');

        $response->assertStatus(200);
        $response->assertViewIs('courses.index');
        $response->assertViewHas('courses');
        
        // Should only see published courses
        $courses = $response->viewData('courses');
        $this->assertCount(3, $courses);
    }

    public function test_guest_can_view_individual_published_course(): void
    {
        $course = Course::factory()->published()->create();

        $response = $this->get("/courses/{$course->slug}");

        $response->assertStatus(200);
        $response->assertViewIs('courses.show');
        $response->assertViewHas('course', $course);
    }

    public function test_guest_cannot_view_unpublished_course(): void
    {
        $course = Course::factory()->unpublished()->create();

        $response = $this->get("/courses/{$course->slug}");

        $response->assertStatus(404);
    }

    public function test_authenticated_user_can_enroll_in_course(): void
    {
        $user = User::factory()->create();
        $course = Course::factory()->published()->create();

        $response = $this->actingAs($user)->post("/courses/{$course->slug}/enroll");

        $response->assertRedirect();
        $this->assertDatabaseHas('course_enrollments', [
            'user_id' => $user->id,
            'course_id' => $course->id,
        ]);
    }

    public function test_guest_cannot_enroll_in_course(): void
    {
        $course = Course::factory()->published()->create();

        $response = $this->post("/courses/{$course->slug}/enroll");

        $response->assertRedirect('/login');
    }

    public function test_user_cannot_enroll_in_premium_course_without_subscription(): void
    {
        $user = User::factory()->create();
        $course = Course::factory()->published()->premium()->create();

        $response = $this->actingAs($user)->post("/courses/{$course->slug}/enroll");

        $response->assertRedirect('/pricing');
        $this->assertDatabaseMissing('course_enrollments', [
            'user_id' => $user->id,
            'course_id' => $course->id,
        ]);
    }

    public function test_admin_can_view_course_management_page(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/admin/courses');

        $response->assertStatus(200);
        $response->assertViewIs('admin.courses.index');
    }

    public function test_admin_can_create_course(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        $campus = Campus::factory()->create();

        $courseData = [
            'title' => 'Test Course',
            'description' => 'This is a test course description that is long enough to pass validation.',
            'campus_id' => $campus->id,
            'difficulty_level' => 'beginner',
            'estimated_duration' => 10,
            'is_published' => true,
        ];

        $response = $this->actingAs($admin)->post('/admin/courses', $courseData);

        $response->assertRedirect();
        $this->assertDatabaseHas('courses', [
            'title' => 'Test Course',
            'campus_id' => $campus->id,
        ]);
    }

    public function test_admin_can_update_course(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        $course = Course::factory()->create();

        $updateData = [
            'title' => 'Updated Course Title',
            'description' => 'This is an updated course description that is long enough to pass validation.',
            'campus_id' => $course->campus_id,
            'difficulty_level' => 'intermediate',
            'estimated_duration' => 15,
            'is_published' => false,
        ];

        $response = $this->actingAs($admin)->put("/admin/courses/{$course->id}", $updateData);

        $response->assertRedirect();
        $this->assertDatabaseHas('courses', [
            'id' => $course->id,
            'title' => 'Updated Course Title',
            'difficulty_level' => 'intermediate',
        ]);
    }

    public function test_admin_can_delete_course(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        $course = Course::factory()->create();

        $response = $this->actingAs($admin)->delete("/admin/courses/{$course->id}");

        $response->assertRedirect();
        $this->assertDatabaseMissing('courses', [
            'id' => $course->id,
        ]);
    }

    public function test_regular_user_cannot_access_course_management(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/admin/courses');

        $response->assertStatus(403);
    }

    public function test_course_search_functionality(): void
    {
        Course::factory()->published()->create(['title' => 'Laravel Development']);
        Course::factory()->published()->create(['title' => 'React Fundamentals']);
        Course::factory()->published()->create(['title' => 'Python Basics']);

        $response = $this->get('/courses?search=Laravel');

        $response->assertStatus(200);
        $response->assertSee('Laravel Development');
        $response->assertDontSee('React Fundamentals');
        $response->assertDontSee('Python Basics');
    }

    public function test_course_filtering_by_difficulty(): void
    {
        Course::factory()->published()->create(['difficulty_level' => 'beginner']);
        Course::factory()->published()->create(['difficulty_level' => 'intermediate']);
        Course::factory()->published()->create(['difficulty_level' => 'advanced']);

        $response = $this->get('/courses?difficulty=beginner');

        $response->assertStatus(200);
        $courses = $response->viewData('courses');
        $this->assertTrue($courses->every(fn($course) => $course->difficulty_level === 'beginner'));
    }

    public function test_course_filtering_by_campus(): void
    {
        $campus1 = Campus::factory()->create(['name' => 'Business Campus']);
        $campus2 = Campus::factory()->create(['name' => 'Tech Campus']);
        
        Course::factory()->published()->create(['campus_id' => $campus1->id]);
        Course::factory()->published()->create(['campus_id' => $campus2->id]);

        $response = $this->get("/courses?campus={$campus1->id}");

        $response->assertStatus(200);
        $courses = $response->viewData('courses');
        $this->assertTrue($courses->every(fn($course) => $course->campus_id === $campus1->id));
    }

    public function test_featured_courses_appear_first(): void
    {
        $regularCourse = Course::factory()->published()->create(['is_featured' => false]);
        $featuredCourse = Course::factory()->published()->create(['is_featured' => true]);

        $response = $this->get('/courses');

        $response->assertStatus(200);
        $courses = $response->viewData('courses');
        $this->assertEquals($featuredCourse->id, $courses->first()->id);
    }

    public function test_course_enrollment_count_is_tracked(): void
    {
        $course = Course::factory()->published()->create();
        $users = User::factory()->count(3)->create();

        foreach ($users as $user) {
            $this->actingAs($user)->post("/courses/{$course->slug}/enroll");
        }

        $course->refresh();
        $this->assertEquals(3, $course->enrollments()->count());
    }
}
