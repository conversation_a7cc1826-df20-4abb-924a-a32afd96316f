<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function index()
    {
        $user = Auth::user();

        return view('dashboard.index', compact('user'));
    }

    public function profile()
    {
        $user = Auth::user();

        return view('dashboard.profile', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'bio' => 'nullable|string|max:1000',
            'location' => 'nullable|string|max:255',
            'social_twitter' => 'nullable|string|max:255',
            'social_instagram' => 'nullable|string|max:255',
            'social_linkedin' => 'nullable|string|max:255',
        ]);

        $user->update($request->only([
            'name', 'bio', 'location', 'social_twitter',
            'social_instagram', 'social_linkedin'
        ]));

        return redirect()->route('dashboard.profile')->with('success', 'Profile updated successfully!');
    }

    public function campuses()
    {
        return view('dashboard.campuses');
    }

    public function tasks()
    {
        return view('dashboard.tasks');
    }

    public function events()
    {
        return view('dashboard.events');
    }

    public function chat()
    {
        return view('dashboard.chat');
    }

    public function leaderboard()
    {
        return view('dashboard.leaderboard');
    }

    public function settings()
    {
        return view('dashboard.settings');
    }
}
