@extends('layouts.dashboard')

@section('title', $campus->name . ' Campus - The Real World')
@section('page-title', $campus->name . ' Campus')

@section('content')
<div class="p-6">
    <!-- Campus Header -->
    <div class="card mb-8">
        <div class="flex items-start">
            <!-- Campus Icon -->
            <div class="w-20 h-20 rounded-xl mr-6 flex items-center justify-center text-4xl flex-shrink-0" 
                 style="background-color: {{ $campus->color }}20; border: 2px solid {{ $campus->color }};">
                @if($campus->icon_url)
                    <img src="{{ $campus->icon_url }}" alt="{{ $campus->name }}" class="w-12 h-12">
                @else
                    <span style="color: {{ $campus->color }}">🎯</span>
                @endif
            </div>

            <!-- Campus Info -->
            <div class="flex-1">
                <h1 class="text-3xl font-bold mb-2">{{ $campus->name }}</h1>
                <p class="text-gray-300 mb-4 leading-relaxed">{{ $campus->description }}</p>
                
                <!-- Campus Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <span class="text-gray-300">{{ $courses->count() }} Courses</span>
                    </div>
                    
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="text-gray-300">{{ $courses->sum('duration_minutes') }} Minutes</span>
                    </div>
                    
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span class="text-gray-300">{{ ucfirst($courses->first()->difficulty ?? 'Mixed') }} Level</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Courses Grid -->
    @if($courses->count() > 0)
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        @foreach($courses as $course)
        <div class="card hover:transform hover:scale-105 transition-all duration-300">
            <!-- Course Header -->
            <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                    <h3 class="text-xl font-bold mb-2">{{ $course->title }}</h3>
                    <p class="text-gray-400 text-sm mb-3">{{ $course->description }}</p>
                </div>
                
                <!-- Difficulty Badge -->
                <span class="bg-{{ $course->difficulty === 'beginner' ? 'green' : ($course->difficulty === 'intermediate' ? 'yellow' : 'red') }}-600 text-white text-xs px-2 py-1 rounded-full ml-4">
                    {{ ucfirst($course->difficulty) }}
                </span>
            </div>

            <!-- Course Stats -->
            <div class="grid grid-cols-3 gap-4 mb-4 text-sm">
                <div class="text-center">
                    <p class="text-gray-400">Lessons</p>
                    <p class="font-semibold">{{ $course->total_lessons }}</p>
                </div>
                <div class="text-center">
                    <p class="text-gray-400">Duration</p>
                    <p class="font-semibold">{{ $course->duration_minutes }}m</p>
                </div>
                <div class="text-center">
                    <p class="text-gray-400">Progress</p>
                    <p class="font-semibold">{{ round($course->progress_percentage) }}%</p>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="mb-4">
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div class="h-2 rounded-full transition-all duration-300" 
                         style="width: {{ $course->progress_percentage }}%; background-color: {{ $campus->color }}"></div>
                </div>
                <p class="text-xs text-gray-500 mt-1">
                    {{ $course->completed_lessons }}/{{ $course->total_lessons }} lessons completed
                </p>
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3">
                @if($course->progress_percentage > 0)
                    <a href="{{ route('dashboard.courses.show', $course) }}" 
                       class="btn-primary flex-1 text-center">
                        Continue Course
                    </a>
                @else
                    <form action="{{ route('dashboard.courses.start', $course) }}" method="POST" class="flex-1">
                        @csrf
                        <button type="submit" class="btn-primary w-full">
                            Start Course
                        </button>
                    </form>
                @endif
                
                <a href="{{ route('dashboard.courses.show', $course) }}" 
                   class="btn-secondary px-4 py-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </a>
            </div>
        </div>
        @endforeach
    </div>
    @else
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">No Courses Available</h3>
        <p class="text-gray-400 mb-6">This campus is still being developed. Check back soon for new courses!</p>
        <a href="{{ route('dashboard.campuses') }}" class="btn-primary">
            Browse Other Campuses
        </a>
    </div>
    @endif

    <!-- Back Button -->
    <div class="mt-8">
        <a href="{{ route('dashboard.campuses') }}" 
           class="inline-flex items-center text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Campuses
        </a>
    </div>
</div>
@endsection
