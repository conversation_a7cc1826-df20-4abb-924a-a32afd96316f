@extends('layouts.admin')

@section('title', 'Lesson Details - ' . $lesson->title)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Lesson Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.lessons.index') }}">Lessons</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.courses.show', $lesson->course) }}">{{ $lesson->course->title }}</a></li>
                    <li class="breadcrumb-item active">{{ $lesson->title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.lessons.edit', $lesson) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Lesson
            </a>
            <a href="{{ route('admin.courses.show', $lesson->course) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Course
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Lesson Information -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lesson Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Lesson Title</label>
                                <p class="form-control-plaintext">{{ $lesson->title }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Lesson Order</label>
                                <p class="form-control-plaintext">
                                    <span class="badge badge-secondary">{{ $lesson->sort_order ?? 'N/A' }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Description</label>
                        <p class="form-control-plaintext">{{ $lesson->description }}</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Course</label>
                                <p class="form-control-plaintext">
                                    <span class="badge" style="background-color: {{ $lesson->course->campus->color }}; color: white;">
                                        {{ $lesson->course->campus->name }}
                                    </span>
                                    {{ $lesson->course->title }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Status</label>
                                <p class="form-control-plaintext">
                                    @if($lesson->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                    @if($lesson->is_preview)
                                        <span class="badge badge-info ml-1">Preview</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($lesson->content)
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Lesson Content</label>
                        <div class="bg-light p-3 rounded">
                            {!! nl2br(e($lesson->content)) !!}
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Duration</label>
                                <p class="form-control-plaintext">
                                    @if($lesson->duration_seconds)
                                        {{ gmdate('H:i:s', $lesson->duration_seconds) }}
                                    @else
                                        <span class="text-muted">Not specified</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">XP Reward</label>
                                <p class="form-control-plaintext">
                                    <span class="badge badge-success">{{ $lesson->xp_reward ?? 0 }} XP</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Created</label>
                                <p class="form-control-plaintext">{{ $lesson->created_at->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>

                    @if($lesson->video_url)
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Video</label>
                        <div class="bg-light p-3 rounded">
                            <div class="d-flex align-items-center justify-between">
                                <div>
                                    <p class="mb-1"><strong>URL:</strong> <a href="{{ $lesson->video_url }}" target="_blank">{{ $lesson->video_url }}</a></p>
                                    @if($lesson->video_type)
                                        <p class="mb-0"><strong>Type:</strong> {{ ucfirst($lesson->video_type) }}</p>
                                    @endif
                                </div>
                                <a href="{{ $lesson->video_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-play"></i> Watch Video
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif

                    @if($lesson->resource_url)
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Additional Resources</label>
                        <div class="bg-light p-3 rounded">
                            <a href="{{ $lesson->resource_url }}" target="_blank" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-download"></i> Download Resource
                            </a>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Student Progress -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Student Progress</h6>
                </div>
                <div class="card-body">
                    @if($lesson->userProgress()->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Progress</th>
                                        <th>Completed</th>
                                        <th>Last Activity</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lesson->userProgress()->with('user')->latest()->take(10)->get() as $progress)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center mr-2">
                                                    <span class="text-white font-weight-bold">{{ substr($progress->user->name, 0, 1) }}</span>
                                                </div>
                                                <div>
                                                    <strong>{{ $progress->user->name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ $progress->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ $progress->progress_percentage }}%" 
                                                     aria-valuenow="{{ $progress->progress_percentage }}" 
                                                     aria-valuemin="0" aria-valuemax="100">
                                                    {{ $progress->progress_percentage }}%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($progress->is_completed)
                                                <span class="badge badge-success">Completed</span>
                                            @else
                                                <span class="badge badge-warning">In Progress</span>
                                            @endif
                                        </td>
                                        <td>{{ $progress->updated_at->diffForHumans() }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if($lesson->userProgress()->count() > 10)
                        <div class="text-center mt-3">
                            <small class="text-muted">Showing 10 of {{ $lesson->userProgress()->count() }} students</small>
                        </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No student progress yet</h5>
                            <p class="text-muted">Students haven't started this lesson yet.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Lesson Statistics -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Lesson Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-right">
                                <h3 class="text-primary">{{ $lesson->userProgress()->count() }}</h3>
                                <small class="text-muted">Total Views</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success">{{ $lesson->userProgress()->where('is_completed', true)->count() }}</h3>
                            <small class="text-muted">Completions</small>
                        </div>
                    </div>
                    
                    @if($lesson->userProgress()->count() > 0)
                    <div class="row text-center">
                        <div class="col-12">
                            <h4 class="text-info">
                                {{ round(($lesson->userProgress()->where('is_completed', true)->count() / $lesson->userProgress()->count()) * 100, 1) }}%
                            </h4>
                            <small class="text-muted">Completion Rate</small>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.lessons.edit', $lesson) }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit Lesson
                        </a>
                        <a href="{{ route('admin.courses.show', $lesson->course) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-book"></i> View Course
                        </a>
                        @if($lesson->video_url)
                        <a href="{{ $lesson->video_url }}" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-play"></i> Watch Video
                        </a>
                        @endif
                        @if($lesson->resource_url)
                        <a href="{{ $lesson->resource_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-download"></i> Download Resource
                        </a>
                        @endif
                        @if($lesson->userProgress()->count() == 0)
                        <form action="{{ route('admin.lessons.destroy', $lesson) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this lesson?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Lesson
                            </button>
                        </form>
                        @else
                        <button class="btn btn-outline-danger btn-sm" disabled title="Cannot delete lesson with student progress">
                            <i class="fas fa-trash"></i> Cannot Delete (Has Progress)
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}
</style>
@endpush
