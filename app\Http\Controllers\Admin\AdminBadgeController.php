<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Badge;
use Illuminate\Http\Request;

class AdminBadgeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $badges = Badge::withCount('users')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.badges.index', compact('badges'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.badges.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'required|string|max:10',
            'color' => 'required|string|max:7',
            'type' => 'required|in:achievement,milestone,special',
            'criteria' => 'required|array',
            'xp_reward' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        $data['criteria'] = json_encode($request->criteria);

        Badge::create($data);

        return redirect()->route('admin.badges.index')
            ->with('success', 'Badge created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Badge $badge)
    {
        $badge->load(['users']);
        
        return view('admin.badges.show', compact('badge'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Badge $badge)
    {
        return view('admin.badges.edit', compact('badge'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Badge $badge)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'icon' => 'required|string|max:10',
            'color' => 'required|string|max:7',
            'type' => 'required|in:achievement,milestone,special',
            'criteria' => 'required|array',
            'xp_reward' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
        ]);

        $data = $request->all();
        $data['criteria'] = json_encode($request->criteria);

        $badge->update($data);

        return redirect()->route('admin.badges.index')
            ->with('success', 'Badge updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Badge $badge)
    {
        if ($badge->users()->count() > 0) {
            return redirect()->route('admin.badges.index')
                ->with('error', 'Cannot delete badge that has been awarded to users.');
        }

        $badge->delete();

        return redirect()->route('admin.badges.index')
            ->with('success', 'Badge deleted successfully.');
    }
}
