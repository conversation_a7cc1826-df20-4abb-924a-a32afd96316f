<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('event_rsvps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->enum('status', ['attending', 'maybe', 'not_attending'])->default('attending');
            $table->boolean('attended')->default(false); // Marked during/after event
            $table->timestamp('rsvp_at');
            $table->timestamps();

            $table->unique(['user_id', 'event_id']);
            $table->index(['event_id', 'status']);
            $table->index('attended');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_rsvps');
    }
};
