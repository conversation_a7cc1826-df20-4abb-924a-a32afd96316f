<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Badge;
use App\Models\UserAchievement;
use Illuminate\Support\Facades\Auth;

class BadgeController extends Controller
{
    /**
     * Display user's badges and achievements
     */
    public function index()
    {
        $user = Auth::user();

        // Get user's badges
        $userBadges = $user->badges()->with('pivot')->get();

        // Get user's achievements
        $achievements = UserAchievement::where('user_id', $user->id)
            ->orderBy('earned_at', 'desc')
            ->get();

        // Get available badges (not earned yet)
        $availableBadges = Badge::whereNotIn('id', $userBadges->pluck('id'))
            ->where('is_active', true)
            ->get();

        // Group achievements by type
        $achievementsByType = $achievements->groupBy('achievement_type');

        // Calculate badge progress
        $badgeProgress = [];
        foreach ($availableBadges as $badge) {
            $progress = $this->calculateBadgeProgress($user, $badge);
            $badgeProgress[$badge->id] = $progress;
        }

        return view('dashboard.badges.index', compact(
            'userBadges',
            'achievements',
            'availableBadges',
            'achievementsByType',
            'badgeProgress'
        ));
    }

    /**
     * Calculate progress towards earning a badge
     */
    private function calculateBadgeProgress($user, $badge): array
    {
        $requirements = $badge->requirements ?? [];
        $progress = [];

        foreach ($requirements as $requirement => $target) {
            switch ($requirement) {
                case 'courses_completed':
                    $current = $user->courseEnrollments()->where('progress_percentage', 100)->count();
                    break;
                case 'total_xp':
                    $current = $user->xp;
                    break;
                case 'login_streak':
                    $current = $user->login_streak ?? 0;
                    break;
                case 'events_attended':
                    $current = $user->eventRsvps()->where('attended', true)->count();
                    break;
                case 'chat_messages':
                    $current = $user->chatMessages()->count();
                    break;
                default:
                    $current = 0;
            }

            $progress[$requirement] = [
                'current' => $current,
                'target' => $target,
                'percentage' => min(100, ($current / $target) * 100)
            ];
        }

        return $progress;
    }

    /**
     * Display specific badge details
     */
    public function show(Badge $badge)
    {
        $user = Auth::user();
        $userBadge = $user->badges()->where('badge_id', $badge->id)->first();
        $progress = $this->calculateBadgeProgress($user, $badge);

        return view('dashboard.badges.show', compact('badge', 'userBadge', 'progress'));
    }
}
