<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Campus;
use Illuminate\Http\Request;

class AdminCampusController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $campuses = Campus::withCount(['courses'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Add user count manually for each campus
        foreach ($campuses as $campus) {
            $campus->users_count = \DB::table('course_enrollments')
                ->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
                ->where('courses.campus_id', $campus->id)
                ->distinct('course_enrollments.user_id')
                ->count('course_enrollments.user_id');
        }

        return view('admin.campuses.index', compact('campuses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.campuses.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:campuses,slug',
            'description' => 'required|string',
            'teaser_description' => 'nullable|string',
            'icon_url' => 'nullable|url|max:255',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_premium' => 'boolean',
            'required_plans' => 'nullable|array',
            'required_plans.*' => 'in:prosper,conquer,champions',
        ]);

        Campus::create($request->all());

        return redirect()->route('admin.campuses.index')
            ->with('success', 'Campus created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Campus $campus)
    {
        $campus->load(['courses.enrollments']);
        $campus->loadCount(['courses']);

        // Calculate user count manually
        $campus->users_count = \DB::table('course_enrollments')
            ->join('courses', 'course_enrollments.course_id', '=', 'courses.id')
            ->where('courses.campus_id', $campus->id)
            ->distinct('course_enrollments.user_id')
            ->count('course_enrollments.user_id');

        return view('admin.campuses.show', compact('campus'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Campus $campus)
    {
        return view('admin.campuses.edit', compact('campus'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Campus $campus)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:campuses,slug,' . $campus->id,
            'description' => 'required|string',
            'teaser_description' => 'nullable|string',
            'icon_url' => 'nullable|url|max:255',
            'color' => 'nullable|string|max:7',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_premium' => 'boolean',
            'required_plans' => 'nullable|array',
            'required_plans.*' => 'in:prosper,conquer,champions',
        ]);

        $campus->update($request->all());

        return redirect()->route('admin.campuses.index')
            ->with('success', 'Campus updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Campus $campus)
    {
        if ($campus->courses()->count() > 0) {
            return redirect()->route('admin.campuses.index')
                ->with('error', 'Cannot delete campus with existing courses.');
        }

        $campus->delete();

        return redirect()->route('admin.campuses.index')
            ->with('success', 'Campus deleted successfully.');
    }
}
