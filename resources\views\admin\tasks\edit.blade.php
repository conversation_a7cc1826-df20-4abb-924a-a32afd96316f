@extends('layouts.admin')

@section('title', 'Edit Task - ' . $task->title)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Task</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.tasks.index') }}">Tasks</a></li>
                    <li class="breadcrumb-item active">Edit {{ $task->title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.tasks.show', $task) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Task
            </a>
            <a href="{{ route('admin.tasks.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Tasks
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Task Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Task Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.tasks.update', $task) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="mb-3">
                            <label for="title" class="form-label">Task Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $task->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description', $task->description) }}</textarea>
                            <small class="form-text text-muted">Detailed description of what needs to be done</small>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Task Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Select task type</option>
                                        <option value="daily" {{ old('type', $task->type) === 'daily' ? 'selected' : '' }}>Daily Task</option>
                                        <option value="weekly" {{ old('type', $task->type) === 'weekly' ? 'selected' : '' }}>Weekly Task</option>
                                        <option value="monthly" {{ old('type', $task->type) === 'monthly' ? 'selected' : '' }}>Monthly Task</option>
                                        <option value="one_time" {{ old('type', $task->type) === 'one_time' ? 'selected' : '' }}>One-time Task</option>
                                        <option value="milestone" {{ old('type', $task->type) === 'milestone' ? 'selected' : '' }}>Milestone</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category</label>
                                    <select class="form-select @error('category') is-invalid @enderror" id="category" name="category">
                                        <option value="">Select category</option>
                                        <option value="learning" {{ old('category', $task->category) === 'learning' ? 'selected' : '' }}>Learning</option>
                                        <option value="social" {{ old('category', $task->category) === 'social' ? 'selected' : '' }}>Social</option>
                                        <option value="achievement" {{ old('category', $task->category) === 'achievement' ? 'selected' : '' }}>Achievement</option>
                                        <option value="engagement" {{ old('category', $task->category) === 'engagement' ? 'selected' : '' }}>Engagement</option>
                                        <option value="special" {{ old('category', $task->category) === 'special' ? 'selected' : '' }}>Special</option>
                                    </select>
                                    @error('category')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="xp_reward" class="form-label">XP Reward</label>
                                    <input type="number" class="form-control @error('xp_reward') is-invalid @enderror" 
                                           id="xp_reward" name="xp_reward" value="{{ old('xp_reward', $task->xp_reward) }}" min="0" max="1000">
                                    <small class="form-text text-muted">XP points awarded for completion</small>
                                    @error('xp_reward')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="difficulty" class="form-label">Difficulty</label>
                                    <select class="form-select @error('difficulty') is-invalid @enderror" id="difficulty" name="difficulty">
                                        <option value="">Select difficulty</option>
                                        <option value="easy" {{ old('difficulty', $task->difficulty) === 'easy' ? 'selected' : '' }}>Easy</option>
                                        <option value="medium" {{ old('difficulty', $task->difficulty) === 'medium' ? 'selected' : '' }}>Medium</option>
                                        <option value="hard" {{ old('difficulty', $task->difficulty) === 'hard' ? 'selected' : '' }}>Hard</option>
                                        <option value="expert" {{ old('difficulty', $task->difficulty) === 'expert' ? 'selected' : '' }}>Expert</option>
                                    </select>
                                    @error('difficulty')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">Requirements (JSON)</label>
                            <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                      id="requirements" name="requirements" rows="4">{{ old('requirements', json_encode($task->requirements ?? [], JSON_PRETTY_PRINT)) }}</textarea>
                            <small class="form-text text-muted">JSON object defining task completion requirements</small>
                            @error('requirements')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start_date" class="form-label">Start Date</label>
                                    <input type="datetime-local" class="form-control @error('start_date') is-invalid @enderror" 
                                           id="start_date" name="start_date" value="{{ old('start_date', $task->start_date ? $task->start_date->format('Y-m-d\TH:i') : '') }}">
                                    <small class="form-text text-muted">When this task becomes available</small>
                                    @error('start_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end_date" class="form-label">End Date</label>
                                    <input type="datetime-local" class="form-control @error('end_date') is-invalid @enderror" 
                                           id="end_date" name="end_date" value="{{ old('end_date', $task->end_date ? $task->end_date->format('Y-m-d\TH:i') : '') }}">
                                    <small class="form-text text-muted">When this task expires (optional)</small>
                                    @error('end_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_completions" class="form-label">Max Completions</label>
                                    <input type="number" class="form-control @error('max_completions') is-invalid @enderror" 
                                           id="max_completions" name="max_completions" value="{{ old('max_completions', $task->max_completions) }}" min="1">
                                    <small class="form-text text-muted">Maximum times this task can be completed (leave empty for unlimited)</small>
                                    @error('max_completions')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', $task->sort_order) }}" min="0">
                                    <small class="form-text text-muted">Display order (lower numbers first)</small>
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', $task->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            <strong>Active</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Task is available to users</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                               {{ old('is_featured', $task->is_featured) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            <strong>Featured Task</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Highlight this task</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.tasks.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Task
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Task Preview & Stats -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Task Preview</h6>
                </div>
                <div class="card-body">
                    <div class="task-preview">
                        <div class="d-flex align-items-center mb-3">
                            <div class="task-icon bg-primary rounded-circle d-flex align-items-center justify-content-center mr-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-tasks text-white"></i>
                            </div>
                            <div>
                                <h6 class="task-title mb-0">{{ $task->title }}</h6>
                                <small class="text-muted task-type">{{ ucfirst(str_replace('_', ' ', $task->type)) }}</small>
                            </div>
                        </div>
                        <p class="task-description text-muted mb-3">{{ $task->description }}</p>
                        <div class="task-meta">
                            <span class="badge badge-success task-xp">{{ $task->xp_reward ?? 0 }} XP</span>
                            @if($task->difficulty)
                                <span class="badge badge-info task-difficulty">{{ ucfirst($task->difficulty) }}</span>
                            @endif
                            @if($task->is_featured)
                                <span class="badge badge-warning">Featured</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task Statistics -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Task Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $task->userTasks()->count() }}</h4>
                                <small class="text-muted">Total Attempts</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $task->userTasks()->where('is_completed', true)->count() }}</h4>
                            <small class="text-muted">Completions</small>
                        </div>
                    </div>
                    
                    @if($task->userTasks()->count() > 0)
                    <hr>
                    <div class="text-center">
                        <h5 class="text-info">
                            {{ round(($task->userTasks()->where('is_completed', true)->count() / $task->userTasks()->count()) * 100, 1) }}%
                        </h5>
                        <small class="text-muted">Completion Rate</small>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.tasks.show', $task) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        @if($task->userTasks()->count() == 0)
                        <form action="{{ route('admin.tasks.destroy', $task) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this task?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Task
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    document.getElementById('title').addEventListener('input', function() {
        document.querySelector('.task-title').textContent = this.value || '{{ $task->title }}';
    });

    document.getElementById('description').addEventListener('input', function() {
        document.querySelector('.task-description').textContent = this.value || '{{ $task->description }}';
    });

    document.getElementById('type').addEventListener('change', function() {
        const typeText = this.options[this.selectedIndex].text;
        document.querySelector('.task-type').textContent = typeText || '{{ ucfirst(str_replace("_", " ", $task->type)) }}';
    });

    document.getElementById('xp_reward').addEventListener('input', function() {
        document.querySelector('.task-xp').textContent = (this.value || 0) + ' XP';
    });

    document.getElementById('difficulty').addEventListener('change', function() {
        const difficultyText = this.options[this.selectedIndex].text;
        if (difficultyText && difficultyText !== 'Select difficulty') {
            document.querySelector('.task-difficulty').textContent = difficultyText;
        }
    });
});
</script>
@endpush
