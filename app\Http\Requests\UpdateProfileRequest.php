<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $userId = auth()->id();

        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/',
            ],
            'bio' => [
                'nullable',
                'string',
                'max:500',
            ],
            'location' => [
                'nullable',
                'string',
                'max:100',
            ],
            'social_twitter' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^@?[A-Za-z0-9_]+$/',
            ],
            'social_instagram' => [
                'nullable',
                'string',
                'max:50',
                'regex:/^@?[A-Za-z0-9_\.]+$/',
            ],
            'social_linkedin' => [
                'nullable',
                'string',
                'max:100',
                'url',
            ],
            'avatar' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg,gif',
                'max:2048', // 2MB
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The name may only contain letters, spaces, hyphens, apostrophes, and dots.',
            'social_twitter.regex' => 'Please enter a valid Twitter username.',
            'social_instagram.regex' => 'Please enter a valid Instagram username.',
            'social_linkedin.url' => 'Please enter a valid LinkedIn URL.',
            'avatar.max' => 'The avatar must not be larger than 2MB.',
            'bio.max' => 'The bio must not exceed 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean up social media handles
        if ($this->social_twitter) {
            $this->merge([
                'social_twitter' => ltrim($this->social_twitter, '@'),
            ]);
        }

        if ($this->social_instagram) {
            $this->merge([
                'social_instagram' => ltrim($this->social_instagram, '@'),
            ]);
        }

        // Trim whitespace
        $this->merge([
            'name' => trim($this->name),
            'bio' => trim($this->bio),
            'location' => trim($this->location),
        ]);
    }
}
