<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAchievement extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'achievement_type',
        'achievement_name',
        'description',
        'icon',
        'xp_reward',
        'metadata',
        'earned_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'earned_at' => 'datetime',
    ];

    /**
     * Get the user that owns the achievement
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Achievement types and their configurations
     */
    public static function getAchievementTypes(): array
    {
        return [
            'first_login' => [
                'name' => 'Welcome Warrior',
                'description' => 'Logged in for the first time',
                'icon' => '🎯',
                'xp_reward' => 10,
            ],
            'first_course' => [
                'name' => 'Learning Begins',
                'description' => 'Enrolled in your first course',
                'icon' => '📚',
                'xp_reward' => 25,
            ],
            'course_completed' => [
                'name' => 'Course Crusher',
                'description' => 'Completed a course',
                'icon' => '🏆',
                'xp_reward' => 100,
            ],
            'level_up' => [
                'name' => 'Level Master',
                'description' => 'Reached a new level',
                'icon' => '⭐',
                'xp_reward' => 50,
            ],
            'streak_7' => [
                'name' => 'Week Warrior',
                'description' => 'Maintained a 7-day login streak',
                'icon' => '🔥',
                'xp_reward' => 75,
            ],
            'streak_30' => [
                'name' => 'Month Master',
                'description' => 'Maintained a 30-day login streak',
                'icon' => '💎',
                'xp_reward' => 200,
            ],
            'event_attended' => [
                'name' => 'Event Enthusiast',
                'description' => 'Attended a live event',
                'icon' => '🎪',
                'xp_reward' => 50,
            ],
            'chat_active' => [
                'name' => 'Community Champion',
                'description' => 'Sent 100 chat messages',
                'icon' => '💬',
                'xp_reward' => 75,
            ],
            'subscription_upgrade' => [
                'name' => 'Premium Player',
                'description' => 'Upgraded to premium subscription',
                'icon' => '👑',
                'xp_reward' => 150,
            ],
        ];
    }

    /**
     * Award an achievement to a user
     */
    public static function award(User $user, string $type, array $metadata = []): ?self
    {
        $achievements = self::getAchievementTypes();

        if (!isset($achievements[$type])) {
            return null;
        }

        // Check if user already has this achievement
        $existing = self::where('user_id', $user->id)
            ->where('achievement_type', $type)
            ->first();

        if ($existing) {
            return $existing;
        }

        $config = $achievements[$type];

        $achievement = self::create([
            'user_id' => $user->id,
            'achievement_type' => $type,
            'achievement_name' => $config['name'],
            'description' => $config['description'],
            'icon' => $config['icon'],
            'xp_reward' => $config['xp_reward'],
            'metadata' => $metadata,
            'earned_at' => now(),
        ]);

        // Award XP to user
        $user->addXp($config['xp_reward']);

        return $achievement;
    }
}
