includes:
    - ./vendor/nunomaduro/larastan/extension.neon

parameters:
    level: 5
    paths:
        - app
        - config
        - database
        - routes
        - tests
    
    # Exclude problematic paths
    excludePaths:
        - bootstrap/cache
        - storage
        - vendor
        - node_modules
        - app/Console/Kernel.php
        - app/Http/Kernel.php
    
    # Laravel specific configurations
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    
    # Ignore common Laravel patterns
    ignoreErrors:
        # Ignore missing property errors for Eloquent models
        - '#Access to an undefined property [a-zA-Z0-9\\_]+::\$[a-zA-Z0-9_]+#'
        
        # Ignore missing method errors for Eloquent models
        - '#Call to an undefined method [a-zA-Z0-9\\_]+::[a-zA-Z0-9_]+\(\)#'
        
        # Ignore facade errors
        - '#Call to an undefined static method Illuminate\\Support\\Facades\\[a-zA-Z]+::[a-zA-Z0-9_]+\(\)#'
        
        # Ignore helper function errors
        - '#Function [a-zA-Z0-9_]+ not found#'
        
        # Ignore view errors
        - '#Cannot call method [a-zA-Z0-9_]+\(\) on Illuminate\\Contracts\\View\\View\|Illuminate\\Contracts\\View\\Factory#'
        
        # Ignore request validation errors
        - '#Method [a-zA-Z0-9\\_]+::[a-zA-Z0-9_]+\(\) should return [a-zA-Z0-9\\_]+ but returns mixed#'
        
        # Ignore collection method errors
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Collection::[a-zA-Z0-9_]+\(\)#'
        
        # Ignore factory errors
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Factories\\Factory::[a-zA-Z0-9_]+\(\)#'
    
    # Bootstrap files
    bootstrapFiles:
        - bootstrap/app.php
    
    # Doctrine extensions
    doctrine:
        objectManagerLoader: tests/object-manager.php
    
    # Custom rules
    rules:
        - NunoMaduro\Larastan\Rules\NoModelMakeRule
        - NunoMaduro\Larastan\Rules\NoUnnecessaryCollectionCallRule
        - NunoMaduro\Larastan\Rules\ModelPropertyRule
        - NunoMaduro\Larastan\Rules\NoUselessWithRule
    
    # Type coverage
    reportUnmatchedIgnoredErrors: false
    checkTooWideReturnTypesInProtectedAndPublicMethods: true
    checkUninitializedProperties: true
    checkImplicitMixed: false
    
    # Memory limit
    memoryLimitFile: .phpstan-memory-limit
    
    # Parallel processing
    parallel:
        jobSize: 20
        maximumNumberOfProcesses: 32
        minimumNumberOfJobsPerProcess: 2
    
    # Custom extensions
    scanDirectories:
        - app
        - database
    
    scanFiles:
        - config/app.php
        - config/database.php
        - config/services.php
    
    # Stub files for better analysis
    stubFiles:
        - stubs/User.stub
        - stubs/Model.stub
    
    # Dynamic return type extensions
    dynamicReturnTypeExtensions:
        - NunoMaduro\Larastan\ReturnTypes\Helpers\AuthExtension
        - NunoMaduro\Larastan\ReturnTypes\Helpers\CacheExtension
        - NunoMaduro\Larastan\ReturnTypes\Helpers\ConfigExtension
        - NunoMaduro\Larastan\ReturnTypes\Helpers\RequestExtension
        - NunoMaduro\Larastan\ReturnTypes\Helpers\ResponseExtension
        - NunoMaduro\Larastan\ReturnTypes\Helpers\ValidatorExtension
        - NunoMaduro\Larastan\ReturnTypes\ModelExtension
        - NunoMaduro\Larastan\ReturnTypes\EloquentBuilderExtension
        - NunoMaduro\Larastan\ReturnTypes\RelationExtension
        - NunoMaduro\Larastan\ReturnTypes\StorageExtension
        - NunoMaduro\Larastan\ReturnTypes\Helpers\CollectExtension
    
    # Method reflection extensions
    methodReflectionExtensions:
        - NunoMaduro\Larastan\Reflection\EloquentBuilderMethodReflectionExtension
        - NunoMaduro\Larastan\Reflection\ModelMethodReflectionExtension
    
    # Static reflection extensions
    staticReflectionExtensions:
        - NunoMaduro\Larastan\Reflection\ModelStaticReflectionExtension
    
    # Property reflection extensions
    propertyReflectionExtensions:
        - NunoMaduro\Larastan\Reflection\ModelPropertyExtension
    
    # Type specifying extensions
    typeSpecifyingExtensions:
        - NunoMaduro\Larastan\TypeSpecifying\ModelRelationTypeSpecifyingExtension
