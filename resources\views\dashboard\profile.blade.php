@extends('layouts.dashboard')

@section('title', 'Profile - The Real World')
@section('page-title', 'Profile Settings')

@section('content')
<div class="p-6">
    <!-- Success Message -->
    @if(session('success'))
    <div class="bg-green-600 text-white rounded-lg p-4 mb-6">
        <p>{{ session('success') }}</p>
    </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Profile Form -->
        <div class="lg:col-span-2">
            <div class="card">
                <h3 class="text-lg font-semibold mb-6">Personal Information</h3>
                
                <form method="POST" action="{{ route('dashboard.profile.update') }}">
                    @csrf
                    @method('PUT')
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Name</label>
                            <input type="text" name="name" id="name" value="{{ old('name', auth()->user()->name) }}" 
                                   class="input-field w-full" required>
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email (readonly) -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                            <input type="email" id="email" value="{{ auth()->user()->email }}" 
                                   class="input-field w-full bg-gray-600 cursor-not-allowed" readonly>
                            <p class="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                        </div>

                        <!-- Location -->
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-300 mb-2">Location</label>
                            <input type="text" name="location" id="location" value="{{ old('location', auth()->user()->location) }}" 
                                   class="input-field w-full" placeholder="e.g., New York, USA">
                            @error('location')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Referral Code (readonly) -->
                        <div>
                            <label for="referral_code" class="block text-sm font-medium text-gray-300 mb-2">Your Referral Code</label>
                            <div class="flex">
                                <input type="text" id="referral_code" value="{{ auth()->user()->referral_code }}" 
                                       class="input-field flex-1 bg-gray-600 cursor-not-allowed" readonly>
                                <button type="button" onclick="copyReferralCode()" 
                                        class="ml-2 btn-secondary px-4 py-2">Copy</button>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Share this code to earn referral bonuses</p>
                        </div>
                    </div>

                    <!-- Bio -->
                    <div class="mt-6">
                        <label for="bio" class="block text-sm font-medium text-gray-300 mb-2">Bio</label>
                        <textarea name="bio" id="bio" rows="4" 
                                  class="input-field w-full" 
                                  placeholder="Tell us about yourself and your goals...">{{ old('bio', auth()->user()->bio) }}</textarea>
                        @error('bio')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Social Links -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-300 mb-4">Social Links</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="social_twitter" class="block text-sm font-medium text-gray-400 mb-1">Twitter</label>
                                <input type="url" name="social_twitter" id="social_twitter" 
                                       value="{{ old('social_twitter', auth()->user()->social_twitter) }}" 
                                       class="input-field w-full" placeholder="https://twitter.com/username">
                            </div>
                            <div>
                                <label for="social_instagram" class="block text-sm font-medium text-gray-400 mb-1">Instagram</label>
                                <input type="url" name="social_instagram" id="social_instagram" 
                                       value="{{ old('social_instagram', auth()->user()->social_instagram) }}" 
                                       class="input-field w-full" placeholder="https://instagram.com/username">
                            </div>
                            <div>
                                <label for="social_linkedin" class="block text-sm font-medium text-gray-400 mb-1">LinkedIn</label>
                                <input type="url" name="social_linkedin" id="social_linkedin" 
                                       value="{{ old('social_linkedin', auth()->user()->social_linkedin) }}" 
                                       class="input-field w-full" placeholder="https://linkedin.com/in/username">
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="mt-8">
                        <button type="submit" class="btn-primary">
                            Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Profile Stats -->
        <div class="space-y-6">
            <!-- Avatar & Stats -->
            <div class="card text-center">
                <img src="{{ auth()->user()->getAvatarUrl() }}" alt="{{ auth()->user()->name }}" 
                     class="w-24 h-24 rounded-full mx-auto mb-4">
                <h3 class="text-lg font-semibold mb-2">{{ auth()->user()->name }}</h3>
                <p class="text-gray-400 mb-4">{{ auth()->user()->getRoleNames()->first() ?? 'Member' }}</p>
                
                <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold text-yellow-400">{{ auth()->user()->xp }}</p>
                        <p class="text-xs text-gray-400">Total XP</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-blue-400">{{ auth()->user()->level }}</p>
                        <p class="text-xs text-gray-400">Level</p>
                    </div>
                </div>
            </div>

            <!-- Account Info -->
            <div class="card">
                <h4 class="font-semibold mb-4">Account Information</h4>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Member Since:</span>
                        <span>{{ auth()->user()->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Subscription:</span>
                        <span class="text-green-400">{{ ucfirst(auth()->user()->subscription_plan ?? 'Trial') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Status:</span>
                        <span class="text-green-400">{{ auth()->user()->is_active ? 'Active' : 'Inactive' }}</span>
                    </div>
                    @if(auth()->user()->trial_ends_at)
                    <div class="flex justify-between">
                        <span class="text-gray-400">Trial Ends:</span>
                        <span class="text-yellow-400">{{ auth()->user()->trial_ends_at->format('M j, Y') }}</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h4 class="font-semibold mb-4">Quick Actions</h4>
                <div class="space-y-2">
                    @if(!auth()->user()->subscribed())
                    <a href="{{ route('subscription.plans') }}" class="btn-primary w-full text-center">
                        Upgrade Plan
                    </a>
                    @endif
                    <a href="{{ route('dashboard.settings') }}" class="btn-secondary w-full text-center">
                        Account Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyReferralCode() {
    const referralCode = document.getElementById('referral_code');
    referralCode.select();
    referralCode.setSelectionRange(0, 99999);
    navigator.clipboard.writeText(referralCode.value);
    
    // Show feedback
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    button.classList.add('bg-green-600');
    
    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
    }, 2000);
}
</script>
@endsection
