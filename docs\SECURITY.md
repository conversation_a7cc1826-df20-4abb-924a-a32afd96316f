# The Real World - Security Guide

This document outlines the security measures, best practices, and configurations implemented in The Real World application.

## 🔐 Security Overview

The application implements multiple layers of security to protect against common web vulnerabilities and ensure data protection.

### Security Features Implemented

- **Authentication & Authorization** - Multi-factor authentication, role-based access control
- **Input Validation** - Comprehensive form validation and sanitization
- **CSRF Protection** - Built-in Laravel CSRF protection
- **XSS Prevention** - Output escaping and Content Security Policy
- **SQL Injection Protection** - Eloquent ORM and prepared statements
- **Rate Limiting** - API and form submission rate limiting
- **Security Headers** - Comprehensive HTTP security headers
- **Encryption** - Data encryption for sensitive information
- **Security Monitoring** - Real-time threat detection and logging

## 🛡️ Authentication Security

### Password Security

```php
// Password requirements (configured in config/security.php)
'password' => [
    'min_length' => 8,
    'require_uppercase' => true,
    'require_lowercase' => true,
    'require_numbers' => true,
    'require_symbols' => true,
    'check_breaches' => true,  // HaveIBeenPwned integration
    'max_age_days' => 90,
],
```

### Session Security

```php
// Session configuration (config/session.php)
'lifetime' => 120,  // 2 hours
'expire_on_close' => false,
'encrypt' => true,
'http_only' => true,
'same_site' => 'lax',
'secure' => env('SESSION_SECURE_COOKIE', true),  // HTTPS only in production
```

### Rate Limiting

```php
// Login attempts (config/security.php)
'rate_limiting' => [
    'login' => [
        'max_attempts' => 5,
        'decay_minutes' => 15,
    ],
    'registration' => [
        'max_attempts' => 3,
        'decay_minutes' => 60,
    ],
],
```

### Two-Factor Authentication

```php
// Enable 2FA for users
$user->enableTwoFactorAuth();

// Verify 2FA code
if ($user->verifyTwoFactorCode($code)) {
    // Grant access
}
```

## 🔒 Authorization & Access Control

### Role-Based Access Control (RBAC)

```php
// Define roles and permissions
$adminRole = Role::create(['name' => 'admin']);
$instructorRole = Role::create(['name' => 'instructor']);

$adminRole->givePermissionTo([
    'manage-users',
    'manage-courses',
    'manage-events',
    'view-analytics',
]);

// Assign roles to users
$user->assignRole('admin');

// Check permissions
if ($user->can('manage-courses')) {
    // Allow access
}
```

### Middleware Protection

```php
// Protect routes with middleware
Route::middleware(['auth', 'admin'])->group(function () {
    Route::get('/admin', [AdminController::class, 'index']);
});

// Custom middleware for subscription checks
Route::middleware(['auth', 'subscription:premium'])->group(function () {
    Route::get('/premium-content', [PremiumController::class, 'index']);
});
```

## 🛡️ Input Validation & Sanitization

### Form Request Validation

```php
class StoreCourseRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255|unique:courses,title',
            'description' => 'required|string|min:50|max:2000',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
        ];
    }
    
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation logic
            if ($this->containsSpam($this->description)) {
                $validator->errors()->add('description', 'Content appears to be spam.');
            }
        });
    }
}
```

### File Upload Security

```php
// Secure file upload handling
class MediaController extends Controller
{
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:10240',
        ]);
        
        $file = $request->file('file');
        
        // Validate file content
        if (!$this->isValidFileContent($file)) {
            throw new ValidationException('Invalid file content');
        }
        
        // Generate secure filename
        $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();
        
        // Store in secure location
        $path = $file->storeAs('uploads', $filename, 'private');
        
        return response()->json(['path' => $path]);
    }
}
```

## 🔐 Data Protection

### Encryption

```php
// Encrypt sensitive data
class User extends Model
{
    protected $casts = [
        'social_security_number' => 'encrypted',
        'payment_info' => 'encrypted:array',
    ];
}

// Manual encryption
$encrypted = encrypt('sensitive data');
$decrypted = decrypt($encrypted);
```

### Database Security

```sql
-- Use prepared statements (Eloquent handles this automatically)
-- Avoid raw queries, but if necessary:
DB::select('SELECT * FROM users WHERE id = ?', [$userId]);

-- Never do this:
-- DB::select("SELECT * FROM users WHERE id = {$userId}");
```

## 🛡️ Security Headers

### HTTP Security Headers

```php
// SecurityHeaders middleware
class SecurityHeaders
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
        
        // Content Security Policy
        $csp = "default-src 'self'; script-src 'self' 'unsafe-inline' https://js.stripe.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;";
        $response->headers->set('Content-Security-Policy', $csp);
        
        return $response;
    }
}
```

## 🔍 Security Monitoring

### Threat Detection

```php
// SecurityMonitoring middleware
class SecurityMonitoring
{
    public function handle(Request $request, Closure $next): Response
    {
        // Monitor suspicious activity
        $this->monitorSuspiciousActivity($request);
        
        // Check for blocked IPs
        if ($this->isIpBlocked($request->ip())) {
            abort(403, 'Access denied');
        }
        
        $response = $next($request);
        
        // Log security events
        $this->logSecurityEvents($request, $response);
        
        return $response;
    }
    
    protected function monitorSuspiciousActivity(Request $request): void
    {
        $ip = $request->ip();
        
        // Check for rapid requests
        $requestCount = Cache::get("requests:{$ip}", 0);
        Cache::put("requests:{$ip}", $requestCount + 1, now()->addMinutes(1));
        
        if ($requestCount > 100) {
            $this->flagSuspiciousActivity($ip, 'rapid_requests');
        }
        
        // Check for SQL injection patterns
        $url = $request->fullUrl();
        $sqlPatterns = ['union select', 'drop table', 'insert into'];
        
        foreach ($sqlPatterns as $pattern) {
            if (stripos($url, $pattern) !== false) {
                $this->flagSuspiciousActivity($ip, 'sql_injection_attempt');
                break;
            }
        }
    }
}
```

### Logging Security Events

```php
// Log security events
Log::channel('security')->warning('Suspicious activity detected', [
    'ip' => $request->ip(),
    'user_agent' => $request->userAgent(),
    'url' => $request->fullUrl(),
    'type' => 'brute_force_attempt',
]);
```

## 🔐 API Security

### API Authentication

```php
// Sanctum token authentication
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
});
```

### API Rate Limiting

```php
// API rate limiting
Route::middleware(['auth:sanctum', 'throttle:api'])->group(function () {
    // API routes
});

// Custom rate limiting
class ApiRateLimit
{
    public function handle(Request $request, Closure $next, int $maxAttempts = 60): Response
    {
        $key = 'api_rate_limit:' . $request->user()->id;
        
        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            return response()->json(['error' => 'Too many requests'], 429);
        }
        
        RateLimiter::hit($key, 60); // 1 minute window
        
        return $next($request);
    }
}
```

## 🛡️ Production Security Checklist

### Environment Configuration

- [ ] `APP_DEBUG=false` in production
- [ ] Strong `APP_KEY` generated
- [ ] Secure database credentials
- [ ] HTTPS enforced
- [ ] Secure session configuration
- [ ] Rate limiting enabled
- [ ] Security headers configured

### Server Security

- [ ] Server hardening completed
- [ ] Firewall configured
- [ ] SSL certificate installed
- [ ] Regular security updates
- [ ] File permissions set correctly
- [ ] Sensitive files protected

### Application Security

- [ ] All inputs validated
- [ ] CSRF protection enabled
- [ ] XSS prevention implemented
- [ ] SQL injection protection verified
- [ ] File upload security configured
- [ ] Error handling secure
- [ ] Logging configured

## 🚨 Incident Response

### Security Incident Procedure

1. **Immediate Response**
   - Identify and contain the threat
   - Block malicious IPs if necessary
   - Preserve evidence

2. **Assessment**
   - Determine scope of impact
   - Identify affected systems/data
   - Document findings

3. **Mitigation**
   - Apply security patches
   - Update security configurations
   - Reset compromised credentials

4. **Recovery**
   - Restore services
   - Verify system integrity
   - Monitor for continued threats

5. **Post-Incident**
   - Conduct security review
   - Update security procedures
   - Implement additional protections

### Emergency Contacts

- **Security Team**: <EMAIL>
- **System Admin**: <EMAIL>
- **On-call**: +1-XXX-XXX-XXXX

## 🔍 Security Testing

### Automated Security Testing

```bash
# Run security audit
composer audit

# Static analysis
./vendor/bin/phpstan analyse

# Dependency vulnerabilities
npm audit

# Custom security tests
php artisan test --testsuite=Security
```

### Manual Security Testing

- [ ] Authentication bypass attempts
- [ ] Authorization escalation tests
- [ ] Input validation testing
- [ ] File upload security tests
- [ ] Session management tests
- [ ] CSRF protection tests

## 📚 Security Resources

- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [Laravel Security Best Practices](https://laravel.com/docs/security)
- [PHP Security Guide](https://phpsecurity.readthedocs.io/)
- [Web Security Testing Guide](https://owasp.org/www-project-web-security-testing-guide/)

## 🆘 Reporting Security Issues

If you discover a security vulnerability, please:

1. **DO NOT** create a public issue
2. Email <EMAIL>
3. Include detailed description
4. Provide steps to reproduce
5. Allow time for investigation

We take security seriously and will respond promptly to all reports.

---

Stay secure! 🔒
