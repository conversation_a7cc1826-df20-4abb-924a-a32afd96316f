@extends('layouts.dashboard')

@section('title', 'Advanced Search - The Real World')
@section('page-title', 'Advanced Search')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">Advanced Search</h2>
                <p class="text-gray-400">Find exactly what you're looking for with detailed filters</p>
            </div>
            <a href="{{ route('dashboard.search.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Simple Search
            </a>
        </div>
    </div>

    <!-- Search Form -->
    <form method="GET" action="{{ route('dashboard.search.index') }}" class="space-y-8">
        <!-- Search Type -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">What are you looking for?</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label class="flex items-center p-4 border border-gray-600 rounded-lg cursor-pointer hover:border-primary-500 transition-colors">
                    <input type="radio" name="type" value="courses" class="mr-3" {{ request('type') === 'courses' ? 'checked' : '' }}>
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">📚</div>
                        <div>
                            <div class="font-medium">Courses</div>
                            <div class="text-sm text-gray-400">Learning content and lessons</div>
                        </div>
                    </div>
                </label>
                
                <label class="flex items-center p-4 border border-gray-600 rounded-lg cursor-pointer hover:border-primary-500 transition-colors">
                    <input type="radio" name="type" value="events" class="mr-3" {{ request('type') === 'events' ? 'checked' : '' }}>
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">🎪</div>
                        <div>
                            <div class="font-medium">Events</div>
                            <div class="text-sm text-gray-400">Live sessions and workshops</div>
                        </div>
                    </div>
                </label>
                
                <label class="flex items-center p-4 border border-gray-600 rounded-lg cursor-pointer hover:border-primary-500 transition-colors">
                    <input type="radio" name="type" value="users" class="mr-3" {{ request('type') === 'users' ? 'checked' : '' }}>
                    <div class="flex items-center">
                        <div class="text-2xl mr-3">👥</div>
                        <div>
                            <div class="font-medium">Users</div>
                            <div class="text-sm text-gray-400">Community members</div>
                        </div>
                    </div>
                </label>
            </div>
        </div>

        <!-- General Search -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Search Terms</h3>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Keywords</label>
                    <input type="text" name="q" value="{{ request('q') }}" 
                           placeholder="Enter keywords to search for..."
                           class="input-field">
                    <p class="text-xs text-gray-400 mt-1">Search in titles, descriptions, and content</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Must include</label>
                        <input type="text" name="include" value="{{ request('include') }}" 
                               placeholder="Required words..."
                               class="input-field">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium mb-2">Must exclude</label>
                        <input type="text" name="exclude" value="{{ request('exclude') }}" 
                               placeholder="Words to exclude..."
                               class="input-field">
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Filters -->
        <div class="card" x-show="document.querySelector('input[name=type][value=courses]').checked" x-cloak>
            <h3 class="text-lg font-semibold mb-4">Course Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Campus</label>
                    <select name="campus_id" class="input-field">
                        <option value="">All Campuses</option>
                        @foreach($campuses ?? [] as $campus)
                            <option value="{{ $campus->id }}" {{ request('campus_id') == $campus->id ? 'selected' : '' }}>
                                {{ $campus->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Difficulty Level</label>
                    <select name="difficulty" class="input-field">
                        <option value="">All Levels</option>
                        <option value="beginner" {{ request('difficulty') === 'beginner' ? 'selected' : '' }}>Beginner</option>
                        <option value="intermediate" {{ request('difficulty') === 'intermediate' ? 'selected' : '' }}>Intermediate</option>
                        <option value="advanced" {{ request('difficulty') === 'advanced' ? 'selected' : '' }}>Advanced</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Category</label>
                    <select name="category" class="input-field">
                        <option value="">All Categories</option>
                        @foreach($courseCategories ?? [] as $category)
                            <option value="{{ $category }}" {{ request('category') === $category ? 'selected' : '' }}>
                                {{ ucfirst($category) }}
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Duration (minutes)</label>
                    <div class="flex space-x-2">
                        <input type="number" name="min_duration" value="{{ request('min_duration') }}" 
                               placeholder="Min" class="input-field">
                        <input type="number" name="max_duration" value="{{ request('max_duration') }}" 
                               placeholder="Max" class="input-field">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Minimum Rating</label>
                    <select name="min_rating" class="input-field">
                        <option value="">Any Rating</option>
                        <option value="4" {{ request('min_rating') == '4' ? 'selected' : '' }}>4+ Stars</option>
                        <option value="3" {{ request('min_rating') == '3' ? 'selected' : '' }}>3+ Stars</option>
                        <option value="2" {{ request('min_rating') == '2' ? 'selected' : '' }}>2+ Stars</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Enrollment Status</label>
                    <select name="enrollment_status" class="input-field">
                        <option value="">All Courses</option>
                        <option value="enrolled" {{ request('enrollment_status') === 'enrolled' ? 'selected' : '' }}>Enrolled</option>
                        <option value="not_enrolled" {{ request('enrollment_status') === 'not_enrolled' ? 'selected' : '' }}>Not Enrolled</option>
                        <option value="completed" {{ request('enrollment_status') === 'completed' ? 'selected' : '' }}>Completed</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Event Filters -->
        <div class="card" x-show="document.querySelector('input[name=type][value=events]').checked" x-cloak>
            <h3 class="text-lg font-semibold mb-4">Event Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Event Category</label>
                    <select name="event_category" class="input-field">
                        <option value="">All Categories</option>
                        <option value="workshop" {{ request('event_category') === 'workshop' ? 'selected' : '' }}>Workshop</option>
                        <option value="webinar" {{ request('event_category') === 'webinar' ? 'selected' : '' }}>Webinar</option>
                        <option value="networking" {{ request('event_category') === 'networking' ? 'selected' : '' }}>Networking</option>
                        <option value="masterclass" {{ request('event_category') === 'masterclass' ? 'selected' : '' }}>Masterclass</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Date Range</label>
                    <select name="date_range" class="input-field">
                        <option value="">All Dates</option>
                        <option value="today" {{ request('date_range') === 'today' ? 'selected' : '' }}>Today</option>
                        <option value="tomorrow" {{ request('date_range') === 'tomorrow' ? 'selected' : '' }}>Tomorrow</option>
                        <option value="this_week" {{ request('date_range') === 'this_week' ? 'selected' : '' }}>This Week</option>
                        <option value="next_week" {{ request('date_range') === 'next_week' ? 'selected' : '' }}>Next Week</option>
                        <option value="this_month" {{ request('date_range') === 'this_month' ? 'selected' : '' }}>This Month</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">RSVP Status</label>
                    <select name="rsvp_status" class="input-field">
                        <option value="">All Events</option>
                        <option value="rsvp" {{ request('rsvp_status') === 'rsvp' ? 'selected' : '' }}>RSVP'd</option>
                        <option value="not_rsvp" {{ request('rsvp_status') === 'not_rsvp' ? 'selected' : '' }}>Not RSVP'd</option>
                        <option value="attended" {{ request('rsvp_status') === 'attended' ? 'selected' : '' }}>Attended</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Custom Date From</label>
                    <input type="date" name="date_from" value="{{ request('date_from') }}" class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Custom Date To</label>
                    <input type="date" name="date_to" value="{{ request('date_to') }}" class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Availability</label>
                    <select name="availability" class="input-field">
                        <option value="">All Events</option>
                        <option value="available" {{ request('availability') === 'available' ? 'selected' : '' }}>Available Spots</option>
                        <option value="full" {{ request('availability') === 'full' ? 'selected' : '' }}>Full</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- User Filters -->
        <div class="card" x-show="document.querySelector('input[name=type][value=users]').checked" x-cloak>
            <h3 class="text-lg font-semibold mb-4">User Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Subscription Plan</label>
                    <select name="subscription_plan" class="input-field">
                        <option value="">All Plans</option>
                        <option value="free" {{ request('subscription_plan') === 'free' ? 'selected' : '' }}>Free</option>
                        <option value="prosper" {{ request('subscription_plan') === 'prosper' ? 'selected' : '' }}>Prosper</option>
                        <option value="conquer" {{ request('subscription_plan') === 'conquer' ? 'selected' : '' }}>Conquer</option>
                        <option value="champions" {{ request('subscription_plan') === 'champions' ? 'selected' : '' }}>Champions</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Minimum Level</label>
                    <input type="number" name="min_level" value="{{ request('min_level') }}" 
                           placeholder="1" min="1" class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Minimum XP</label>
                    <input type="number" name="min_xp" value="{{ request('min_xp') }}" 
                           placeholder="0" min="0" class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Location</label>
                    <input type="text" name="location" value="{{ request('location') }}" 
                           placeholder="City, Country" class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Joined Date</label>
                    <select name="joined_date" class="input-field">
                        <option value="">Any Time</option>
                        <option value="today" {{ request('joined_date') === 'today' ? 'selected' : '' }}>Today</option>
                        <option value="this_week" {{ request('joined_date') === 'this_week' ? 'selected' : '' }}>This Week</option>
                        <option value="this_month" {{ request('joined_date') === 'this_month' ? 'selected' : '' }}>This Month</option>
                        <option value="this_year" {{ request('joined_date') === 'this_year' ? 'selected' : '' }}>This Year</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Activity Level</label>
                    <select name="activity_level" class="input-field">
                        <option value="">All Users</option>
                        <option value="active" {{ request('activity_level') === 'active' ? 'selected' : '' }}>Active (Last 7 days)</option>
                        <option value="recent" {{ request('activity_level') === 'recent' ? 'selected' : '' }}>Recent (Last 30 days)</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Sort Options -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Sort Results</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Sort By</label>
                    <select name="sort_by" class="input-field">
                        <option value="relevance" {{ request('sort_by') === 'relevance' ? 'selected' : '' }}>Relevance</option>
                        <option value="newest" {{ request('sort_by') === 'newest' ? 'selected' : '' }}>Newest First</option>
                        <option value="oldest" {{ request('sort_by') === 'oldest' ? 'selected' : '' }}>Oldest First</option>
                        <option value="rating" {{ request('sort_by') === 'rating' ? 'selected' : '' }}>Highest Rated</option>
                        <option value="popular" {{ request('sort_by') === 'popular' ? 'selected' : '' }}>Most Popular</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium mb-2">Results Per Page</label>
                    <select name="per_page" class="input-field">
                        <option value="12" {{ request('per_page') == '12' ? 'selected' : '' }}>12</option>
                        <option value="24" {{ request('per_page') == '24' ? 'selected' : '' }}>24</option>
                        <option value="48" {{ request('per_page') == '48' ? 'selected' : '' }}>48</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-between">
            <a href="{{ route('dashboard.search.advanced') }}" class="btn-secondary">
                Clear All Filters
            </a>
            
            <div class="space-x-4">
                <button type="button" onclick="window.history.back()" class="btn-secondary">
                    Cancel
                </button>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
            </div>
        </div>
    </form>
</div>

<style>
[x-cloak] {
    display: none !important;
}
</style>
@endsection
