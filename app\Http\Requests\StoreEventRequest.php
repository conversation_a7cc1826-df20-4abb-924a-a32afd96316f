<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

class StoreEventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole('admin') || $this->user()->hasR<PERSON>('instructor');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string|min:50',
            'category' => 'required|in:workshop,webinar,networking,masterclass',
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time',
            'max_attendees' => 'nullable|integer|min:1|max:10000',
            'meeting_url' => 'nullable|url|max:500',
            'recording_url' => 'nullable|url|max:500',
            'is_premium' => 'boolean',
            'is_published' => 'boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Event title is required.',
            'title.max' => 'Event title cannot exceed 255 characters.',
            'description.required' => 'Event description is required.',
            'description.min' => 'Event description must be at least 50 characters.',
            'category.required' => 'Please select an event category.',
            'category.in' => 'Event category must be workshop, webinar, networking, or masterclass.',
            'start_time.required' => 'Event start time is required.',
            'start_time.date' => 'Please provide a valid start date and time.',
            'start_time.after' => 'Event start time must be in the future.',
            'end_time.required' => 'Event end time is required.',
            'end_time.date' => 'Please provide a valid end date and time.',
            'end_time.after' => 'Event end time must be after the start time.',
            'max_attendees.integer' => 'Maximum attendees must be a number.',
            'max_attendees.min' => 'Maximum attendees must be at least 1.',
            'max_attendees.max' => 'Maximum attendees cannot exceed 10,000.',
            'meeting_url.url' => 'Meeting URL must be a valid URL.',
            'meeting_url.max' => 'Meeting URL cannot exceed 500 characters.',
            'recording_url.url' => 'Recording URL must be a valid URL.',
            'recording_url.max' => 'Recording URL cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'start_time' => 'start date and time',
            'end_time' => 'end date and time',
            'max_attendees' => 'maximum attendees',
            'meeting_url' => 'meeting URL',
            'recording_url' => 'recording URL',
            'is_premium' => 'premium status',
            'is_published' => 'published status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_premium' => $this->boolean('is_premium'),
            'is_published' => $this->boolean('is_published'),
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation for event duration
            if ($this->start_time && $this->end_time) {
                $start = Carbon::parse($this->start_time);
                $end = Carbon::parse($this->end_time);
                
                // Check if event is too short (less than 15 minutes)
                if ($end->diffInMinutes($start) < 15) {
                    $validator->errors()->add('end_time', 'Event must be at least 15 minutes long.');
                }
                
                // Check if event is too long (more than 8 hours)
                if ($end->diffInHours($start) > 8) {
                    $validator->errors()->add('end_time', 'Event cannot be longer than 8 hours.');
                }
            }
        });
    }
}
