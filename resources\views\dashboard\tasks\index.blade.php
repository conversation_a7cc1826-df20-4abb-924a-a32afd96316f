@extends('layouts.dashboard')

@section('title', 'Daily Tasks - The Real World')
@section('page-title', 'Daily Tasks')

@section('content')
<div class="p-6">
    <!-- Header Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <!-- Daily Progress -->
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Daily Progress</p>
                    <p class="text-2xl font-bold text-primary-400">{{ round($dailyProgress) }}%</p>
                </div>
            </div>
        </div>

        <!-- Tasks Completed -->
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Completed</p>
                    <p class="text-2xl font-bold text-green-400">{{ $completedTasks }}/{{ $totalTasks }}</p>
                </div>
            </div>
        </div>

        <!-- XP Earned Today -->
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">XP Today</p>
                    <p class="text-2xl font-bold text-yellow-400">{{ $dailyXpEarned }}</p>
                </div>
            </div>
        </div>

        <!-- Current Streak -->
        <div class="card">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.879 16.121A3 3 0 1012.015 11L11 14l4 4c-1.5 1.5-4 1.5-5.121-.879z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Streak</p>
                    <p class="text-2xl font-bold text-orange-400">{{ $streak }} days</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="card mb-8">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">Today's Progress</h3>
            <span class="text-sm text-gray-400">{{ $completedTasks }}/{{ $totalTasks }} tasks completed</span>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-4 mb-4">
            <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-4 rounded-full transition-all duration-500" 
                 style="width: {{ $dailyProgress }}%"></div>
        </div>
        
        @if($allTasksCompleted)
        <div class="bg-green-600 text-white rounded-lg p-4 text-center">
            <div class="flex items-center justify-center mb-2">
                <svg class="w-8 h-8 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
                <span class="text-xl font-bold">🎉 All Tasks Completed!</span>
            </div>
            <p>Congratulations! You've completed all daily tasks and earned bonus XP!</p>
        </div>
        @endif
    </div>

    <!-- Daily Tasks -->
    <div class="card">
        <h3 class="text-xl font-semibold mb-6">Daily Tasks</h3>
        
        <div class="space-y-4">
            @foreach($allTasks as $task)
            <div class="flex items-center p-4 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors {{ $task->is_completed_today ? 'bg-green-900/20 border-green-700' : 'bg-gray-800/50' }}">
                <!-- Task Icon -->
                <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-4 flex-shrink-0 text-2xl" 
                     style="background-color: {{ $task->getDifficultyColor() }}20; border: 2px solid {{ $task->getDifficultyColor() }};">
                    {!! $task->getIconHtml() !!}
                </div>

                <!-- Task Info -->
                <div class="flex-1">
                    <div class="flex items-center justify-between">
                        <h4 class="font-semibold text-lg {{ $task->is_completed_today ? 'text-green-400' : 'text-white' }}">
                            {{ $task->title }}
                        </h4>
                        <div class="flex items-center space-x-4 text-sm">
                            <!-- Difficulty Badge -->
                            <span class="px-2 py-1 rounded-full text-xs font-medium" 
                                  style="background-color: {{ $task->getDifficultyColor() }}20; color: {{ $task->getDifficultyColor() }};">
                                {{ ucfirst($task->difficulty) }}
                            </span>
                            <!-- XP Reward -->
                            <span class="text-yellow-400 font-medium">+{{ $task->xp_reward }} XP</span>
                        </div>
                    </div>
                    
                    <p class="text-gray-400 mt-1">{{ $task->description }}</p>
                </div>

                <!-- Action Button -->
                <div class="ml-4">
                    @if($task->is_completed_today)
                        <div class="flex items-center text-green-400">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Completed
                        </div>
                    @else
                        <button onclick="completeTask({{ $task->id }})" 
                                class="btn-primary px-4 py-2" 
                                id="task-btn-{{ $task->id }}">
                            Complete
                        </button>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Tips Section -->
    <div class="card mt-8">
        <h3 class="text-lg font-semibold mb-4">💡 Daily Task Tips</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-300">
            <div>
                <h4 class="font-medium text-white mb-2">Maximize Your XP</h4>
                <ul class="space-y-1">
                    <li>• Complete all tasks daily for bonus XP</li>
                    <li>• Maintain your streak for additional rewards</li>
                    <li>• Focus on higher difficulty tasks for more XP</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-white mb-2">Build Consistency</h4>
                <ul class="space-y-1">
                    <li>• Set a daily routine for task completion</li>
                    <li>• Start with easier tasks to build momentum</li>
                    <li>• Check back daily for new opportunities</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function completeTask(taskId) {
    const button = document.getElementById(`task-btn-${taskId}`);
    button.disabled = true;
    button.textContent = 'Completing...';

    fetch(`/dashboard/tasks/${taskId}/complete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification(data.message, 'success');
            
            // Reload page to update progress
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification(data.message, 'error');
            button.disabled = false;
            button.textContent = 'Complete';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        button.disabled = false;
        button.textContent = 'Complete';
    });
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
