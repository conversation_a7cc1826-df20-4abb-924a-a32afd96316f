<?php $__env->startSection('title', 'FAQ - The Real World'); ?>
<?php $__env->startSection('description', 'Get answers to frequently asked questions about The Real World, our courses, pricing, and membership benefits.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold font-display mb-6">
            Frequently Asked <span class="text-gradient">Questions</span>
        </h1>
        <p class="text-xl text-gray-300 mb-8">
            Get answers to the most common questions about The Real World, our courses, and membership benefits.
        </p>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="space-y-6" x-data="{ openFaq: null }">
            <?php $__currentLoopData = $faqs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card border border-gray-700">
                <button @click="openFaq = openFaq === <?php echo e($index + 1); ?> ? null : <?php echo e($index + 1); ?>" 
                        class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-700 transition-colors rounded-lg">
                    <span class="font-semibold text-lg"><?php echo e($faq['question']); ?></span>
                    <svg :class="openFaq === <?php echo e($index + 1); ?> ? 'rotate-180' : ''" 
                         class="w-5 h-5 transform transition-transform text-primary-400" 
                         fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === <?php echo e($index + 1); ?>" 
                     x-collapse 
                     class="px-6 pb-4">
                    <p class="text-gray-300 leading-relaxed"><?php echo e($faq['answer']); ?></p>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Additional FAQ Categories -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Course Content</h3>
                <p class="text-gray-400">Questions about our courses, lessons, and learning materials.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Billing & Payments</h3>
                <p class="text-gray-400">Information about pricing, billing cycles, and payment methods.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Technical Support</h3>
                <p class="text-gray-400">Help with account access, technical issues, and platform usage.</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-6">Still Have Questions?</h2>
        <p class="text-xl text-gray-400 mb-8">
            Can't find the answer you're looking for? Our support team is here to help you succeed.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div class="card text-center">
                <div class="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Live Chat Support</h3>
                <p class="text-gray-400 mb-4">Get instant help from our support team during business hours.</p>
                <button class="btn-primary">Start Chat</button>
            </div>

            <div class="card text-center">
                <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Email Support</h3>
                <p class="text-gray-400 mb-4">Send us an email and we'll get back to you within 24 hours.</p>
                <a href="mailto:<EMAIL>" class="btn-secondary">Send Email</a>
            </div>
        </div>

        <div class="text-center">
            <p class="text-gray-400 mb-6">
                Ready to start your journey? Join thousands of students already building wealth.
            </p>
            <a href="<?php echo e(route('register')); ?>" class="btn-primary text-lg px-8 py-4">
                Join The Real World
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://unpkg.com/@alpinejs/collapse@3.x.x/dist/cdn.min.js"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/home/<USER>/ ?>