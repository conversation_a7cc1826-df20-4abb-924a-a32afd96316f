<?php $__env->startSection('title', 'The Real World - Join the Ultimate Online University'); ?>
<?php $__env->startSection('description', 'Learn real-world skills to build wealth and achieve financial freedom. Join thousands of students already making money.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-20 lg:py-32">
    <div class="absolute inset-0 bg-black/20"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Hero Content -->
            <div class="text-center lg:text-left">
                <h1 class="text-4xl md:text-6xl font-bold font-display mb-6">
                    <span class="text-white">Escape The</span>
                    <span class="text-gradient block">Matrix</span>
                </h1>
                <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                    Join the ultimate online university where you learn real-world skills to build wealth and achieve financial freedom. No fluff, just results.
                </p>
                
                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                    <a href="<?php echo e(route('register')); ?>" class="btn-primary text-lg px-8 py-4">
                        Join The Real World
                    </a>
                    <a href="<?php echo e(route('campuses')); ?>" class="btn-secondary text-lg px-8 py-4">
                        Explore Campuses
                    </a>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-3 gap-4 text-center lg:text-left">
                    <div>
                        <div class="text-2xl font-bold text-red-400" x-data="{ count: 0 }" x-init="setInterval(() => { if(count < <?php echo e($stats['total_users']); ?>) count++ }, 50)">
                            <span x-text="count.toLocaleString()">0</span>+
                        </div>
                        <div class="text-sm text-gray-400">Students</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-green-400" x-data="{ count: 0 }" x-init="setInterval(() => { if(count < <?php echo e($stats['active_users']); ?>) count++ }, 50)">
                            <span x-text="count.toLocaleString()">0</span>+
                        </div>
                        <div class="text-sm text-gray-400">Active Users</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-yellow-400">
                            <?php echo e($stats['total_income']); ?>

                        </div>
                        <div class="text-sm text-gray-400">Student Earnings</div>
                    </div>
                </div>
            </div>

            <!-- Hero Image/Video -->
            <div class="relative">
                <div class="aspect-video bg-gray-800 rounded-lg overflow-hidden shadow-2xl">
                    <div class="w-full h-full bg-gradient-to-br from-red-600 to-red-800 flex items-center justify-center">
                        <div class="text-center">
                            <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <p class="text-white font-semibold">Watch Preview</p>
                            <p class="text-white/80 text-sm">See what's inside</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Campuses -->
<section class="py-20 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold font-display mb-4">
                Choose Your <span class="text-gradient">Campus</span>
            </h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Each campus is designed to teach you a specific skill set that can generate real income. Pick your path to financial freedom.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $featuredCampuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="card hover:transform hover:scale-105 transition-all duration-300 cursor-pointer group">
                <div class="flex items-center mb-4">
                    <?php if($campus->icon_url): ?>
                        <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-12 h-12 rounded-lg mr-4">
                    <?php else: ?>
                        <div class="w-12 h-12 rounded-lg mr-4 flex items-center justify-center text-2xl" style="background-color: <?php echo e($campus->color); ?>">
                            💼
                        </div>
                    <?php endif; ?>
                    <h3 class="text-xl font-semibold group-hover:text-red-400 transition-colors"><?php echo e($campus->name); ?></h3>
                </div>
                <p class="text-gray-400 mb-4"><?php echo e($campus->teaser_description ?: $campus->description); ?></p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-red-400 font-medium">
                        <?php echo e($campus->is_premium ? 'Premium' : 'Free Access'); ?>

                    </span>
                    <svg class="w-5 h-5 text-gray-400 group-hover:text-red-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-12">
                <p class="text-gray-400">No campuses available yet. Check back soon!</p>
            </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('campuses')); ?>" class="btn-primary">
                View All Campuses
            </a>
        </div>
    </div>
</section>

<!-- Testimonials -->
<section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold font-display mb-4">
                Real Students, <span class="text-gradient">Real Results</span>
            </h2>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Don't just take our word for it. See what our students are achieving with The Real World education.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="card">
                <div class="flex items-center mb-4">
                    <?php if($testimonial->photo_url): ?>
                        <img src="<?php echo e($testimonial->photo_url); ?>" alt="<?php echo e($testimonial->name); ?>" class="w-12 h-12 rounded-full mr-4">
                    <?php else: ?>
                        <div class="w-12 h-12 rounded-full bg-red-600 flex items-center justify-center mr-4">
                            <span class="text-white font-semibold"><?php echo e(substr($testimonial->name, 0, 1)); ?></span>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h4 class="font-semibold"><?php echo e($testimonial->name); ?></h4>
                        <p class="text-sm text-gray-400"><?php echo e($testimonial->location); ?></p>
                    </div>
                </div>
                <blockquote class="text-gray-300 mb-4">
                    "<?php echo e($testimonial->quote); ?>"
                </blockquote>
                <div class="flex items-center justify-between">
                    <span class="text-green-400 font-semibold"><?php echo e($testimonial->income_display); ?></span>
                    <?php if($testimonial->business_type): ?>
                        <span class="text-xs text-gray-500 bg-gray-700 px-2 py-1 rounded"><?php echo e($testimonial->business_type); ?></span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-12">
                <p class="text-gray-400">No testimonials available yet.</p>
            </div>
            <?php endif; ?>
        </div>

        <div class="text-center mt-12">
            <a href="<?php echo e(route('testimonials')); ?>" class="btn-secondary">
                View All Success Stories
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-red-600 to-red-800">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold font-display mb-6 text-white">
            Ready to Escape the Matrix?
        </h2>
        <p class="text-xl text-red-100 mb-8">
            Join thousands of students who are already building wealth with real-world skills. Your financial freedom starts today.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('register')); ?>" class="bg-white text-red-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors text-lg">
                Start Your Journey
            </a>
            <a href="<?php echo e(route('pricing')); ?>" class="border-2 border-white text-white hover:bg-white hover:text-red-600 font-semibold py-4 px-8 rounded-lg transition-colors text-lg">
                View Pricing
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/home/<USER>/ ?>