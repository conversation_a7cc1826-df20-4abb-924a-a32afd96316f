<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'campus_id',
        'title',
        'description',
        'thumbnail_url',
        'start_datetime',
        'end_datetime',
        'video_url',
        'recording_url',
        'status',
        'max_attendees',
        'is_premium',
        'required_plans',
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'is_premium' => 'boolean',
        'required_plans' => 'array',
    ];

    /**
     * Relationships
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    public function rsvps(): HasMany
    {
        return $this->hasMany(EventRsvp::class);
    }

    /**
     * Scopes
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeLive($query)
    {
        return $query->where('status', 'live');
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_datetime', '>=', now())
                     ->whereIn('status', ['scheduled', 'live']);
    }

    public function scopePast($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * Helper Methods
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    public function canUserAccess(User $user): bool
    {
        // If no required plans, everyone can access
        if (!$this->required_plans || empty($this->required_plans)) {
            return true;
        }

        // Check if user has required subscription plan
        return in_array($user->subscription_plan, $this->required_plans);
    }

    public function canUserJoin(User $user): bool
    {
        // Must have access
        if (!$this->canUserAccess($user)) {
            return false;
        }

        // Must be RSVP'd
        if (!$this->rsvps()->where('user_id', $user->id)->exists()) {
            return false;
        }

        // Event must be starting soon or already started
        $now = now();
        $startTime = $this->start_datetime;
        $endTime = $this->end_datetime;

        // Can join 15 minutes before start time and until end time
        return $now >= $startTime->subMinutes(15) && $now <= $endTime;
    }

    public function isRsvpOpen(): bool
    {
        // RSVP closes 1 hour before event starts
        return now() < $this->start_datetime->subHour();
    }

    public function getCountdownData(): array
    {
        $now = now();
        $startTime = $this->start_datetime;
        $endTime = $this->end_datetime;

        if ($now < $startTime) {
            // Event hasn't started yet
            $diff = $now->diff($startTime);
            return [
                'status' => 'upcoming',
                'message' => 'Starts in',
                'days' => $diff->days,
                'hours' => $diff->h,
                'minutes' => $diff->i,
                'seconds' => $diff->s,
                'total_seconds' => $now->diffInSeconds($startTime),
            ];
        } elseif ($now >= $startTime && $now <= $endTime) {
            // Event is live
            $diff = $now->diff($endTime);
            return [
                'status' => 'live',
                'message' => 'Live now - Ends in',
                'days' => 0,
                'hours' => $diff->h,
                'minutes' => $diff->i,
                'seconds' => $diff->s,
                'total_seconds' => $now->diffInSeconds($endTime),
            ];
        } else {
            // Event has ended
            return [
                'status' => 'ended',
                'message' => 'Event ended',
                'days' => 0,
                'hours' => 0,
                'minutes' => 0,
                'seconds' => 0,
                'total_seconds' => 0,
            ];
        }
    }

    public function getFormattedDuration(): string
    {
        $duration = $this->start_datetime->diffInMinutes($this->end_datetime);

        if ($duration < 60) {
            return $duration . ' minutes';
        }

        $hours = floor($duration / 60);
        $minutes = $duration % 60;

        if ($minutes > 0) {
            return $hours . 'h ' . $minutes . 'm';
        }

        return $hours . ' hour' . ($hours > 1 ? 's' : '');
    }

    public function getStatusBadge(): array
    {
        $now = now();

        if ($now < $this->start_datetime) {
            return [
                'text' => 'Upcoming',
                'class' => 'bg-blue-600 text-white',
            ];
        } elseif ($now >= $this->start_datetime && $now <= $this->end_datetime) {
            return [
                'text' => 'Live Now',
                'class' => 'bg-red-600 text-white animate-pulse',
            ];
        } else {
            return [
                'text' => 'Ended',
                'class' => 'bg-gray-600 text-white',
            ];
        }
    }
}
