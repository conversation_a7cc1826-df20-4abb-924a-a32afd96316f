<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\WebhookController;
use App\Http\Controllers\Dashboard\CampusController;
use App\Http\Controllers\Dashboard\CourseController;
use App\Http\Controllers\Dashboard\LessonController;
use App\Http\Controllers\Dashboard\TaskController;
use App\Http\Controllers\Dashboard\LeaderboardController;
use App\Http\Controllers\Dashboard\BadgeController;
use App\Http\Controllers\Dashboard\EventController;
use App\Http\Controllers\Dashboard\ChatController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Dashboard\ProfileController;
use App\Http\Controllers\Dashboard\SettingsController;
use App\Http\Controllers\Dashboard\SubscriptionController as DashboardSubscriptionController;
use App\Http\Controllers\MediaController;
use App\Http\Controllers\Dashboard\NotificationController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\Admin\PerformanceController;
use App\Http\Controllers\Admin\AdminCampusController;
use App\Http\Controllers\Admin\AdminCourseController;
use App\Http\Controllers\Admin\AdminEventController;
use App\Http\Controllers\Admin\AdminBadgeController;
use App\Http\Controllers\Admin\AdminMediaController;
use App\Http\Controllers\Admin\AdminSettingsController;
use App\Http\Controllers\Admin\AdminAnalyticsController;
use App\Http\Controllers\Admin\AdminTaskController;
use App\Http\Controllers\Admin\AdminLessonController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public Website Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/campuses', [HomeController::class, 'campuses'])->name('campuses');
Route::get('/campuses/{campus:slug}', [HomeController::class, 'showCampus'])->name('campuses.show');
Route::get('/pricing', [HomeController::class, 'pricing'])->name('pricing');
Route::get('/testimonials', [HomeController::class, 'testimonials'])->name('testimonials');
Route::get('/faq', [HomeController::class, 'faq'])->name('faq');

// Legal Pages
Route::get('/terms', [HomeController::class, 'terms'])->name('terms');
Route::get('/privacy', [HomeController::class, 'privacy'])->name('privacy');
Route::get('/earnings-disclaimer', [HomeController::class, 'earningsDisclaimer'])->name('earnings-disclaimer');

// Email Management
Route::get('/unsubscribe/{token}', [HomeController::class, 'unsubscribe'])->name('unsubscribe');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/forgot-password', [AuthController::class, 'showForgotPassword'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLink'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPassword'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
});

// Email Verification Routes
Route::middleware('auth')->group(function () {
    Route::get('/email/verify', [AuthController::class, 'showVerifyEmail'])->name('verification.notice');
    Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])->name('verification.verify');
    Route::post('/email/verification-notification', [AuthController::class, 'sendVerification'])->name('verification.send');
});

// Authenticated Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Dashboard Routes
    Route::prefix('dashboard')->name('dashboard.')->group(function () {
        Route::get('/', [DashboardController::class, 'index'])->name('index');

        // Campus & Course Routes
        Route::get('/campuses', [CampusController::class, 'index'])->name('campuses');
        Route::get('/campuses/{campus}', [CampusController::class, 'show'])->name('campuses.show');
        Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
        Route::get('/courses/{course}', [CourseController::class, 'show'])->name('courses.show');
        Route::post('/courses/{course}/start', [CourseController::class, 'start'])->name('courses.start');
        Route::get('/lessons/{lesson}', [LessonController::class, 'show'])->name('lessons.show');
        Route::post('/lessons/{lesson}/progress', [LessonController::class, 'updateProgress'])->name('lessons.progress');
        Route::post('/lessons/{lesson}/complete', [LessonController::class, 'complete'])->name('lessons.complete');

        // Tasks & Gamification Routes
        Route::get('/tasks', [TaskController::class, 'index'])->name('tasks');
        Route::post('/tasks/{task}/complete', [TaskController::class, 'complete'])->name('tasks.complete');

        // Leaderboard Routes
        Route::prefix('leaderboard')->name('leaderboard.')->group(function () {
            Route::get('/', [LeaderboardController::class, 'index'])->name('index');
        });

        // Badges and Achievements
        Route::prefix('badges')->name('badges.')->group(function () {
            Route::get('/', [BadgeController::class, 'index'])->name('index');
            Route::get('/{badge}', [BadgeController::class, 'show'])->name('show');
        });

        // Events Routes
        Route::get('/events', [EventController::class, 'index'])->name('events.index');
        Route::get('/events/{event}', [EventController::class, 'show'])->name('events.show');
        Route::post('/events/{event}/rsvp', [EventController::class, 'rsvp'])->name('events.rsvp');
        Route::delete('/events/{event}/rsvp', [EventController::class, 'cancelRsvp'])->name('events.cancel-rsvp');
        Route::get('/events/{event}/join', [EventController::class, 'join'])->name('events.join');

        // Chat Routes
        Route::get('/chat', [ChatController::class, 'index'])->name('chat');
        Route::post('/chat/send', [ChatController::class, 'sendMessage'])->name('chat.send');
        Route::get('/chat/messages', [ChatController::class, 'loadMessages'])->name('chat.messages');

        // Profile Routes
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [ProfileController::class, 'show'])->name('show');
            Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
            Route::put('/update', [ProfileController::class, 'update'])->name('update');
            Route::put('/password', [ProfileController::class, 'updatePassword'])->name('update-password');
            Route::delete('/avatar', [ProfileController::class, 'deleteAvatar'])->name('delete-avatar');
        });

        // Settings Routes
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingsController::class, 'index'])->name('index');
            Route::put('/notifications', [SettingsController::class, 'updateNotifications'])->name('notifications');
            Route::put('/privacy', [SettingsController::class, 'updatePrivacy'])->name('privacy');
            Route::get('/subscription', [SettingsController::class, 'subscription'])->name('subscription');
            Route::put('/subscription', [SettingsController::class, 'updateSubscription'])->name('subscription.update');
            Route::get('/billing', [SettingsController::class, 'billing'])->name('billing');
            Route::delete('/account', [SettingsController::class, 'deleteAccount'])->name('delete-account');
        });

        // Media Routes
        Route::prefix('media')->name('media.')->group(function () {
            Route::get('/', function() {
                return view('dashboard.media.index');
            })->name('index');
            Route::get('/library', [MediaController::class, 'index'])->name('library');
            Route::post('/upload', [MediaController::class, 'upload'])->name('upload');
            Route::get('/{media}', [MediaController::class, 'show'])->name('show');
            Route::put('/{media}', [MediaController::class, 'update'])->name('update');
            Route::delete('/{media}', [MediaController::class, 'destroy'])->name('destroy');
        });

        // Notification Routes
        Route::prefix('notifications')->name('notifications.')->group(function () {
            Route::get('/', [NotificationController::class, 'index'])->name('index');
            Route::get('/recent', [NotificationController::class, 'recent'])->name('recent');
            Route::get('/unread-count', [NotificationController::class, 'unreadCount'])->name('unread-count');
            Route::post('/{id}/read', [NotificationController::class, 'markAsRead'])->name('mark-read');
            Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
            Route::delete('/{id}', [NotificationController::class, 'destroy'])->name('destroy');
            Route::delete('/clear', [NotificationController::class, 'clear'])->name('clear');
            Route::put('/preferences', [NotificationController::class, 'updatePreferences'])->name('preferences');
        });
    });

    // Search Routes (outside dashboard group for public access)
    Route::prefix('search')->name('search.')->group(function () {
        Route::get('/', [SearchController::class, 'search'])->name('index');
        Route::get('/quick', [SearchController::class, 'quickSearch'])->name('quick');
        Route::get('/courses', [SearchController::class, 'searchCourses'])->name('courses');
        Route::get('/events', [SearchController::class, 'searchEvents'])->name('events');
        Route::get('/advanced', function() {
            return view('search.advanced');
        })->name('advanced');
    });

    // Legacy search route
    Route::get('/search', [SearchController::class, 'search'])->name('search');

    // Dashboard routes
    Route::middleware(['auth', 'verified'])->prefix('dashboard')->name('dashboard.')->group(function () {
        // Dashboard search routes
        Route::prefix('search')->name('search.')->group(function () {
            Route::get('/', [SearchController::class, 'search'])->name('index');
            Route::get('/advanced', function() {
                return view('search.advanced');
            })->name('advanced');
        });
    });

    // Subscription Routes
    Route::prefix('subscription')->name('subscription.')->group(function () {
        Route::get('/plans', [DashboardSubscriptionController::class, 'plans'])->name('plans');
        Route::post('/checkout', [DashboardSubscriptionController::class, 'checkout'])->name('checkout');
        Route::post('/change-plan', [DashboardSubscriptionController::class, 'changePlan'])->name('change-plan');
        Route::post('/cancel', [DashboardSubscriptionController::class, 'cancelSubscription'])->name('cancel-subscription');
        Route::post('/resume', [DashboardSubscriptionController::class, 'resumeSubscription'])->name('resume-subscription');
        Route::get('/success', [DashboardSubscriptionController::class, 'success'])->name('success');
        Route::get('/cancel', [DashboardSubscriptionController::class, 'cancel'])->name('cancel');
        Route::get('/invoice/{invoice}', [DashboardSubscriptionController::class, 'downloadInvoice'])->name('invoice.download');
    });

    // Admin Routes
    Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
        // Admin Dashboard
        Route::get('/', [AdminController::class, 'index'])->name('dashboard');
        Route::get('/system', [AdminController::class, 'system'])->name('system');
        Route::get('/analytics', [AdminController::class, 'analytics'])->name('analytics.index');
        Route::get('/performance', [AdminController::class, 'performance'])->name('performance.index');
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings.index');

        // User Management
        Route::resource('users', AdminUserController::class);
        Route::patch('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])->name('users.toggle-status');

        // Content Management
        Route::resource('campuses', AdminCampusController::class);
        Route::resource('courses', AdminCourseController::class);
        Route::patch('courses/{course}/toggle-status', [AdminCourseController::class, 'toggleStatus'])->name('courses.toggle-status');
        Route::resource('lessons', AdminLessonController::class);
        Route::patch('lessons/{lesson}/toggle-status', [AdminLessonController::class, 'toggleStatus'])->name('lessons.toggle-status');
        Route::post('courses/{course}/lessons/reorder', [AdminLessonController::class, 'reorder'])->name('courses.lessons.reorder');
        Route::resource('events', AdminEventController::class);
        Route::resource('badges', AdminBadgeController::class);
        Route::resource('tasks', AdminTaskController::class);
        Route::patch('tasks/{task}/toggle-status', [AdminTaskController::class, 'toggleStatus'])->name('tasks.toggle-status');
        Route::resource('media', AdminMediaController::class);

        // Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [AdminSettingsController::class, 'index'])->name('index');
            Route::put('/general', [AdminSettingsController::class, 'updateGeneral'])->name('general');
            Route::put('/email', [AdminSettingsController::class, 'updateEmail'])->name('email');
            Route::put('/security', [AdminSettingsController::class, 'updateSecurity'])->name('security');
        });

        // Analytics
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [AdminAnalyticsController::class, 'index'])->name('index');
            Route::get('/users', [AdminAnalyticsController::class, 'users'])->name('users');
            Route::get('/revenue', [AdminAnalyticsController::class, 'revenue'])->name('revenue');
            Route::get('/engagement', [AdminAnalyticsController::class, 'engagement'])->name('engagement');
        });

        // Performance routes
        Route::prefix('performance')->name('performance.')->group(function () {
            Route::get('/', [PerformanceController::class, 'index'])->name('index');
            Route::get('/cache-stats', [PerformanceController::class, 'cacheStats'])->name('cache-stats');
            Route::post('/clear-cache', [PerformanceController::class, 'clearCache'])->name('clear-cache');
            Route::post('/warmup-cache', [PerformanceController::class, 'warmupCache'])->name('warmup-cache');
            Route::post('/optimize', [PerformanceController::class, 'optimize'])->name('optimize');
            Route::get('/database-analysis', [PerformanceController::class, 'databaseAnalysis'])->name('database-analysis');
            Route::post('/optimize-database', [PerformanceController::class, 'optimizeDatabase'])->name('optimize-database');
            Route::get('/system-info', [PerformanceController::class, 'systemInfo'])->name('system-info');
            Route::get('/recommendations', [PerformanceController::class, 'recommendations'])->name('recommendations');
        });
    });
});

// Stripe Webhook (outside of auth middleware)
Route::post('/stripe/webhook', [WebhookController::class, 'handleWebhook'])->name('cashier.webhook');
