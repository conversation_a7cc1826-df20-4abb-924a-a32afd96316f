@echo off
echo ========================================
echo    Laravel Cache Clearing Script
echo ========================================
echo.

echo [1/8] Clearing application cache...
php artisan cache:clear
echo.

echo [2/8] Clearing configuration cache...
php artisan config:clear
echo.

echo [3/8] Clearing route cache...
php artisan route:clear
echo.

echo [4/8] Clearing view cache...
php artisan view:clear
echo.

echo [5/8] Clearing event cache...
php artisan event:clear
echo.

echo [6/8] Running optimize:clear (all caches)...
php artisan optimize:clear
echo.

echo [7/8] Clearing bootstrap cache files...
if exist "bootstrap\cache\packages.php" del "bootstrap\cache\packages.php"
if exist "bootstrap\cache\services.php" del "bootstrap\cache\services.php"
if exist "bootstrap\cache\config.php" del "bootstrap\cache\config.php"
if exist "bootstrap\cache\routes-v7.php" del "bootstrap\cache\routes-v7.php"
echo Bootstrap cache files cleared.
echo.

echo [8/8] Clearing storage framework cache...
powershell -Command "if (Test-Path 'storage\framework\cache\data') { Remove-Item -Recurse -Force 'storage\framework\cache\data\*' }"
powershell -Command "if (Test-Path 'storage\framework\views') { Remove-Item -Force 'storage\framework\views\*.php' }"
echo Storage cache cleared.
echo.

echo ========================================
echo   All caches cleared successfully!
echo ========================================
echo.
echo You may need to restart your development server.
echo Run: php artisan serve
echo.
pause
