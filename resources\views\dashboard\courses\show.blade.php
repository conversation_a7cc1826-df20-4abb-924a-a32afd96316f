@extends('layouts.dashboard')

@section('title', $course->title . ' - The Real World')
@section('page-title', $course->title)

@section('content')
<div class="p-6">
    <!-- Course Header -->
    <div class="card mb-8">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-4">
                    <a href="{{ route('dashboard.campuses.show', $campus) }}" 
                       class="text-primary-400 hover:text-primary-300 text-sm font-medium mr-2">
                        {{ $campus->name }}
                    </a>
                    <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-gray-400 text-sm">{{ $course->title }}</span>
                </div>
                
                <h1 class="text-3xl font-bold mb-4">{{ $course->title }}</h1>
                <p class="text-gray-300 mb-6 leading-relaxed">{{ $course->description }}</p>
                
                <!-- Course Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-primary-400">{{ $totalLessons }}</p>
                        <p class="text-sm text-gray-400">Lessons</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-400">{{ $completedLessons }}</p>
                        <p class="text-sm text-gray-400">Completed</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-400">{{ round($courseProgress) }}%</p>
                        <p class="text-sm text-gray-400">Progress</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-yellow-400">{{ $course->duration_minutes }}m</p>
                        <p class="text-sm text-gray-400">Duration</p>
                    </div>
                </div>
            </div>
            
            <!-- Difficulty Badge -->
            <span class="bg-{{ $course->difficulty === 'beginner' ? 'green' : ($course->difficulty === 'intermediate' ? 'yellow' : 'red') }}-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                {{ ucfirst($course->difficulty) }}
            </span>
        </div>

        <!-- Progress Bar -->
        <div class="mt-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-300">Course Progress</span>
                <span class="text-sm text-gray-400">{{ $completedLessons }}/{{ $totalLessons }} lessons</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-3">
                <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-500" 
                     style="width: {{ $courseProgress }}%"></div>
            </div>
        </div>
    </div>

    <!-- Quick Action -->
    @if($nextLesson)
    <div class="card mb-8 bg-gradient-to-r from-primary-600 to-primary-700">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-white mb-2">
                    {{ $courseProgress > 0 ? 'Continue Learning' : 'Start Your Journey' }}
                </h3>
                <p class="text-primary-100">
                    Next: {{ $nextLesson->title }}
                </p>
            </div>
            <a href="{{ route('dashboard.lessons.show', $nextLesson) }}" 
               class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-colors">
                {{ $courseProgress > 0 ? 'Continue' : 'Start Now' }}
            </a>
        </div>
    </div>
    @endif

    <!-- Lessons List -->
    <div class="card">
        <h3 class="text-xl font-semibold mb-6">Course Lessons</h3>
        
        <div class="space-y-4">
            @foreach($lessons as $index => $lesson)
            <div class="flex items-center p-4 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors {{ $lesson->is_completed ? 'bg-green-900/20' : 'bg-gray-800/50' }}">
                <!-- Lesson Number -->
                <div class="w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 {{ $lesson->is_completed ? 'bg-green-600' : 'bg-gray-700' }}">
                    @if($lesson->is_completed)
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    @else
                        <span class="text-white font-semibold">{{ $index + 1 }}</span>
                    @endif
                </div>

                <!-- Lesson Info -->
                <div class="flex-1">
                    <div class="flex items-center justify-between">
                        <h4 class="font-semibold text-lg {{ $lesson->is_completed ? 'text-green-400' : 'text-white' }}">
                            {{ $lesson->title }}
                        </h4>
                        <div class="flex items-center space-x-4 text-sm text-gray-400">
                            @if($lesson->is_preview)
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs">Preview</span>
                            @endif
                            <span>{{ $lesson->getFormattedDuration() }}</span>
                            <span>{{ $lesson->xp_reward }} XP</span>
                        </div>
                    </div>
                    
                    @if($lesson->description)
                    <p class="text-gray-400 mt-1">{{ $lesson->description }}</p>
                    @endif

                    <!-- Progress Bar for Individual Lesson -->
                    @if($lesson->progress_percentage > 0 && !$lesson->is_completed)
                    <div class="mt-2">
                        <div class="w-full bg-gray-700 rounded-full h-1">
                            <div class="bg-primary-600 h-1 rounded-full" style="width: {{ $lesson->progress_percentage }}%"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">{{ round($lesson->progress_percentage) }}% watched</p>
                    </div>
                    @endif
                </div>

                <!-- Action Button -->
                <div class="ml-4">
                    @if($lesson->is_preview || $campus->isAccessibleBy($user))
                        <a href="{{ route('dashboard.lessons.show', $lesson) }}" 
                           class="btn-{{ $lesson->is_completed ? 'secondary' : 'primary' }} px-4 py-2">
                            {{ $lesson->is_completed ? 'Review' : ($lesson->progress_percentage > 0 ? 'Continue' : 'Start') }}
                        </a>
                    @else
                        <span class="text-gray-500 text-sm">
                            <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                            Premium
                        </span>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>

    <!-- Back Button -->
    <div class="mt-8">
        <a href="{{ route('dashboard.campuses.show', $campus) }}" 
           class="inline-flex items-center text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to {{ $campus->name }}
        </a>
    </div>
</div>
@endsection
