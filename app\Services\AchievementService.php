<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserAchievement;
use App\Models\Badge;
use App\Notifications\AchievementUnlocked;
use App\Notifications\BadgeEarned;

class AchievementService
{
    /**
     * Check and award achievements for a user action
     */
    public function checkAchievements(User $user, string $action, array $data = []): array
    {
        $awarded = [];

        switch ($action) {
            case 'first_login':
                if (!$user->hasAchievement('first_login')) {
                    $achievement = UserAchievement::award($user, 'first_login');
                    if ($achievement) {
                        $awarded[] = $achievement;
                        $user->notify(new AchievementUnlocked($achievement));
                    }
                }
                break;

            case 'course_enrolled':
                if (!$user->hasAchievement('first_course')) {
                    $achievement = UserAchievement::award($user, 'first_course');
                    if ($achievement) {
                        $awarded[] = $achievement;
                        $user->notify(new AchievementUnlocked($achievement));
                    }
                }
                break;

            case 'course_completed':
                $achievement = UserAchievement::award($user, 'course_completed', [
                    'course_id' => $data['course_id'] ?? null,
                    'course_title' => $data['course_title'] ?? null,
                ]);
                if ($achievement) {
                    $awarded[] = $achievement;
                    $user->notify(new AchievementUnlocked($achievement));
                }

                // Check for course completion badges
                $completedCourses = $user->courseEnrollments()->where('progress_percentage', 100)->count();
                $badges = $this->checkCourseBadges($user, $completedCourses);
                $awarded = array_merge($awarded, $badges);
                break;

            case 'level_up':
                $achievement = UserAchievement::award($user, 'level_up', [
                    'new_level' => $data['level'] ?? $user->level,
                    'previous_level' => $data['previous_level'] ?? $user->level - 1,
                ]);
                if ($achievement) {
                    $awarded[] = $achievement;
                    $user->notify(new AchievementUnlocked($achievement));
                }
                break;

            case 'login_streak':
                $streak = $data['streak'] ?? $user->login_streak;
                if ($streak >= 7 && !$user->hasAchievement('streak_7')) {
                    $achievement = UserAchievement::award($user, 'streak_7', ['streak' => $streak]);
                    if ($achievement) {
                        $awarded[] = $achievement;
                        $user->notify(new AchievementUnlocked($achievement));
                    }
                }
                if ($streak >= 30 && !$user->hasAchievement('streak_30')) {
                    $achievement = UserAchievement::award($user, 'streak_30', ['streak' => $streak]);
                    if ($achievement) {
                        $awarded[] = $achievement;
                        $user->notify(new AchievementUnlocked($achievement));
                    }
                }
                break;

            case 'event_attended':
                $achievement = UserAchievement::award($user, 'event_attended', [
                    'event_id' => $data['event_id'] ?? null,
                    'event_title' => $data['event_title'] ?? null,
                ]);
                if ($achievement) {
                    $awarded[] = $achievement;
                    $user->notify(new AchievementUnlocked($achievement));
                }
                break;

            case 'chat_message':
                $messageCount = $user->chatMessages()->count();
                if ($messageCount >= 100 && !$user->hasAchievement('chat_active')) {
                    $achievement = UserAchievement::award($user, 'chat_active', ['message_count' => $messageCount]);
                    if ($achievement) {
                        $awarded[] = $achievement;
                        $user->notify(new AchievementUnlocked($achievement));
                    }
                }
                break;

            case 'subscription_upgrade':
                $achievement = UserAchievement::award($user, 'subscription_upgrade', [
                    'plan' => $data['plan'] ?? $user->subscription_plan,
                ]);
                if ($achievement) {
                    $awarded[] = $achievement;
                    $user->notify(new AchievementUnlocked($achievement));
                }
                break;
        }

        return $awarded;
    }

    /**
     * Check for course completion badges
     */
    private function checkCourseBadges(User $user, int $completedCourses): array
    {
        $awarded = [];
        $badges = [
            1 => 'first_course_badge',
            5 => 'course_enthusiast',
            10 => 'learning_machine',
            25 => 'course_master',
            50 => 'education_legend',
        ];

        foreach ($badges as $threshold => $badgeSlug) {
            if ($completedCourses >= $threshold && !$user->hasBadge($badgeSlug)) {
                $badge = Badge::where('slug', $badgeSlug)->first();
                if ($badge) {
                    $user->badges()->attach($badge->id, ['earned_at' => now()]);
                    $awarded[] = $badge;
                    $user->notify(new BadgeEarned($badge));
                }
            }
        }

        return $awarded;
    }

    /**
     * Check for XP-based badges
     */
    public function checkXpBadges(User $user): array
    {
        $awarded = [];
        $badges = [
            1000 => 'xp_warrior',
            5000 => 'xp_champion',
            10000 => 'xp_legend',
            25000 => 'xp_master',
            50000 => 'xp_god',
        ];

        foreach ($badges as $threshold => $badgeSlug) {
            if ($user->xp >= $threshold && !$user->hasBadge($badgeSlug)) {
                $badge = Badge::where('slug', $badgeSlug)->first();
                if ($badge) {
                    $user->badges()->attach($badge->id, ['earned_at' => now()]);
                    $awarded[] = $badge;
                    $user->notify(new BadgeEarned($badge));
                }
            }
        }

        return $awarded;
    }

    /**
     * Get user's achievement progress
     */
    public function getAchievementProgress(User $user): array
    {
        return [
            'total_achievements' => $user->achievements()->count(),
            'total_badges' => $user->badges()->count(),
            'total_xp' => $user->xp,
            'current_level' => $user->level,
            'courses_completed' => $user->courseEnrollments()->where('progress_percentage', 100)->count(),
            'events_attended' => $user->eventRsvps()->where('attended', true)->count(),
            'login_streak' => $user->login_streak ?? 0,
            'chat_messages' => $user->chatMessages()->count(),
        ];
    }

    /**
     * Get leaderboard data
     */
    public function getLeaderboard(string $type = 'xp', int $limit = 10): array
    {
        switch ($type) {
            case 'xp':
                return User::orderBy('xp', 'desc')
                    ->take($limit)
                    ->get(['id', 'name', 'avatar', 'xp', 'level'])
                    ->toArray();

            case 'courses':
                return User::withCount(['courseEnrollments' => function ($query) {
                        $query->where('progress_percentage', 100);
                    }])
                    ->orderBy('course_enrollments_count', 'desc')
                    ->take($limit)
                    ->get(['id', 'name', 'avatar', 'course_enrollments_count'])
                    ->toArray();

            case 'badges':
                return User::withCount('badges')
                    ->orderBy('badges_count', 'desc')
                    ->take($limit)
                    ->get(['id', 'name', 'avatar', 'badges_count'])
                    ->toArray();

            default:
                return [];
        }
    }
}
