<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use App\Models\Course;
use App\Models\Event;
use App\Models\User;
use App\Models\Campus;

class CacheService
{
    /**
     * Cache key prefixes
     */
    const POPULAR_COURSES = 'popular_courses';
    const UPCOMING_EVENTS = 'upcoming_events';
    const USER_STATS = 'user_stats';
    const CAMPUS_COURSES = 'campus_courses';
    const PLATFORM_STATS = 'platform_stats';
    const SEARCH_RESULTS = 'search_results';
    const USER_PROGRESS = 'user_progress';

    /**
     * Default cache durations (in minutes)
     */
    const CACHE_DURATIONS = [
        self::POPULAR_COURSES => 360,    // 6 hours
        self::UPCOMING_EVENTS => 60,     // 1 hour
        self::USER_STATS => 720,         // 12 hours
        self::CAMPUS_COURSES => 240,     // 4 hours
        self::PLATFORM_STATS => 720,     // 12 hours
        self::SEARCH_RESULTS => 30,      // 30 minutes
        self::USER_PROGRESS => 60,       // 1 hour
    ];

    /**
     * Get popular courses with caching
     */
    public function getPopularCourses(int $limit = 20): \Illuminate\Support\Collection
    {
        return Cache::remember(
            self::POPULAR_COURSES . "_{$limit}",
            now()->addMinutes(self::CACHE_DURATIONS[self::POPULAR_COURSES]),
            function () use ($limit) {
                return Course::with(['campus'])
                    ->where('is_published', true)
                    ->withCount('enrollments')
                    ->orderBy('enrollments_count', 'desc')
                    ->orderBy('rating', 'desc')
                    ->limit($limit)
                    ->get();
            }
        );
    }

    /**
     * Get upcoming events with caching
     */
    public function getUpcomingEvents(int $limit = 10): \Illuminate\Support\Collection
    {
        return Cache::remember(
            self::UPCOMING_EVENTS . "_{$limit}",
            now()->addMinutes(self::CACHE_DURATIONS[self::UPCOMING_EVENTS]),
            function () use ($limit) {
                return Event::with(['creator'])
                    ->where('is_active', true)
                    ->where('start_time', '>=', now())
                    ->orderBy('start_time', 'asc')
                    ->limit($limit)
                    ->get();
            }
        );
    }

    /**
     * Get user statistics with caching
     */
    public function getUserStatistics(int $userId): array
    {
        return Cache::remember(
            self::USER_STATS . "_{$userId}",
            now()->addMinutes(self::CACHE_DURATIONS[self::USER_STATS]),
            function () use ($userId) {
                $user = User::find($userId);
                if (!$user) {
                    return [];
                }

                return [
                    'total_xp' => $user->xp,
                    'level' => $user->level,
                    'courses_completed' => $user->completedCourses()->count(),
                    'events_attended' => $user->attendedEvents()->count(),
                    'chat_messages' => $user->chatMessages()->count(),
                    'subscription_plan' => $user->subscription_plan,
                    'member_since' => $user->created_at->format('Y-m-d'),
                ];
            }
        );
    }

    /**
     * Get courses by campus with caching
     */
    public function getCoursesByCampus(int $campusId, int $limit = 20): \Illuminate\Support\Collection
    {
        return Cache::remember(
            self::CAMPUS_COURSES . "_{$campusId}_{$limit}",
            now()->addMinutes(self::CACHE_DURATIONS[self::CAMPUS_COURSES]),
            function () use ($campusId, $limit) {
                return Course::where('campus_id', $campusId)
                    ->where('is_published', true)
                    ->withCount('enrollments')
                    ->orderBy('enrollments_count', 'desc')
                    ->limit($limit)
                    ->get();
            }
        );
    }

    /**
     * Get platform statistics with caching
     */
    public function getPlatformStatistics(): array
    {
        return Cache::remember(
            self::PLATFORM_STATS,
            now()->addMinutes(self::CACHE_DURATIONS[self::PLATFORM_STATS]),
            function () {
                return [
                    'total_users' => User::count(),
                    'active_users' => User::where('is_active', true)->count(),
                    'premium_users' => User::whereNotNull('subscription_plan')
                        ->where('subscription_plan', '!=', 'free')->count(),
                    'total_courses' => Course::where('is_published', true)->count(),
                    'total_events' => Event::where('is_active', true)->count(),
                    'total_campuses' => Campus::count(),
                    'total_xp_earned' => User::sum('xp'),
                    'courses_completed_today' => DB::table('course_enrollments')
                        ->where('completed_at', '>=', now()->startOfDay())
                        ->count(),
                    'events_today' => Event::whereDate('start_time', today())->count(),
                ];
            }
        );
    }

    /**
     * Cache search results
     */
    public function cacheSearchResults(string $query, string $type, array $results): void
    {
        $key = self::SEARCH_RESULTS . '_' . md5($query . $type);
        Cache::put(
            $key,
            $results,
            now()->addMinutes(self::CACHE_DURATIONS[self::SEARCH_RESULTS])
        );
    }

    /**
     * Get cached search results
     */
    public function getCachedSearchResults(string $query, string $type): ?array
    {
        $key = self::SEARCH_RESULTS . '_' . md5($query . $type);
        return Cache::get($key);
    }

    /**
     * Get user progress with caching
     */
    public function getUserProgress(int $userId): array
    {
        return Cache::remember(
            self::USER_PROGRESS . "_{$userId}",
            now()->addMinutes(self::CACHE_DURATIONS[self::USER_PROGRESS]),
            function () use ($userId) {
                $user = User::find($userId);
                if (!$user) {
                    return [];
                }

                $enrollments = $user->courseEnrollments()->with('course')->get();
                $progress = [];

                foreach ($enrollments as $enrollment) {
                    $progress[] = [
                        'course_id' => $enrollment->course_id,
                        'course_title' => $enrollment->course->title,
                        'progress_percentage' => $enrollment->progress_percentage,
                        'completed_at' => $enrollment->completed_at,
                        'last_accessed' => $enrollment->last_accessed_at,
                    ];
                }

                return $progress;
            }
        );
    }

    /**
     * Invalidate user-related caches
     */
    public function invalidateUserCaches(int $userId): void
    {
        $patterns = [
            self::USER_STATS . "_{$userId}",
            self::USER_PROGRESS . "_{$userId}",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }

        // Also invalidate platform stats as user data affects them
        Cache::forget(self::PLATFORM_STATS);
    }

    /**
     * Invalidate course-related caches
     */
    public function invalidateCourseCaches(int $courseId = null, int $campusId = null): void
    {
        // Clear popular courses cache
        Cache::forget(self::POPULAR_COURSES . '_20');
        Cache::forget(self::POPULAR_COURSES . '_10');

        // Clear campus courses cache if campus is specified
        if ($campusId) {
            Cache::forget(self::CAMPUS_COURSES . "_{$campusId}_20");
            Cache::forget(self::CAMPUS_COURSES . "_{$campusId}_10");
        }

        // Clear platform stats
        Cache::forget(self::PLATFORM_STATS);
    }

    /**
     * Invalidate event-related caches
     */
    public function invalidateEventCaches(): void
    {
        Cache::forget(self::UPCOMING_EVENTS . '_10');
        Cache::forget(self::UPCOMING_EVENTS . '_20');
        Cache::forget(self::PLATFORM_STATS);
    }

    /**
     * Clear all application caches
     */
    public function clearAllCaches(): void
    {
        $keys = [
            self::POPULAR_COURSES,
            self::UPCOMING_EVENTS,
            self::USER_STATS,
            self::CAMPUS_COURSES,
            self::PLATFORM_STATS,
            self::SEARCH_RESULTS,
            self::USER_PROGRESS,
        ];

        foreach ($keys as $key) {
            Cache::flush();
        }
    }

    /**
     * Warm up critical caches
     */
    public function warmupCaches(): void
    {
        // Warm up popular courses
        $this->getPopularCourses(20);
        $this->getPopularCourses(10);

        // Warm up upcoming events
        $this->getUpcomingEvents(10);

        // Warm up platform statistics
        $this->getPlatformStatistics();

        // Warm up campus courses for each campus
        $campuses = Campus::pluck('id');
        foreach ($campuses as $campusId) {
            $this->getCoursesByCampus($campusId, 20);
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStatistics(): array
    {
        // This would be expanded with actual cache hit/miss statistics
        // For now, return basic information
        return [
            'cache_driver' => config('cache.default'),
            'cache_prefix' => config('cache.prefix'),
            'cached_items' => [
                'popular_courses' => Cache::has(self::POPULAR_COURSES . '_20'),
                'upcoming_events' => Cache::has(self::UPCOMING_EVENTS . '_10'),
                'platform_stats' => Cache::has(self::PLATFORM_STATS),
            ],
        ];
    }
}
