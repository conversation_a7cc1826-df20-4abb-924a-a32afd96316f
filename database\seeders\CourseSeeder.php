<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;
use App\Models\Course;
use App\Models\Lesson;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get campuses
        $ecommerceCampus = Campus::where('slug', 'ecommerce')->first();
        $freelancingCampus = Campus::where('slug', 'freelancing')->first();
        $cryptoCampus = Campus::where('slug', 'cryptocurrency')->first();

        if ($ecommerceCampus) {
            // E-commerce courses
            $course1 = Course::create([
                'campus_id' => $ecommerceCampus->id,
                'title' => 'Dropshipping Fundamentals',
                'slug' => 'dropshipping-fundamentals',
                'description' => 'Learn the basics of dropshipping and how to start your first profitable store.',
                'learning_objectives' => 'Master dropshipping fundamentals, product research, and store setup',
                'duration_minutes' => 180,
                'difficulty_level' => 'beginner',
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => true,
                'is_premium' => false,
                'is_published' => true,
                'xp_reward' => 50,
            ]);

            // Lessons for Dropshipping Fundamentals
            Lesson::create([
                'course_id' => $course1->id,
                'title' => 'What is Dropshipping?',
                'description' => 'Introduction to the dropshipping business model and its advantages.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 900,
                'sort_order' => 1,
                'is_active' => true,
                'is_preview' => true,
                'xp_reward' => 10,
            ]);

            Lesson::create([
                'course_id' => $course1->id,
                'title' => 'Finding Profitable Products',
                'description' => 'Learn how to research and identify winning products for your store.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 1200,
                'sort_order' => 2,
                'is_active' => true,
                'is_preview' => false,
                'xp_reward' => 15,
            ]);

            Lesson::create([
                'course_id' => $course1->id,
                'title' => 'Setting Up Your Shopify Store',
                'description' => 'Step-by-step guide to creating your first dropshipping store.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 1800,
                'sort_order' => 3,
                'is_active' => true,
                'is_preview' => false,
                'xp_reward' => 20,
            ]);

            // Second E-commerce course
            $course2 = Course::create([
                'campus_id' => $ecommerceCampus->id,
                'title' => 'Amazon FBA Mastery',
                'slug' => 'amazon-fba-mastery',
                'description' => 'Master Amazon FBA and build a profitable business on the world\'s largest marketplace.',
                'learning_objectives' => 'Learn Amazon FBA strategies, product sourcing, and scaling techniques',
                'duration_minutes' => 240,
                'difficulty_level' => 'intermediate',
                'sort_order' => 2,
                'is_active' => true,
                'is_featured' => false,
                'is_premium' => true,
                'is_published' => true,
                'xp_reward' => 75,
            ]);

            Lesson::create([
                'course_id' => $course2->id,
                'title' => 'Amazon FBA Overview',
                'description' => 'Understanding the Amazon FBA business model and opportunities.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 1080,
                'sort_order' => 1,
                'is_active' => true,
                'is_preview' => true,
                'xp_reward' => 12,
            ]);
        }

        if ($freelancingCampus) {
            // Freelancing course
            $course3 = Course::create([
                'campus_id' => $freelancingCampus->id,
                'title' => 'Copywriting Mastery',
                'slug' => 'copywriting-mastery',
                'description' => 'Learn high-converting copywriting skills that clients pay premium rates for.',
                'learning_objectives' => 'Master copywriting fundamentals, headline writing, and conversion techniques',
                'duration_minutes' => 150,
                'difficulty_level' => 'beginner',
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => true,
                'is_premium' => false,
                'is_published' => true,
                'xp_reward' => 60,
            ]);

            Lesson::create([
                'course_id' => $course3->id,
                'title' => 'Introduction to Copywriting',
                'description' => 'What is copywriting and why it\'s one of the highest-paid skills.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 720,
                'sort_order' => 1,
                'is_active' => true,
                'is_preview' => true,
                'xp_reward' => 10,
            ]);

            Lesson::create([
                'course_id' => $course3->id,
                'title' => 'Writing Headlines That Convert',
                'description' => 'Master the art of writing compelling headlines that grab attention.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 960,
                'sort_order' => 2,
                'is_active' => true,
                'is_preview' => false,
                'xp_reward' => 15,
            ]);
        }

        if ($cryptoCampus) {
            // Cryptocurrency course
            $course4 = Course::create([
                'campus_id' => $cryptoCampus->id,
                'title' => 'Crypto Trading Basics',
                'slug' => 'crypto-trading-basics',
                'description' => 'Learn the fundamentals of cryptocurrency trading and technical analysis.',
                'learning_objectives' => 'Understand cryptocurrency markets, trading strategies, and risk management',
                'duration_minutes' => 200,
                'difficulty_level' => 'intermediate',
                'sort_order' => 1,
                'is_active' => true,
                'is_featured' => true,
                'is_premium' => true,
                'is_published' => true,
                'xp_reward' => 80,
            ]);

            Lesson::create([
                'course_id' => $course4->id,
                'title' => 'Understanding Cryptocurrency',
                'description' => 'What is cryptocurrency and how does blockchain technology work.',
                'video_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'video_type' => 'youtube',
                'duration_seconds' => 1200,
                'sort_order' => 1,
                'is_active' => true,
                'is_preview' => true,
                'xp_reward' => 15,
            ]);
        }
    }
}
