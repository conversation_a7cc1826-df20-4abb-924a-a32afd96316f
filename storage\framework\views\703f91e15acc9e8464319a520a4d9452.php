<div class="flex items-start space-x-3 <?php echo e($message->user_id === $user->id ? 'flex-row-reverse space-x-reverse' : ''); ?>">
    <!-- User Avatar -->
    <div class="flex-shrink-0">
        <img src="<?php echo e($message->user->getAvatarUrl()); ?>" 
             alt="<?php echo e($message->user->name); ?>" 
             class="w-8 h-8 rounded-full">
    </div>

    <!-- Message Content -->
    <div class="flex-1 min-w-0">
        <!-- Message Header -->
        <div class="flex items-center space-x-2 mb-1 <?php echo e($message->user_id === $user->id ? 'justify-end' : ''); ?>">
            <span class="text-sm font-medium <?php echo e($message->user_id === $user->id ? 'text-primary-400' : 'text-white'); ?>">
                <?php echo e($message->user->name); ?>

            </span>
            
            <!-- User Level Badge -->
            <span class="bg-gray-600 text-gray-300 text-xs px-2 py-0.5 rounded">
                Level <?php echo e($message->user->level); ?>

            </span>
            
            <!-- User Role Badge -->
            <?php if($message->user->hasRole('admin')): ?>
                <span class="bg-red-600 text-white text-xs px-2 py-0.5 rounded">Admin</span>
            <?php elseif($message->user->hasRole('mentor')): ?>
                <span class="bg-blue-600 text-white text-xs px-2 py-0.5 rounded">Mentor</span>
            <?php elseif($message->user->hasRole('champion')): ?>
                <span class="bg-yellow-600 text-white text-xs px-2 py-0.5 rounded">Champion</span>
            <?php endif; ?>
            
            <!-- Timestamp -->
            <span class="text-xs text-gray-500"><?php echo e($message->getFormattedTime()); ?></span>
            
            <!-- Edited Indicator -->
            <?php if($message->is_edited): ?>
                <span class="text-xs text-gray-500 italic">(edited)</span>
            <?php endif; ?>
        </div>

        <!-- Message Text -->
        <div class="p-3 rounded-lg <?php echo e($message->user_id === $user->id ? 'bg-primary-600 text-white ml-auto max-w-xs' : 'bg-gray-700 text-gray-100 max-w-md'); ?>">
            <p class="text-sm break-words"><?php echo $message->getProcessedMessage(); ?></p>
        </div>

        <!-- Message Actions (for own messages) -->
        <?php if($message->user_id === $user->id): ?>
        <div class="flex items-center justify-end space-x-2 mt-1">
            <?php if($message->canBeEditedBy($user)): ?>
                <button onclick="editMessage(<?php echo e($message->id); ?>)" 
                        class="text-xs text-gray-500 hover:text-gray-300">
                    Edit
                </button>
            <?php endif; ?>
            
            <?php if($message->canBeDeletedBy($user)): ?>
                <button onclick="deleteMessage(<?php echo e($message->id); ?>)" 
                        class="text-xs text-gray-500 hover:text-red-400">
                    Delete
                </button>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/chat/partials/message.blade.php ENDPATH**/ ?>