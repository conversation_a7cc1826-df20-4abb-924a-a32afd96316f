@props([
    'title' => 'Quick Search',
    'compact' => false
])

<div class="search-widget {{ $compact ? 'compact' : '' }}">
    @if(!$compact)
    <div class="mb-4">
        <h3 class="text-lg font-semibold mb-2">{{ $title }}</h3>
        <p class="text-sm text-gray-400">Find courses, events, and more</p>
    </div>
    @endif

    <!-- Search Input -->
    <div class="mb-4">
        <x-search-bar placeholder="Search..." size="small" />
    </div>

    @if(!$compact)
    <!-- Quick Links -->
    <div class="space-y-3">
        <h4 class="text-sm font-medium text-gray-300">Popular Searches</h4>
        
        <div class="space-y-2">
            <a href="{{ route('search') }}?q=freelancing" 
               class="flex items-center text-sm text-gray-400 hover:text-white transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Freelancing
            </a>
            
            <a href="{{ route('search') }}?q=crypto" 
               class="flex items-center text-sm text-gray-400 hover:text-white transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Crypto Trading
            </a>
            
            <a href="{{ route('search') }}?q=copywriting" 
               class="flex items-center text-sm text-gray-400 hover:text-white transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Copywriting
            </a>
            
            <a href="{{ route('search') }}?q=e-commerce" 
               class="flex items-center text-sm text-gray-400 hover:text-white transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                E-commerce
            </a>
        </div>
    </div>

    <!-- Search Categories -->
    <div class="mt-6 space-y-3">
        <h4 class="text-sm font-medium text-gray-300">Browse by Category</h4>
        
        <div class="grid grid-cols-2 gap-2">
            <a href="{{ route('search') }}?type=courses" 
               class="flex items-center p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                <span class="text-lg mr-2">📚</span>
                <div>
                    <div class="text-xs font-medium">Courses</div>
                    <div class="text-xs text-gray-400">Learn & Grow</div>
                </div>
            </a>
            
            <a href="{{ route('search') }}?type=events" 
               class="flex items-center p-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                <span class="text-lg mr-2">📅</span>
                <div>
                    <div class="text-xs font-medium">Events</div>
                    <div class="text-xs text-gray-400">Live Sessions</div>
                </div>
            </a>
        </div>
    </div>

    <!-- Advanced Search Link -->
    <div class="mt-6 pt-4 border-t border-gray-700">
        <a href="{{ route('search.advanced') }}" 
           class="flex items-center justify-center w-full p-2 text-sm text-primary-400 hover:text-primary-300 hover:bg-gray-700 rounded-lg transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
            </svg>
            Advanced Search
        </a>
    </div>
    @endif
</div>

<style>
.search-widget.compact {
    padding: 1rem;
}

.search-widget.compact .search-input {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}
</style>
