<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class AdminSettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        $settings = [
            'general' => [
                'app_name' => config('app.name'),
                'app_url' => config('app.url'),
                'timezone' => config('app.timezone'),
                'maintenance_mode' => app()->isDownForMaintenance(),
            ],
            'email' => [
                'mail_driver' => config('mail.default'),
                'mail_host' => config('mail.mailers.smtp.host'),
                'mail_port' => config('mail.mailers.smtp.port'),
                'mail_from_address' => config('mail.from.address'),
                'mail_from_name' => config('mail.from.name'),
            ],
            'security' => [
                'session_lifetime' => config('session.lifetime'),
                'password_timeout' => config('auth.password_timeout'),
                'two_factor_enabled' => false, // This would come from database
            ],
        ];

        return view('admin.settings.index', compact('settings'));
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'timezone' => 'required|string',
        ]);

        // In a real application, you would save these to a settings table
        // For now, we'll just cache them
        Cache::put('settings.app_name', $request->app_name, now()->addDays(30));
        Cache::put('settings.app_url', $request->app_url, now()->addDays(30));
        Cache::put('settings.timezone', $request->timezone, now()->addDays(30));

        return redirect()->route('admin.settings.index')
            ->with('success', 'General settings updated successfully.');
    }

    /**
     * Update email settings.
     */
    public function updateEmail(Request $request)
    {
        $request->validate([
            'mail_driver' => 'required|string',
            'mail_host' => 'required|string',
            'mail_port' => 'required|integer',
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string',
        ]);

        // Cache email settings
        Cache::put('settings.mail_driver', $request->mail_driver, now()->addDays(30));
        Cache::put('settings.mail_host', $request->mail_host, now()->addDays(30));
        Cache::put('settings.mail_port', $request->mail_port, now()->addDays(30));
        Cache::put('settings.mail_from_address', $request->mail_from_address, now()->addDays(30));
        Cache::put('settings.mail_from_name', $request->mail_from_name, now()->addDays(30));

        return redirect()->route('admin.settings.index')
            ->with('success', 'Email settings updated successfully.');
    }

    /**
     * Update security settings.
     */
    public function updateSecurity(Request $request)
    {
        $request->validate([
            'session_lifetime' => 'required|integer|min:1|max:10080', // Max 1 week
            'password_timeout' => 'required|integer|min:1|max:1440', // Max 24 hours
            'two_factor_enabled' => 'boolean',
        ]);

        // Cache security settings
        Cache::put('settings.session_lifetime', $request->session_lifetime, now()->addDays(30));
        Cache::put('settings.password_timeout', $request->password_timeout, now()->addDays(30));
        Cache::put('settings.two_factor_enabled', $request->boolean('two_factor_enabled'), now()->addDays(30));

        return redirect()->route('admin.settings.index')
            ->with('success', 'Security settings updated successfully.');
    }
}
