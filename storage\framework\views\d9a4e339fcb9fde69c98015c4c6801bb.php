<?php $__env->startSection('title', 'Subscription Settings - The Real World'); ?>
<?php $__env->startSection('page-title', 'Subscription Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-bold mb-2">Subscription Management</h2>
                <p class="text-gray-400">Manage your subscription plan and billing preferences</p>
            </div>
            <a href="<?php echo e(route('dashboard.settings.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Settings
            </a>
        </div>
    </div>

    <!-- Current Subscription -->
    <div class="card mb-8">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold">Current Subscription</h3>
            <?php if(auth()->user()->subscription_plan !== 'free'): ?>
                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                    Active
                </span>
            <?php else: ?>
                <span class="bg-gray-600 text-gray-300 px-3 py-1 rounded-full text-sm">
                    Free Plan
                </span>
            <?php endif; ?>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Plan Details -->
            <div class="md:col-span-2">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="text-4xl">
                        <?php switch(auth()->user()->subscription_plan):
                            case ('prosper'): ?>
                                🌱
                                <?php break; ?>
                            <?php case ('conquer'): ?>
                                ⚔️
                                <?php break; ?>
                            <?php case ('champions'): ?>
                                👑
                                <?php break; ?>
                            <?php default: ?>
                                🆓
                        <?php endswitch; ?>
                    </div>
                    <div>
                        <h4 class="text-xl font-bold">
                            <?php echo e(ucfirst(auth()->user()->subscription_plan ?? 'free')); ?> Plan
                        </h4>
                        <p class="text-gray-400">
                            <?php switch(auth()->user()->subscription_plan):
                                case ('prosper'): ?>
                                    Essential tools for your financial journey
                                    <?php break; ?>
                                <?php case ('conquer'): ?>
                                    Advanced strategies and exclusive content
                                    <?php break; ?>
                                <?php case ('champions'): ?>
                                    Elite access with personal mentorship
                                    <?php break; ?>
                                <?php default: ?>
                                    Limited access to basic content
                            <?php endswitch; ?>
                        </p>
                    </div>
                </div>

                <?php if(auth()->user()->subscription_plan !== 'free'): ?>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-400">Monthly Price:</span>
                            <div class="font-semibold">
                                <?php switch(auth()->user()->subscription_plan):
                                    case ('prosper'): ?>
                                        $49/month
                                        <?php break; ?>
                                    <?php case ('conquer'): ?>
                                        $99/month
                                        <?php break; ?>
                                    <?php case ('champions'): ?>
                                        $199/month
                                        <?php break; ?>
                                <?php endswitch; ?>
                            </div>
                        </div>
                        <div>
                            <span class="text-gray-400">Next Billing:</span>
                            <div class="font-semibold"><?php echo e(now()->addMonth()->format('M j, Y')); ?></div>
                        </div>
                        <div>
                            <span class="text-gray-400">Status:</span>
                            <div class="font-semibold text-green-400">Active</div>
                        </div>
                        <div>
                            <span class="text-gray-400">Auto-Renewal:</span>
                            <div class="font-semibold">Enabled</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="space-y-3">
                <?php if(auth()->user()->subscription_plan === 'free'): ?>
                    <a href="<?php echo e(route('pricing')); ?>" class="btn-primary w-full text-center">
                        Upgrade Now
                    </a>
                <?php else: ?>
                    <button onclick="openUpgradeModal()" class="btn-primary w-full">
                        Change Plan
                    </button>
                    <button onclick="openCancelModal()" class="btn-secondary w-full">
                        Cancel Subscription
                    </button>
                <?php endif; ?>
                
                <a href="<?php echo e(route('dashboard.settings.billing')); ?>" class="btn-secondary w-full text-center">
                    View Billing History
                </a>
            </div>
        </div>
    </div>

    <!-- Plan Comparison -->
    <div class="card mb-8">
        <h3 class="text-lg font-semibold mb-6">Available Plans</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Prosper Plan -->
            <div class="border border-gray-700 rounded-lg p-6 <?php echo e(auth()->user()->subscription_plan === 'prosper' ? 'border-primary-600 bg-primary-900/20' : ''); ?>">
                <div class="text-center mb-6">
                    <div class="text-4xl mb-2">🌱</div>
                    <h4 class="text-xl font-bold mb-2">Prosper</h4>
                    <div class="text-3xl font-bold mb-1">$49</div>
                    <div class="text-gray-400 text-sm">per month</div>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Access to all basic courses
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Community chat access
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Monthly live events
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Basic support
                    </li>
                </ul>
                
                <?php if(auth()->user()->subscription_plan === 'prosper'): ?>
                    <button class="btn-secondary w-full" disabled>Current Plan</button>
                <?php elseif(auth()->user()->subscription_plan === 'free'): ?>
                    <button onclick="upgradeToPlan('prosper')" class="btn-primary w-full">Select Plan</button>
                <?php else: ?>
                    <button onclick="changeToPlan('prosper')" class="btn-secondary w-full">Downgrade</button>
                <?php endif; ?>
            </div>

            <!-- Conquer Plan -->
            <div class="border border-gray-700 rounded-lg p-6 <?php echo e(auth()->user()->subscription_plan === 'conquer' ? 'border-primary-600 bg-primary-900/20' : ''); ?> relative">
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span class="bg-primary-600 text-white px-3 py-1 rounded-full text-xs font-semibold">
                        Most Popular
                    </span>
                </div>
                
                <div class="text-center mb-6">
                    <div class="text-4xl mb-2">⚔️</div>
                    <h4 class="text-xl font-bold mb-2">Conquer</h4>
                    <div class="text-3xl font-bold mb-1">$99</div>
                    <div class="text-gray-400 text-sm">per month</div>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Everything in Prosper
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Advanced courses & strategies
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Weekly live events
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Priority support
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Exclusive resources
                    </li>
                </ul>
                
                <?php if(auth()->user()->subscription_plan === 'conquer'): ?>
                    <button class="btn-secondary w-full" disabled>Current Plan</button>
                <?php elseif(in_array(auth()->user()->subscription_plan, ['free', 'prosper'])): ?>
                    <button onclick="upgradeToPlan('conquer')" class="btn-primary w-full">Select Plan</button>
                <?php else: ?>
                    <button onclick="changeToPlan('conquer')" class="btn-secondary w-full">Downgrade</button>
                <?php endif; ?>
            </div>

            <!-- Champions Plan -->
            <div class="border border-gray-700 rounded-lg p-6 <?php echo e(auth()->user()->subscription_plan === 'champions' ? 'border-primary-600 bg-primary-900/20' : ''); ?>">
                <div class="text-center mb-6">
                    <div class="text-4xl mb-2">👑</div>
                    <h4 class="text-xl font-bold mb-2">Champions</h4>
                    <div class="text-3xl font-bold mb-1">$199</div>
                    <div class="text-gray-400 text-sm">per month</div>
                </div>
                
                <ul class="space-y-3 mb-6">
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Everything in Conquer
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        1-on-1 mentorship calls
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Daily live events
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        VIP support
                    </li>
                    <li class="flex items-center text-sm">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Elite community access
                    </li>
                </ul>
                
                <?php if(auth()->user()->subscription_plan === 'champions'): ?>
                    <button class="btn-secondary w-full" disabled>Current Plan</button>
                <?php else: ?>
                    <button onclick="upgradeToPlan('champions')" class="btn-primary w-full">Select Plan</button>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Billing Preferences -->
    <?php if(auth()->user()->subscription_plan !== 'free'): ?>
        <div class="card">
            <h3 class="text-lg font-semibold mb-6">Billing Preferences</h3>
            
            <form action="<?php echo e(route('dashboard.settings.subscription.update')); ?>" method="POST" class="space-y-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">Billing Cycle</label>
                        <select name="billing_cycle" class="input-field">
                            <option value="monthly" selected>Monthly</option>
                            <option value="yearly">Yearly (Save 20%)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium mb-2">Auto-Renewal</label>
                        <select name="auto_renewal" class="input-field">
                            <option value="1" selected>Enabled</option>
                            <option value="0">Disabled</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="email_receipts" name="email_receipts" checked class="mr-3">
                    <label for="email_receipts" class="text-sm">Send email receipts for payments</label>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="renewal_reminders" name="renewal_reminders" checked class="mr-3">
                    <label for="renewal_reminders" class="text-sm">Send renewal reminders 3 days before billing</label>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="btn-primary">
                        Save Preferences
                    </button>
                </div>
            </form>
        </div>
    <?php endif; ?>
</div>

<!-- Upgrade Modal -->
<div id="upgradeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Confirm Plan Change</h3>
        <p class="text-gray-400 mb-6">Are you sure you want to change your subscription plan?</p>
        
        <div class="flex space-x-4">
            <button onclick="closeUpgradeModal()" class="btn-secondary flex-1">Cancel</button>
            <button onclick="confirmPlanChange()" class="btn-primary flex-1">Confirm</button>
        </div>
    </div>
</div>

<!-- Cancel Modal -->
<div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Cancel Subscription</h3>
        <p class="text-gray-400 mb-6">Are you sure you want to cancel your subscription? You'll lose access to premium features at the end of your billing cycle.</p>
        
        <div class="flex space-x-4">
            <button onclick="closeCancelModal()" class="btn-secondary flex-1">Keep Subscription</button>
            <button onclick="confirmCancellation()" class="btn-danger flex-1">Cancel Subscription</button>
        </div>
    </div>
</div>

<script>
let selectedPlan = null;

function upgradeToPlan(plan) {
    selectedPlan = plan;
    openUpgradeModal();
}

function changeToPlan(plan) {
    selectedPlan = plan;
    openUpgradeModal();
}

function openUpgradeModal() {
    document.getElementById('upgradeModal').classList.remove('hidden');
    document.getElementById('upgradeModal').classList.add('flex');
}

function closeUpgradeModal() {
    document.getElementById('upgradeModal').classList.add('hidden');
    document.getElementById('upgradeModal').classList.remove('flex');
    selectedPlan = null;
}

function openCancelModal() {
    document.getElementById('cancelModal').classList.remove('hidden');
    document.getElementById('cancelModal').classList.add('flex');
}

function closeCancelModal() {
    document.getElementById('cancelModal').classList.add('hidden');
    document.getElementById('cancelModal').classList.remove('flex');
}

function confirmPlanChange() {
    if (selectedPlan) {
        // Redirect to payment processing
        window.location.href = `/subscription/change/${selectedPlan}`;
    }
}

function confirmCancellation() {
    // Submit cancellation request
    fetch('/api/subscription/cancel', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    }).then(response => {
        if (response.ok) {
            window.location.reload();
        }
    });
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/settings/subscription.blade.php ENDPATH**/ ?>