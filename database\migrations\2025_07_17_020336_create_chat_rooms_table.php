<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_rooms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campus_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['general', 'announcements', 'help', 'custom'])->default('general');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_moderated')->default(false);
            $table->json('allowed_roles')->nullable(); // JSON array of roles that can access
            $table->integer('message_retention_days')->default(30); // Auto-delete old messages
            $table->timestamps();

            $table->index(['campus_id', 'is_active']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_rooms');
    }
};
