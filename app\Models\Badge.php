<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Badge extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon_url',
        'color',
        'type',
        'requirements',
        'xp_reward',
        'is_active',
        'is_rare',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_rare' => 'boolean',
        'requirements' => 'array',
    ];

    /**
     * Relationships
     */
    public function userBadges(): HasMany
    {
        return $this->hasMany(UserBadge::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Helper Methods
     */
    public function isEarnedBy(User $user): bool
    {
        return $this->userBadges()
            ->where('user_id', $user->id)
            ->exists();
    }

    public function getTotalEarned(): int
    {
        return $this->userBadges()->count();
    }
}
