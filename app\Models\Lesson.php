<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Lesson extends Model
{
    use HasFactory;

    protected $fillable = [
        'course_id',
        'title',
        'description',
        'video_url',
        'video_type',
        'resource_url',
        'content',
        'duration_seconds',
        'sort_order',
        'is_active',
        'is_preview',
        'xp_reward',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_preview' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    public function userProgress(): HasMany
    {
        return $this->hasMany(UserLessonProgress::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePreview($query)
    {
        return $query->where('is_preview', true);
    }

    /**
     * Helper Methods
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    public function isCompletedBy(User $user): bool
    {
        return $this->userProgress()
            ->where('user_id', $user->id)
            ->where('is_completed', true)
            ->exists();
    }

    public function getProgressForUser(User $user)
    {
        $progress = $this->userProgress()
            ->where('user_id', $user->id)
            ->first();

        return $progress ? $progress->progress_percentage : 0;
    }

    public function getFormattedDuration()
    {
        if (!$this->duration_seconds) return 'Unknown';

        $minutes = floor($this->duration_seconds / 60);
        $seconds = $this->duration_seconds % 60;

        if ($minutes > 0) {
            return $minutes . 'm ' . $seconds . 's';
        }

        return $seconds . 's';
    }

    public function getVideoEmbedUrl()
    {
        if (!$this->video_url) return null;

        switch ($this->video_type) {
            case 'youtube':
                // Convert YouTube URL to embed format
                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/', $this->video_url, $matches)) {
                    return 'https://www.youtube.com/embed/' . $matches[1];
                }
                break;
            case 'vimeo':
                // Convert Vimeo URL to embed format
                if (preg_match('/vimeo\.com\/(\d+)/', $this->video_url, $matches)) {
                    return 'https://player.vimeo.com/video/' . $matches[1];
                }
                break;
            case 'direct':
            case 'embed':
                return $this->video_url;
        }

        return $this->video_url;
    }
}
