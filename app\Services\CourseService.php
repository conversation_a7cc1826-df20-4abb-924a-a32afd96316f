<?php

namespace App\Services;

use App\Models\Course;
use App\Models\CourseEnrollment;
use App\Models\User;
use App\Models\Lesson;
use App\Models\UserLessonProgress;
use App\Notifications\CourseCompleted;

class CourseService
{
    /**
     * Enroll user in a course
     */
    public function enrollUser(User $user, Course $course): CourseEnrollment
    {
        // Check if user is already enrolled
        $existingEnrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if ($existingEnrollment) {
            return $existingEnrollment;
        }

        // Create new enrollment
        $enrollment = CourseEnrollment::create([
            'user_id' => $user->id,
            'course_id' => $course->id,
            'enrolled_at' => now(),
        ]);

        // Award achievement for first course enrollment
        app(AchievementService::class)->checkAchievements($user, 'course_enrolled', [
            'course_id' => $course->id,
            'course_title' => $course->title,
        ]);

        return $enrollment;
    }

    /**
     * Update course progress for user
     */
    public function updateProgress(User $user, Course $course): void
    {
        $enrollment = CourseEnrollment::where('user_id', $user->id)
            ->where('course_id', $course->id)
            ->first();

        if (!$enrollment) {
            return;
        }

        $totalLessons = $course->lessons()->where('is_active', true)->count();
        if ($totalLessons === 0) {
            return;
        }

        $completedLessons = UserLessonProgress::where('user_id', $user->id)
            ->whereHas('lesson', function ($query) use ($course) {
                $query->where('course_id', $course->id)->where('is_active', true);
            })
            ->where('is_completed', true)
            ->count();

        $progressPercentage = ($completedLessons / $totalLessons) * 100;

        $enrollment->updateProgress((int) $progressPercentage);

        // Check if course is completed
        if ($progressPercentage >= 100 && !$enrollment->completed_at) {
            $this->completeCourse($user, $course, $enrollment);
        }
    }

    /**
     * Complete a course
     */
    private function completeCourse(User $user, Course $course, CourseEnrollment $enrollment): void
    {
        $enrollment->markAsCompleted();

        // Award XP for course completion
        $xpReward = $course->xp_reward ?? 100;
        $user->addXp($xpReward);

        // Send completion notification
        $user->notify(new CourseCompleted($course, $xpReward));

        // Award achievement
        app(AchievementService::class)->checkAchievements($user, 'course_completed', [
            'course_id' => $course->id,
            'course_title' => $course->title,
        ]);
    }

    /**
     * Get user's enrolled courses
     */
    public function getUserCourses(User $user): array
    {
        return CourseEnrollment::where('user_id', $user->id)
            ->with(['course.campus'])
            ->orderBy('enrolled_at', 'desc')
            ->get()
            ->map(function ($enrollment) {
                return [
                    'enrollment' => $enrollment,
                    'course' => $enrollment->course,
                    'progress' => $enrollment->progress_percentage,
                    'is_completed' => $enrollment->isCompleted(),
                    'time_spent' => $enrollment->time_spent,
                ];
            })
            ->toArray();
    }

    /**
     * Get recommended courses for user
     */
    public function getRecommendedCourses(User $user, int $limit = 5): array
    {
        // Get user's enrolled course categories
        $enrolledCategories = CourseEnrollment::where('user_id', $user->id)
            ->with('course')
            ->get()
            ->pluck('course.category')
            ->filter()
            ->unique()
            ->toArray();

        // Get courses in similar categories that user hasn't enrolled in
        $query = Course::where('is_published', true)
            ->whereNotIn('id', function ($subQuery) use ($user) {
                $subQuery->select('course_id')
                    ->from('course_enrollments')
                    ->where('user_id', $user->id);
            });

        if (!empty($enrolledCategories)) {
            $query->whereIn('category', $enrolledCategories);
        }

        return $query->orderBy('rating', 'desc')
            ->orderBy('created_at', 'desc')
            ->take($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get popular courses
     */
    public function getPopularCourses(int $limit = 10): array
    {
        return Course::withCount('enrollments')
            ->where('is_published', true)
            ->orderBy('enrollments_count', 'desc')
            ->orderBy('rating', 'desc')
            ->take($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get course statistics
     */
    public function getCourseStats(Course $course): array
    {
        $totalEnrollments = $course->enrollments()->count();
        $completedEnrollments = $course->enrollments()->where('progress_percentage', 100)->count();
        $completionRate = $totalEnrollments > 0 ? ($completedEnrollments / $totalEnrollments) * 100 : 0;

        $averageProgress = $course->enrollments()->avg('progress_percentage') ?? 0;

        return [
            'total_enrollments' => $totalEnrollments,
            'completed_enrollments' => $completedEnrollments,
            'completion_rate' => round($completionRate, 2),
            'average_progress' => round($averageProgress, 2),
            'total_lessons' => $course->lessons()->count(),
            'active_lessons' => $course->lessons()->where('is_active', true)->count(),
            'estimated_duration' => $course->estimated_duration,
            'rating' => $course->rating,
        ];
    }

    /**
     * Search courses
     */
    public function searchCourses(string $query, array $filters = []): array
    {
        $courseQuery = Course::where('is_published', true);

        // Text search
        if (!empty($query)) {
            $courseQuery->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('content', 'like', "%{$query}%");
            });
        }

        // Apply filters
        if (isset($filters['campus_id'])) {
            $courseQuery->where('campus_id', $filters['campus_id']);
        }

        if (isset($filters['category'])) {
            $courseQuery->where('category', $filters['category']);
        }

        if (isset($filters['difficulty_level'])) {
            $courseQuery->where('difficulty_level', $filters['difficulty_level']);
        }

        if (isset($filters['min_rating'])) {
            $courseQuery->where('rating', '>=', $filters['min_rating']);
        }

        // Sorting
        $sortBy = $filters['sort_by'] ?? 'relevance';
        switch ($sortBy) {
            case 'newest':
                $courseQuery->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $courseQuery->orderBy('created_at', 'asc');
                break;
            case 'rating':
                $courseQuery->orderBy('rating', 'desc');
                break;
            case 'popular':
                $courseQuery->withCount('enrollments')->orderBy('enrollments_count', 'desc');
                break;
            case 'duration':
                $courseQuery->orderBy('estimated_duration', 'asc');
                break;
            default: // relevance
                $courseQuery->orderBy('rating', 'desc')->orderBy('created_at', 'desc');
                break;
        }

        return $courseQuery->with(['campus', 'enrollments'])
            ->paginate($filters['per_page'] ?? 12)
            ->toArray();
    }

    /**
     * Get course categories
     */
    public function getCourseCategories(): array
    {
        return Course::where('is_published', true)
            ->whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->filter()
            ->sort()
            ->values()
            ->toArray();
    }

    /**
     * Get next lesson for user in course
     */
    public function getNextLesson(User $user, Course $course): ?Lesson
    {
        // Get user's completed lessons in this course
        $completedLessonIds = UserLessonProgress::where('user_id', $user->id)
            ->whereHas('lesson', function ($query) use ($course) {
                $query->where('course_id', $course->id);
            })
            ->where('is_completed', true)
            ->pluck('lesson_id')
            ->toArray();

        // Get next uncompleted lesson
        return $course->lessons()
            ->where('is_active', true)
            ->whereNotIn('id', $completedLessonIds)
            ->orderBy('order')
            ->first();
    }

    /**
     * Calculate estimated completion time for user
     */
    public function getEstimatedCompletionTime(User $user, Course $course): array
    {
        $enrollment = $course->getUserEnrollment($user);
        
        if (!$enrollment) {
            return [
                'estimated_hours' => $course->estimated_duration ? ceil($course->estimated_duration / 60) : 0,
                'estimated_days' => $course->estimated_duration ? ceil($course->estimated_duration / (60 * 2)) : 0, // Assuming 2 hours per day
            ];
        }

        $remainingProgress = 100 - $enrollment->progress_percentage;
        $totalDuration = $course->estimated_duration ?? 0;
        $remainingDuration = ($remainingProgress / 100) * $totalDuration;

        return [
            'estimated_hours' => ceil($remainingDuration / 60),
            'estimated_days' => ceil($remainingDuration / (60 * 2)), // Assuming 2 hours per day
            'current_progress' => $enrollment->progress_percentage,
        ];
    }
}
