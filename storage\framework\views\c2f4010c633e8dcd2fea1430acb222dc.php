<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo e(config('app.name', 'Laravel')); ?> - Login</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-gray-900 text-white">
    <div class="min-h-screen flex flex-col sm:justify-center items-center pt-6 sm:pt-0">
        <div class="w-full sm:max-w-md mt-6 px-6 py-4 bg-gray-800 shadow-md overflow-hidden sm:rounded-lg">
            <!-- Logo -->
            <div class="flex justify-center mb-8">
                <h1 class="text-2xl font-bold text-gradient">The Real World</h1>
            </div>

            <!-- Session Status -->
            <?php if(session('status')): ?>
                <div class="mb-4 font-medium text-sm text-green-400">
                    <?php echo e(session('status')); ?>

                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo e(route('login')); ?>">
                <?php echo csrf_field(); ?>

                <!-- Email Address -->
                <div>
                    <label for="email" class="block font-medium text-sm text-gray-300">Email</label>
                    <input id="email" class="input-field block mt-1 w-full" type="email" name="email" value="<?php echo e(old('email')); ?>" required autofocus autocomplete="username" />
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-red-400 text-sm mt-1"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Password -->
                <div class="mt-4">
                    <label for="password" class="block font-medium text-sm text-gray-300">Password</label>
                    <input id="password" class="input-field block mt-1 w-full" type="password" name="password" required autocomplete="current-password" />
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-red-400 text-sm mt-1"><?php echo e($message); ?></span>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <!-- Remember Me -->
                <div class="block mt-4">
                    <label for="remember_me" class="inline-flex items-center">
                        <input id="remember_me" type="checkbox" class="rounded border-gray-600 text-red-600 shadow-sm focus:ring-red-500 focus:ring-offset-gray-800" name="remember">
                        <span class="ml-2 text-sm text-gray-300">Remember me</span>
                    </label>
                </div>

                <div class="flex items-center justify-between mt-6">
                    <?php if(Route::has('password.request')): ?>
                        <a class="underline text-sm text-gray-400 hover:text-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-gray-800" href="<?php echo e(route('password.request')); ?>">
                            Forgot your password?
                        </a>
                    <?php endif; ?>

                    <button type="submit" class="btn-primary ml-3">
                        Log in
                    </button>
                </div>

                <div class="mt-6 text-center">
                    <span class="text-gray-400">Don't have an account?</span>
                    <a href="<?php echo e(route('register')); ?>" class="text-red-400 hover:text-red-300 font-medium">
                        Sign up
                    </a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/auth/login.blade.php ENDPATH**/ ?>