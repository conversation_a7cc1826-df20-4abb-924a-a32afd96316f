<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChatRoom extends Model
{
    use HasFactory;

    protected $fillable = [
        'campus_id',
        'name',
        'description',
        'type',
        'is_active',
        'is_moderated',
        'allowed_roles',
        'message_retention_days',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_moderated' => 'boolean',
        'allowed_roles' => 'array',
    ];

    /**
     * Relationships
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    public function messages(): HasMany
    {
        return $this->hasMany(ChatMessage::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePublic($query)
    {
        return $query->where('room_type', 'public');
    }

    public function scopeGeneral($query)
    {
        return $query->where('type', 'general');
    }

    public function scopeAnnouncements($query)
    {
        return $query->where('type', 'announcements');
    }

    /**
     * Helper Methods
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    public function getRecentMessagesCount($hours = 24): int
    {
        return $this->messages()
            ->where('created_at', '>=', now()->subHours($hours))
            ->count();
    }

    public function getActiveUsersCount($minutes = 30): int
    {
        return $this->messages()
            ->where('created_at', '>=', now()->subMinutes($minutes))
            ->distinct('user_id')
            ->count();
    }

    public function getRoomIcon(): string
    {
        $icons = [
            'general' => '💬',
            'announcements' => '📢',
            'help' => '❓',
            'custom' => '⚙️',
        ];

        return $icons[$this->type] ?? '💬';
    }

    public function getRoomColor(): string
    {
        $colors = [
            'general' => '#10B981',
            'announcements' => '#3B82F6',
            'help' => '#F59E0B',
            'custom' => '#8B5CF6',
        ];

        return $colors[$this->type] ?? '#6B7280';
    }

    public function canUserAccess(User $user): bool
    {
        // If no allowed roles specified, everyone can access
        if (!$this->allowed_roles || empty($this->allowed_roles)) {
            return true;
        }

        // Check if user has any of the allowed roles
        return $user->hasAnyRole($this->allowed_roles);
    }
}
