<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use App\Models\User;

class ProfileController extends Controller
{
    /**
     * Display the user's profile
     */
    public function show()
    {
        $user = Auth::user();
        $user->load(['roles', 'userLessonProgress.lesson.course', 'eventRsvps.event', 'badges']);

        // Calculate user statistics
        $stats = [
            'total_xp' => $user->xp,
            'current_level' => $user->level,
            'next_level_xp' => $this->getXpForLevel($user->level + 1),
            'current_level_xp' => $this->getXpForLevel($user->level),
            'progress_to_next_level' => $this->calculateLevelProgress($user),

            'lessons_completed' => $user->userLessonProgress()->where('is_completed', true)->count(),
            'courses_started' => $user->userLessonProgress()
                ->join('lessons', 'user_lesson_progress.lesson_id', '=', 'lessons.id')
                ->distinct('lessons.course_id')
                ->count('lessons.course_id'),
            'courses_completed' => $this->getCompletedCoursesCount($user),

            'events_attended' => $user->eventRsvps()->where('attended', true)->count(),
            'events_rsvped' => $user->eventRsvps()->count(),

            'messages_sent' => $user->chatMessages()->count(),
            'tasks_completed' => $user->userTasks()->count(),
            'badges_earned' => $user->badges()->count(),

            'member_since' => $user->created_at,
            'last_active' => $user->last_login_at ?? $user->updated_at,
        ];

        // Get recent activity
        $recentActivity = $this->getRecentActivity($user);

        // Get earned badges
        $earnedBadges = $user->badges()->orderBy('user_badges.awarded_at', 'desc')->take(6)->get();

        return view('dashboard.profile.show', compact('user', 'stats', 'recentActivity', 'earnedBadges'));
    }

    /**
     * Show the form for editing the profile
     */
    public function edit()
    {
        $user = Auth::user();
        return view('dashboard.profile.edit', compact('user'));
    }

    /**
     * Update the user's profile information
     */
    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'bio' => 'nullable|string|max:500',
            'location' => 'nullable|string|max:100',
            'website' => 'nullable|url|max:255',
            'twitter' => 'nullable|string|max:100',
            'linkedin' => 'nullable|string|max:100',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $updateData = [
            'name' => $request->name,
            'email' => $request->email,
            'bio' => $request->bio,
            'location' => $request->location,
            'website' => $request->website,
            'twitter' => $request->twitter,
            'linkedin' => $request->linkedin,
        ];

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Clear old avatar
            $user->clearMediaCollection('avatar');

            // Add new avatar
            $media = $user->addMedia($request->file('avatar'), 'avatar');
            $updateData['avatar_url'] = $media->getUrl();
        }

        $user->update($updateData);

        return redirect()->route('dashboard.profile.show')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        $user->update([
            'password' => Hash::make($request->password),
        ]);

        return back()->with('success', 'Password updated successfully!');
    }

    /**
     * Delete the user's avatar
     */
    public function deleteAvatar()
    {
        $user = Auth::user();

        // Clear avatar media collection
        $user->clearMediaCollection('avatar');
        $user->update(['avatar_url' => null]);

        return back()->with('success', 'Avatar deleted successfully!');
    }

    /**
     * Calculate XP required for a specific level
     */
    private function getXpForLevel($level)
    {
        return $level * 100; // Simple formula: level * 100
    }

    /**
     * Calculate progress to next level as percentage
     */
    private function calculateLevelProgress($user)
    {
        $currentLevelXp = $this->getXpForLevel($user->level);
        $nextLevelXp = $this->getXpForLevel($user->level + 1);
        $progressXp = $user->xp - $currentLevelXp;
        $requiredXp = $nextLevelXp - $currentLevelXp;

        return $requiredXp > 0 ? round(($progressXp / $requiredXp) * 100, 1) : 0;
    }

    /**
     * Get count of completed courses
     */
    private function getCompletedCoursesCount($user)
    {
        // A course is completed when all its lessons are completed
        $coursesStarted = $user->userLessonProgress()
            ->join('lessons', 'user_lesson_progress.lesson_id', '=', 'lessons.id')
            ->distinct('lessons.course_id')
            ->pluck('lessons.course_id');

        $completedCourses = 0;

        foreach ($coursesStarted as $courseId) {
            $totalLessons = \App\Models\Lesson::where('course_id', $courseId)->count();
            $completedLessons = $user->userLessonProgress()
                ->join('lessons', 'user_lesson_progress.lesson_id', '=', 'lessons.id')
                ->where('lessons.course_id', $courseId)
                ->where('user_lesson_progress.is_completed', true)
                ->count();

            if ($totalLessons > 0 && $completedLessons >= $totalLessons) {
                $completedCourses++;
            }
        }

        return $completedCourses;
    }

    /**
     * Get recent user activity
     */
    private function getRecentActivity($user)
    {
        $activity = collect();

        // Recent lesson completions
        $recentLessons = $user->userLessonProgress()
            ->where('is_completed', true)
            ->with('lesson')
            ->latest('completed_at')
            ->take(5)
            ->get();

        foreach ($recentLessons as $progress) {
            $activity->push([
                'type' => 'lesson_completed',
                'icon' => '📚',
                'title' => 'Completed Lesson',
                'description' => $progress->lesson->title,
                'date' => $progress->completed_at,
                'xp' => 25,
            ]);
        }

        // Recent event RSVPs
        $recentRsvps = $user->eventRsvps()
            ->with('event')
            ->latest('rsvp_at')
            ->take(3)
            ->get();

        foreach ($recentRsvps as $rsvp) {
            $activity->push([
                'type' => 'event_rsvp',
                'icon' => '🎯',
                'title' => 'RSVP\'d to Event',
                'description' => $rsvp->event->title,
                'date' => $rsvp->rsvp_at,
                'xp' => 10,
            ]);
        }

        // Recent badges
        $recentBadges = $user->badges()
            ->orderBy('user_badges.awarded_at', 'desc')
            ->take(3)
            ->get();

        foreach ($recentBadges as $badge) {
            $activity->push([
                'type' => 'badge_earned',
                'icon' => '🏆',
                'title' => 'Earned Badge',
                'description' => $badge->name,
                'date' => $badge->pivot->awarded_at,
                'xp' => $badge->xp_reward,
            ]);
        }

        return $activity->sortByDesc('date')->take(10);
    }
}
