<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Badge;

class BadgeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $badges = [
            [
                'name' => 'First Steps',
                'slug' => 'first-steps',
                'description' => 'Complete your first daily task',
                'icon_url' => '👶',
                'color' => '#10B981',
                'type' => 'achievement',
                'requirements' => ['task_completion' => 1],
                'xp_reward' => 10,
                'is_active' => true,
            ],
            [
                'name' => 'Task Master',
                'slug' => 'task-master',
                'description' => 'Complete 10 daily tasks',
                'icon_url' => '🎯',
                'color' => '#3B82F6',
                'type' => 'milestone',
                'requirements' => ['task_completion' => 10],
                'xp_reward' => 50,
                'is_active' => true,
            ],
            [
                'name' => 'Streak Starter',
                'slug' => 'streak-starter',
                'description' => 'Maintain a 3-day streak',
                'icon_url' => '🔥',
                'color' => '#F59E0B',
                'type' => 'achievement',
                'requirements' => ['streak' => 3],
                'xp_reward' => 25,
                'is_active' => true,
            ],
            [
                'name' => 'Dedicated Learner',
                'slug' => 'dedicated-learner',
                'description' => 'Maintain a 7-day streak',
                'icon_url' => '📚',
                'color' => '#8B5CF6',
                'type' => 'milestone',
                'requirements' => ['streak' => 7],
                'xp_reward' => 100,
                'is_active' => true,
            ],
            [
                'name' => 'Knowledge Seeker',
                'slug' => 'knowledge-seeker',
                'description' => 'Complete 5 lessons',
                'icon_url' => '🎓',
                'color' => '#06B6D4',
                'type' => 'milestone',
                'requirements' => ['lesson_completion' => 5],
                'xp_reward' => 75,
                'is_active' => true,
            ],
            [
                'name' => 'Course Conqueror',
                'slug' => 'course-conqueror',
                'description' => 'Complete your first course',
                'icon_url' => '🏆',
                'color' => '#EF4444',
                'type' => 'achievement',
                'requirements' => ['course_completion' => 1],
                'xp_reward' => 150,
                'is_active' => true,
            ],
            [
                'name' => 'Social Butterfly',
                'slug' => 'social-butterfly',
                'description' => 'Share 5 posts on social media',
                'icon_url' => '🦋',
                'color' => '#EC4899',
                'type' => 'achievement',
                'requirements' => ['social_share' => 5],
                'xp_reward' => 40,
                'is_active' => true,
            ],
            [
                'name' => 'Community Builder',
                'slug' => 'community-builder',
                'description' => 'Refer 3 friends to The Real World',
                'icon_url' => '🤝',
                'color' => '#84CC16',
                'type' => 'special',
                'requirements' => ['referral' => 3],
                'xp_reward' => 200,
                'is_active' => true,
            ],
        ];

        foreach ($badges as $badgeData) {
            Badge::updateOrCreate(
                ['name' => $badgeData['name']],
                $badgeData
            );
        }
    }
}
