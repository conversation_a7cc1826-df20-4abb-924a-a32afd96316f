<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            // Add missing columns that are referenced in the Course model
            if (!Schema::hasColumn('courses', 'slug')) {
                $table->string('slug')->unique()->after('title');
                $table->index('slug');
            }
            
            if (!Schema::hasColumn('courses', 'is_premium')) {
                $table->boolean('is_premium')->default(false)->after('is_featured');
                $table->index('is_premium');
            }
            
            if (!Schema::hasColumn('courses', 'is_published')) {
                $table->boolean('is_published')->default(false)->after('is_premium');
                $table->index('is_published');
            }
            
            if (!Schema::hasColumn('courses', 'estimated_duration')) {
                $table->integer('estimated_duration')->nullable()->after('duration_minutes');
            }
            
            if (!Schema::hasColumn('courses', 'difficulty_level')) {
                $table->string('difficulty_level')->default('beginner')->after('estimated_duration');
                $table->index('difficulty_level');
            }
            
            if (!Schema::hasColumn('courses', 'learning_objectives')) {
                $table->text('learning_objectives')->nullable()->after('description');
            }
            
            if (!Schema::hasColumn('courses', 'xp_reward')) {
                $table->integer('xp_reward')->default(0)->after('learning_objectives');
                $table->index('xp_reward');
            }
            
            // Update existing difficulty column if it exists
            if (Schema::hasColumn('courses', 'difficulty')) {
                $table->dropColumn('difficulty');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('courses', function (Blueprint $table) {
            $table->dropColumn([
                'slug', 
                'is_premium', 
                'is_published', 
                'estimated_duration', 
                'difficulty_level', 
                'learning_objectives',
                'xp_reward'
            ]);
            
            // Restore original difficulty column
            $table->enum('difficulty', ['beginner', 'intermediate', 'advanced'])->default('beginner');
        });
    }
};
