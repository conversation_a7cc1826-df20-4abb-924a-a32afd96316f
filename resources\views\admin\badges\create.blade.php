@extends('layouts.admin')

@section('title', 'Create Badge')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create New Badge</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.badges.index') }}">Badges</a></li>
                    <li class="breadcrumb-item active">Create Badge</li>
                </ol>
            </nav>
        </div>
        <a href="{{ route('admin.badges.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Badges
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Badge Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.badges.store') }}" method="POST">
                        @csrf

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Badge Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                           id="slug" name="slug" value="{{ old('slug') }}" required>
                                    <small class="form-text text-muted">URL-friendly version (auto-generated)</small>
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                            <small class="form-text text-muted">What this badge represents</small>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Badge Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Select badge type</option>
                                        <option value="achievement" {{ old('type') === 'achievement' ? 'selected' : '' }}>Achievement</option>
                                        <option value="milestone" {{ old('type') === 'milestone' ? 'selected' : '' }}>Milestone</option>
                                        <option value="special" {{ old('type') === 'special' ? 'selected' : '' }}>Special</option>
                                        <option value="seasonal" {{ old('type') === 'seasonal' ? 'selected' : '' }}>Seasonal</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">Badge Color</label>
                                    <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', '#6366f1') }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="icon_url" class="form-label">Icon URL</label>
                                    <input type="url" class="form-control @error('icon_url') is-invalid @enderror" 
                                           id="icon_url" name="icon_url" value="{{ old('icon_url') }}" 
                                           placeholder="https://example.com/icon.png">
                                    <small class="form-text text-muted">URL to badge icon image</small>
                                    @error('icon_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="xp_reward" class="form-label">XP Reward</label>
                                    <input type="number" class="form-control @error('xp_reward') is-invalid @enderror" 
                                           id="xp_reward" name="xp_reward" value="{{ old('xp_reward', 0) }}" min="0" max="1000">
                                    <small class="form-text text-muted">XP points awarded when earned</small>
                                    @error('xp_reward')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">Requirements (JSON)</label>
                            <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                      id="requirements" name="requirements" rows="4">{{ old('requirements', '{}') }}</textarea>
                            <small class="form-text text-muted">JSON object defining badge requirements</small>
                            @error('requirements')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                    <small class="form-text text-muted">Display order (lower numbers first)</small>
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_rare" name="is_rare" value="1" 
                                               {{ old('is_rare') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_rare">
                                            <strong>Rare Badge</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Mark as rare/special badge</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    <strong>Active</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Badge can be earned by users</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.badges.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Badge
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Badge Preview -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Preview</h6>
                </div>
                <div class="card-body text-center">
                    <div class="badge-preview mb-3">
                        <div class="badge-icon" style="width: 80px; height: 80px; background-color: #6366f1; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                            <i class="fas fa-award text-white" style="font-size: 2rem;"></i>
                        </div>
                        <h5 class="badge-name">Badge Name</h5>
                        <p class="badge-description text-muted">Badge description will appear here</p>
                        <div class="badge-meta">
                            <span class="badge badge-primary">Achievement</span>
                            <span class="badge badge-success">0 XP</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Badge Guidelines -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Guidelines</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2"><i class="fas fa-check text-success mr-2"></i> Use clear, descriptive names</li>
                        <li class="mb-2"><i class="fas fa-check text-success mr-2"></i> Keep descriptions concise</li>
                        <li class="mb-2"><i class="fas fa-check text-success mr-2"></i> Set appropriate XP rewards</li>
                        <li class="mb-2"><i class="fas fa-check text-success mr-2"></i> Use consistent icon style</li>
                        <li class="mb-0"><i class="fas fa-check text-success mr-2"></i> Test requirements thoroughly</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    
    nameInput.addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/^-+|-+$/g, '');
        slugInput.value = slug;
    });

    // Live preview updates
    const colorInput = document.getElementById('color');
    const badgeIcon = document.querySelector('.badge-icon');
    
    colorInput.addEventListener('input', function() {
        badgeIcon.style.backgroundColor = this.value;
    });

    nameInput.addEventListener('input', function() {
        document.querySelector('.badge-name').textContent = this.value || 'Badge Name';
    });

    document.getElementById('description').addEventListener('input', function() {
        document.querySelector('.badge-description').textContent = this.value || 'Badge description will appear here';
    });

    document.getElementById('type').addEventListener('change', function() {
        document.querySelector('.badge-meta .badge-primary').textContent = this.value || 'Achievement';
    });

    document.getElementById('xp_reward').addEventListener('input', function() {
        document.querySelector('.badge-meta .badge-success').textContent = (this.value || 0) + ' XP';
    });
});
</script>
@endpush
