@extends('layouts.admin')

@section('title', 'System Information - Admin')
@section('page-title', 'System Information')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">System Information</h2>
        <p class="text-gray-400">Server and application configuration details</p>
    </div>

    <!-- System Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">PHP Version</h3>
                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ $systemInfo['php_version'] }}</div>
            <p class="text-sm text-gray-400">Current Version</p>
        </div>

        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Laravel Version</h3>
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ $systemInfo['laravel_version'] }}</div>
            <p class="text-sm text-gray-400">Framework</p>
        </div>

        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Server</h3>
                <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                </svg>
            </div>
            <div class="text-lg font-bold mb-2">{{ $systemInfo['server_software'] }}</div>
            <p class="text-sm text-gray-400">Web Server</p>
        </div>

        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Environment</h3>
                <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ strtoupper(config('app.env')) }}</div>
            <p class="text-sm text-gray-400">App Environment</p>
        </div>
    </div>

    <!-- PHP Configuration -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">PHP Configuration</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Memory Limit</span>
                    <span class="font-mono">{{ $systemInfo['memory_limit'] }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Max Execution Time</span>
                    <span class="font-mono">{{ $systemInfo['max_execution_time'] }}s</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Upload Max Filesize</span>
                    <span class="font-mono">{{ $systemInfo['upload_max_filesize'] }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Post Max Size</span>
                    <span class="font-mono">{{ $systemInfo['post_max_size'] }}</span>
                </div>
            </div>
        </div>

        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Application Configuration</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Debug Mode</span>
                    <span class="px-2 py-1 rounded text-xs {{ config('app.debug') ? 'bg-yellow-600 text-yellow-100' : 'bg-green-600 text-green-100' }}">
                        {{ config('app.debug') ? 'Enabled' : 'Disabled' }}
                    </span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Cache Driver</span>
                    <span class="font-mono">{{ config('cache.default') }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Session Driver</span>
                    <span class="font-mono">{{ config('session.driver') }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Queue Driver</span>
                    <span class="font-mono">{{ config('queue.default') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Information -->
    <div class="card mb-8">
        <h3 class="text-lg font-semibold mb-4">Database Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-400 mb-2">{{ $databaseInfo['connection'] }}</div>
                <p class="text-gray-400">Database Type</p>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-400 mb-2">{{ $databaseInfo['database'] }}</div>
                <p class="text-gray-400">Database Name</p>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-400 mb-2">{{ number_format($databaseInfo['tables']) }}</div>
                <p class="text-gray-400">Total Tables</p>
            </div>
        </div>
    </div>

    <!-- Storage Information -->
    <div class="card">
        <h3 class="text-lg font-semibold mb-4">Storage Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            @foreach($storageInfo as $disk => $info)
            <div class="p-4 bg-gray-700 rounded-lg">
                <h4 class="font-medium mb-3 capitalize">{{ $disk }} Disk</h4>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>Driver:</span>
                        <span class="font-mono">{{ $info['driver'] }}</span>
                    </div>
                    @if(isset($info['path']))
                    <div class="flex justify-between">
                        <span>Path:</span>
                        <span class="font-mono text-xs">{{ $info['path'] }}</span>
                    </div>
                    @endif
                    @if(isset($info['url']))
                    <div class="flex justify-between">
                        <span>URL:</span>
                        <span class="font-mono text-xs">{{ $info['url'] }}</span>
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endsection
