<?php

namespace App\Services;

use App\Models\Event;
use App\Models\EventRsvp;
use App\Models\User;
use App\Notifications\EventReminder;
use Carbon\Carbon;

class EventService
{
    /**
     * Get upcoming events
     */
    public function getUpcomingEvents(int $limit = 10): array
    {
        return Event::where('start_time', '>', now())
            ->where('is_active', true)
            ->orderBy('start_time', 'asc')
            ->take($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get events for a specific date range
     */
    public function getEventsInRange(Carbon $startDate, Carbon $endDate): array
    {
        return Event::whereBetween('start_time', [$startDate, $endDate])
            ->where('is_active', true)
            ->orderBy('start_time', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * Get user's RSVP'd events
     */
    public function getUserEvents(User $user): array
    {
        return Event::whereHas('rsvps', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('is_active', true)
            ->orderBy('start_time', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * RSVP user to an event
     */
    public function rsvpToEvent(User $user, Event $event): bool
    {
        // Check if user already RSVP'd
        $existingRsvp = EventRsvp::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($existingRsvp) {
            return false; // Already RSVP'd
        }

        // Check if event has capacity
        if ($event->max_attendees && $event->rsvps()->count() >= $event->max_attendees) {
            return false; // Event is full
        }

        // Create RSVP
        EventRsvp::create([
            'user_id' => $user->id,
            'event_id' => $event->id,
            'status' => 'attending',
            'rsvp_at' => now(),
        ]);

        return true;
    }

    /**
     * Cancel user's RSVP
     */
    public function cancelRsvp(User $user, Event $event): bool
    {
        $rsvp = EventRsvp::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($rsvp) {
            $rsvp->delete();
            return true;
        }

        return false;
    }

    /**
     * Mark user as attended
     */
    public function markAsAttended(User $user, Event $event): bool
    {
        $rsvp = EventRsvp::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($rsvp) {
            $rsvp->update(['attended' => true]);
            
            // Award achievement for attending event
            app(AchievementService::class)->checkAchievements($user, 'event_attended', [
                'event_id' => $event->id,
                'event_title' => $event->title,
            ]);

            return true;
        }

        return false;
    }

    /**
     * Get event statistics
     */
    public function getEventStats(Event $event): array
    {
        $totalRsvps = $event->rsvps()->count();
        $attendedCount = $event->rsvps()->where('attended', true)->count();
        $attendanceRate = $totalRsvps > 0 ? ($attendedCount / $totalRsvps) * 100 : 0;

        return [
            'total_rsvps' => $totalRsvps,
            'attended_count' => $attendedCount,
            'attendance_rate' => round($attendanceRate, 2),
            'available_spots' => $event->max_attendees ? max(0, $event->max_attendees - $totalRsvps) : null,
            'is_full' => $event->max_attendees && $totalRsvps >= $event->max_attendees,
        ];
    }

    /**
     * Send event reminders
     */
    public function sendEventReminders(): int
    {
        $remindersSent = 0;
        
        // Get events starting in the next 24 hours
        $upcomingEvents = Event::whereBetween('start_time', [
                now()->addHour(),
                now()->addDay()
            ])
            ->where('is_active', true)
            ->get();

        foreach ($upcomingEvents as $event) {
            $rsvps = $event->rsvps()->with('user')->get();
            
            foreach ($rsvps as $rsvp) {
                if ($rsvp->user && $rsvp->user->event_reminders) {
                    $rsvp->user->notify(new EventReminder($event));
                    $remindersSent++;
                }
            }
        }

        return $remindersSent;
    }

    /**
     * Get popular events
     */
    public function getPopularEvents(int $limit = 5): array
    {
        return Event::withCount('rsvps')
            ->where('is_active', true)
            ->orderBy('rsvps_count', 'desc')
            ->take($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get events by category
     */
    public function getEventsByCategory(string $category): array
    {
        return Event::where('category', $category)
            ->where('is_active', true)
            ->orderBy('start_time', 'asc')
            ->get()
            ->toArray();
    }

    /**
     * Check if user can join event
     */
    public function canUserJoinEvent(User $user, Event $event): array
    {
        $result = [
            'can_join' => false,
            'reason' => null,
        ];

        // Check if event is active
        if (!$event->is_active) {
            $result['reason'] = 'Event is not active';
            return $result;
        }

        // Check if event has already started
        if ($event->start_time <= now()) {
            $result['reason'] = 'Event has already started';
            return $result;
        }

        // Check if user already RSVP'd
        if ($event->rsvps()->where('user_id', $user->id)->exists()) {
            $result['reason'] = 'Already RSVP\'d to this event';
            return $result;
        }

        // Check capacity
        if ($event->max_attendees && $event->rsvps()->count() >= $event->max_attendees) {
            $result['reason'] = 'Event is full';
            return $result;
        }

        // Check subscription requirements
        if ($event->required_subscription && !$user->subscribed()) {
            $result['reason'] = 'Subscription required';
            return $result;
        }

        $result['can_join'] = true;
        return $result;
    }

    /**
     * Get event calendar data
     */
    public function getCalendarData(Carbon $month): array
    {
        $startOfMonth = $month->copy()->startOfMonth();
        $endOfMonth = $month->copy()->endOfMonth();

        $events = Event::whereBetween('start_time', [$startOfMonth, $endOfMonth])
            ->where('is_active', true)
            ->get();

        $calendar = [];
        foreach ($events as $event) {
            $date = $event->start_time->format('Y-m-d');
            if (!isset($calendar[$date])) {
                $calendar[$date] = [];
            }
            $calendar[$date][] = [
                'id' => $event->id,
                'title' => $event->title,
                'time' => $event->start_time->format('H:i'),
                'category' => $event->category,
                'rsvp_count' => $event->rsvps()->count(),
            ];
        }

        return $calendar;
    }
}
