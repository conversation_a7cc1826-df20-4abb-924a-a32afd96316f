<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('performance_metrics', function (Blueprint $table) {
            $table->id();
            $table->timestamp('timestamp');
            $table->json('metrics');
            $table->decimal('response_time_avg', 8, 2)->nullable();
            $table->decimal('memory_usage_pct', 5, 2)->nullable();
            $table->decimal('cache_hit_rate', 5, 2)->nullable();
            $table->integer('database_connections')->nullable();
            $table->integer('queue_size')->nullable();
            $table->integer('alerts_count')->default(0);
            $table->timestamps();
            
            // Indexes for performance
            $table->index('timestamp');
            $table->index('created_at');
            $table->index(['timestamp', 'alerts_count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('performance_metrics');
    }
};
