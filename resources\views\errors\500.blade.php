@extends('layouts.app')

@section('title', 'Server Error - The Real World')

@section('content')
<div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-2xl mx-auto text-center">
        <!-- Error Animation -->
        <div class="mb-8">
            <div class="relative">
                <div class="text-9xl font-bold text-red-600 opacity-20 select-none">500</div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-32 h-32 bg-gradient-to-br from-red-600 to-red-800 rounded-full flex items-center justify-center animate-pulse">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div class="mb-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Server Error</h1>
            <p class="text-xl text-gray-400 mb-6">
                Something went wrong on our end. We're working to fix it.
            </p>
            <p class="text-gray-500">
                Even the best systems have hiccups. We'll be back up and running shortly.
            </p>
        </div>

        <!-- Status Information -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <div class="flex items-center justify-center mb-4">
                <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse mr-3"></div>
                <span class="text-yellow-400 font-semibold">System Status: Under Maintenance</span>
            </div>
            <p class="text-gray-400 text-sm">
                Our technical team has been notified and is working to resolve this issue.
                <br>
                Expected resolution time: <span class="text-white font-semibold">15-30 minutes</span>
            </p>
        </div>

        <!-- What You Can Do -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold mb-4">What you can do:</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 bg-gray-800 rounded-lg">
                    <div class="flex items-center mb-2">
                        <svg class="w-5 h-5 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        <span class="font-semibold">Refresh the page</span>
                    </div>
                    <p class="text-sm text-gray-400">The issue might be temporary</p>
                </div>

                <div class="p-4 bg-gray-800 rounded-lg">
                    <div class="flex items-center mb-2">
                        <svg class="w-5 h-5 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-semibold">Wait a few minutes</span>
                    </div>
                    <p class="text-sm text-gray-400">We're working on a fix</p>
                </div>

                <div class="p-4 bg-gray-800 rounded-lg">
                    <div class="flex items-center mb-2">
                        <svg class="w-5 h-5 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-semibold">Contact support</span>
                    </div>
                    <p class="text-sm text-gray-400">If the problem persists</p>
                </div>

                <div class="p-4 bg-gray-800 rounded-lg">
                    <div class="flex items-center mb-2">
                        <svg class="w-5 h-5 text-primary-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                        </svg>
                        <span class="font-semibold">Check our status</span>
                    </div>
                    <p class="text-sm text-gray-400">Visit our status page</p>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button onclick="window.location.reload()" 
                    class="btn-primary inline-flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh Page
            </button>

            <a href="{{ route('home') }}" 
               class="btn-secondary inline-flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                Go Home
            </a>

            <a href="mailto:<EMAIL>" 
               class="btn-secondary inline-flex items-center justify-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Contact Support
            </a>
        </div>

        <!-- Error ID for Support -->
        <div class="p-4 bg-gray-800/50 rounded-lg mb-8">
            <p class="text-sm text-gray-400 mb-2">Error Reference ID:</p>
            <code class="text-primary-400 font-mono text-sm">{{ Str::random(8) }}-{{ now()->format('YmdHis') }}</code>
            <p class="text-xs text-gray-500 mt-2">
                Please include this ID when contacting support
            </p>
        </div>

        <!-- Social Links -->
        <div class="mb-8">
            <p class="text-sm text-gray-400 mb-4">Stay updated on our status:</p>
            <div class="flex justify-center space-x-4">
                <a href="https://twitter.com/therealworldhq" 
                   class="text-gray-400 hover:text-primary-400 transition-colors">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                </a>
                <a href="https://discord.gg/therealworld" 
                   class="text-gray-400 hover:text-primary-400 transition-colors">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.317 4.37a19.791 19.791 0 00-4.885-1.515.074.074 0 00-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 00-5.487 0 12.64 12.64 0 00-.617-1.25.077.077 0 00-.079-.037A19.736 19.736 0 003.677 4.37a.07.07 0 00-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 00.031.057 19.9 19.9 0 005.993 3.03.078.078 0 00.084-.028c.462-.63.874-1.295 1.226-1.994a.076.076 0 00-.041-.106 13.107 13.107 0 01-1.872-.892.077.077 0 01-.008-.128 10.2 10.2 0 00.372-.292.074.074 0 01.077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 01.078.01c.12.098.246.198.373.292a.077.077 0 01-.006.127 12.299 12.299 0 01-1.873.892.077.077 0 00-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 00.084.028 19.839 19.839 0 006.002-3.03.077.077 0 00.032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 00-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                    </svg>
                </a>
                <a href="https://instagram.com/therealworldhq" 
                   class="text-gray-400 hover:text-primary-400 transition-colors">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.875-.385-.875-.875s.385-.875.875-.875.875.385.875.875-.385.875-.875.875zm-4.262 1.781c-1.297 0-2.345 1.048-2.345 2.345s1.048 2.345 2.345 2.345 2.345-1.048 2.345-2.345-1.048-2.345-2.345-2.345z"/>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Motivational Message -->
        <div class="p-4 border-l-4 border-red-600 bg-gray-800/50">
            <blockquote class="text-lg italic text-gray-300">
                "The way to get started is to quit talking and begin doing."
            </blockquote>
            <cite class="block mt-2 text-sm text-gray-400">- Walt Disney</cite>
            <p class="text-sm text-gray-500 mt-2">
                We'll be back online soon. Thank you for your patience.
            </p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh every 30 seconds
    let refreshInterval;
    let countdown = 30;
    
    function startAutoRefresh() {
        refreshInterval = setInterval(() => {
            countdown--;
            if (countdown <= 0) {
                window.location.reload();
            }
        }, 1000);
    }
    
    // Start auto-refresh after 2 minutes
    setTimeout(startAutoRefresh, 120000);
    
    // Track 500 errors for monitoring
    if (typeof gtag !== 'undefined') {
        gtag('event', 'server_error', {
            'error_code': '500',
            'page_location': window.location.href,
            'timestamp': new Date().toISOString()
        });
    }
});
</script>
@endsection
