@extends('layouts.admin')

@section('title', 'Admin Dashboard')
@section('page-title', 'Dashboard Overview')

@section('content')
<div class="p-6">
    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Users -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Total Users</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['total_users']) }}</p>
                    <p class="text-sm text-green-400">+{{ $stats['new_users_today'] }} today</p>
                </div>
            </div>
        </div>

        <!-- Active Courses -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Published Courses</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['active_courses']) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($stats['total_courses']) }} total</p>
                </div>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Upcoming Events</p>
                    <p class="text-2xl font-bold text-white">{{ $stats['upcoming_events'] }}</p>
                    <p class="text-sm text-gray-400">of {{ $stats['total_events'] }} total</p>
                </div>
            </div>
        </div>

        <!-- Active Campuses -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-4">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-400">Active Campuses</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['active_campuses']) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($stats['total_campuses']) }} total</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- User Growth Chart -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold mb-4">User Growth (Last 30 Days)</h3>
            <div class="h-64 flex items-end justify-between space-x-1">
                @php
                    $maxUsers = collect($userGrowthData)->max('users') ?: 1;
                @endphp
                @foreach($userGrowthData as $data)
                <div class="flex flex-col items-center">
                    <div class="bg-red-600 rounded-t"
                         style="height: {{ max(4, ($data['users'] / $maxUsers) * 200) }}px; width: 8px;">
                    </div>
                    <span class="text-xs text-gray-400 mt-1 transform -rotate-45 origin-top-left">{{ $data['date'] }}</span>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Subscription Distribution -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold mb-4">Subscription Distribution</h3>
            <div class="space-y-4">
                @php
                    $total = array_sum($subscriptionStats);
                    $colors = [
                        'free' => 'bg-gray-500',
                        'premium' => 'bg-red-500',
                    ];
                @endphp
                
                @foreach($subscriptionStats as $plan => $count)
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-4 h-4 {{ $colors[$plan] ?? 'bg-gray-500' }} rounded mr-3"></div>
                        <span class="text-sm font-medium capitalize">{{ $plan }}</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-400 mr-2">{{ number_format($count) }}</span>
                        <span class="text-xs text-gray-500">
                            ({{ $total > 0 ? round(($count / $total) * 100, 1) : 0 }}%)
                        </span>
                    </div>
                </div>
                <div class="w-full bg-gray-700 rounded-full h-2">
                    <div class="{{ $colors[$plan] ?? 'bg-gray-500' }} h-2 rounded-full"
                         style="width: {{ $total > 0 ? ($count / $total) * 100 : 0 }}%"></div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Users -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">Recent Users</h3>
                <a href="{{ route('admin.users.index') }}" class="text-red-400 hover:text-red-300 text-sm">
                    View All
                </a>
            </div>
            <div class="space-y-3">
                @foreach($recentUsers as $user)
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <img src="{{ $user->avatar_url ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=7F9CF5&background=EBF4FF' }}"
                             alt="{{ $user->name }}"
                             class="w-8 h-8 rounded-full mr-3">
                        <div>
                            <p class="text-sm font-medium">{{ $user->name }}</p>
                            <p class="text-xs text-gray-400">{{ $user->email }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        @php
                            $hasActiveSubscription = $user->subscriptions()->where('stripe_status', 'active')->exists();
                        @endphp
                        <span class="text-xs px-2 py-1 rounded-full {{ $hasActiveSubscription ? 'bg-red-600' : 'bg-gray-600' }} text-white">
                            {{ $hasActiveSubscription ? 'Premium' : 'Free' }}
                        </span>
                        <p class="text-xs text-gray-400 mt-1">{{ $user->created_at->diffForHumans() }}</p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Top Performers -->
        <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <h3 class="text-lg font-semibold mb-4">Top Performers (XP)</h3>
            <div class="space-y-3">
                @foreach($topPerformers as $index => $performer)
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center mr-3 {{ $index < 3 ? 'bg-yellow-600' : 'bg-gray-600' }}">
                            @if($index === 0)
                                🥇
                            @elseif($index === 1)
                                🥈
                            @elseif($index === 2)
                                🥉
                            @else
                                {{ $index + 1 }}
                            @endif
                        </div>
                        <div>
                            <p class="text-sm font-medium">{{ $performer->name }}</p>
                            <p class="text-xs text-gray-400">Level {{ $performer->level ?? 1 }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-bold text-yellow-400">{{ number_format($performer->xp ?? 0) }} XP</p>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-8">
        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <a href="{{ route('admin.users.create') }}"
               class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span class="text-sm font-medium">Add User</span>
            </a>

            <a href="{{ route('admin.analytics.index') }}"
               class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="text-sm font-medium">Analytics</span>
            </a>

            <a href="{{ route('admin.system') }}" 
               class="bg-yellow-600 hover:bg-yellow-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm font-medium">System</span>
            </a>

            <a href="{{ route('dashboard.index') }}"
               class="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                <span class="text-sm font-medium">Dashboard</span>
            </a>

            <a href="#" 
               class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <span class="text-sm font-medium">Alerts</span>
            </a>

            <a href="#" 
               class="bg-gray-600 hover:bg-gray-700 text-white p-4 rounded-lg text-center transition-colors">
                <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <span class="text-sm font-medium">Reports</span>
            </a>
        </div>
    </div>
</div>
@endsection
