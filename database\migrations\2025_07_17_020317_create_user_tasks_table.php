<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('task_id')->constrained()->onDelete('cascade');
            $table->json('completion_data')->nullable(); // Task-specific completion data
            $table->integer('xp_earned')->default(0);
            $table->timestamp('completed_at');
            $table->date('completion_date'); // For daily task tracking
            $table->timestamps();

            $table->index(['user_id', 'completion_date']);
            $table->index(['task_id', 'completion_date']);
            $table->unique(['user_id', 'task_id', 'completion_date']); // Prevent duplicate daily completions
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_tasks');
    }
};
