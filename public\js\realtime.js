/**
 * Real-time functionality for The Real World platform
 * Handles WebSocket connections, notifications, and live updates
 */

class RealTimeManager {
    constructor() {
        this.echo = null;
        this.userId = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        
        this.init();
    }

    /**
     * Initialize the real-time connection
     */
    init() {
        // Get user ID from meta tag
        const userIdMeta = document.querySelector('meta[name="user-id"]');
        if (userIdMeta) {
            this.userId = userIdMeta.getAttribute('content');
        }

        // Initialize Echo if Laravel Echo is available
        if (typeof Echo !== 'undefined' && this.userId) {
            this.setupEcho();
        } else {
            console.warn('Laravel Echo not available or user not authenticated');
            this.fallbackToPolling();
        }
    }

    /**
     * Setup Laravel Echo WebSocket connection
     */
    setupEcho() {
        try {
            this.echo = Echo;
            this.setupPrivateChannels();
            this.setupPresenceChannels();
            this.setupPublicChannels();
            this.isConnected = true;
            this.reconnectAttempts = 0;
            
            console.log('Real-time connection established');
            this.showConnectionStatus('connected');
        } catch (error) {
            console.error('Failed to setup Echo:', error);
            this.fallbackToPolling();
        }
    }

    /**
     * Setup private channels for user-specific events
     */
    setupPrivateChannels() {
        if (!this.echo || !this.userId) return;

        // Listen for notifications
        this.echo.private(`user.${this.userId}`)
            .listen('.notification.sent', (e) => {
                this.handleNotification(e);
            });
    }

    /**
     * Setup presence channels for chat rooms
     */
    setupPresenceChannels() {
        if (!this.echo) return;

        // Get current chat room if on chat page
        const chatRoomId = this.getCurrentChatRoom();
        if (chatRoomId) {
            this.joinChatRoom(chatRoomId);
        }
    }

    /**
     * Setup public channels for global events
     */
    setupPublicChannels() {
        if (!this.echo) return;

        // Listen for user status changes
        this.echo.channel('user.status')
            .listen('.user.status.changed', (e) => {
                this.handleUserStatusChange(e);
            });
    }

    /**
     * Join a chat room presence channel
     */
    joinChatRoom(roomId) {
        if (!this.echo) return;

        this.echo.join(`chat.room.${roomId}`)
            .here((users) => {
                this.updateOnlineUsers(users);
            })
            .joining((user) => {
                this.userJoinedChat(user);
            })
            .leaving((user) => {
                this.userLeftChat(user);
            })
            .listen('.message.sent', (e) => {
                this.handleChatMessage(e);
            });
    }

    /**
     * Handle incoming notifications
     */
    handleNotification(notification) {
        // Show browser notification if permission granted
        if (Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                tag: notification.id
            });
        }

        // Show in-app notification
        this.showInAppNotification(notification);

        // Update notification count
        this.updateNotificationCount();
    }

    /**
     * Handle chat messages
     */
    handleChatMessage(message) {
        // Don't show own messages
        if (message.user.id == this.userId) return;

        // Add message to chat interface
        this.addMessageToChat(message);

        // Show notification if mentioned
        if (this.isUserMentioned(message.message)) {
            this.showChatMention(message);
        }

        // Play sound notification
        this.playChatSound();
    }

    /**
     * Handle user status changes
     */
    handleUserStatusChange(data) {
        this.updateUserStatus(data.user_id, data.is_online);
    }

    /**
     * Show in-app notification
     */
    showInAppNotification(notification) {
        const container = document.getElementById('notification-container') || this.createNotificationContainer();
        
        const notificationEl = document.createElement('div');
        notificationEl.className = 'notification-toast bg-gray-800 border border-gray-700 rounded-lg p-4 mb-3 shadow-lg transform transition-all duration-300 translate-x-full';
        notificationEl.innerHTML = `
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-3 flex-1">
                    <h4 class="text-sm font-medium text-white">${notification.title}</h4>
                    <p class="text-sm text-gray-300 mt-1">${notification.message}</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-400 hover:text-white">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;

        container.appendChild(notificationEl);

        // Animate in
        setTimeout(() => {
            notificationEl.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notificationEl.classList.add('translate-x-full');
            setTimeout(() => {
                if (notificationEl.parentElement) {
                    notificationEl.remove();
                }
            }, 300);
        }, 5000);
    }

    /**
     * Create notification container if it doesn't exist
     */
    createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-4 right-4 z-50 max-w-sm w-full';
        document.body.appendChild(container);
        return container;
    }

    /**
     * Update notification count in UI
     */
    updateNotificationCount() {
        fetch('/api/notifications/count', {
            headers: {
                'Authorization': `Bearer ${this.getAuthToken()}`,
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            const countElements = document.querySelectorAll('.notification-count');
            countElements.forEach(el => {
                el.textContent = data.count;
                el.style.display = data.count > 0 ? 'block' : 'none';
            });
        })
        .catch(error => console.error('Failed to update notification count:', error));
    }

    /**
     * Add message to chat interface
     */
    addMessageToChat(message) {
        const chatContainer = document.getElementById('chat-messages');
        if (!chatContainer) return;

        const messageEl = document.createElement('div');
        messageEl.className = 'chat-message mb-4 animate-fade-in';
        messageEl.innerHTML = `
            <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-sm font-medium text-white">
                        ${message.user.name.charAt(0).toUpperCase()}
                    </div>
                </div>
                <div class="flex-1">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm font-medium text-white">${message.user.name}</span>
                        <span class="text-xs text-gray-400">${this.formatTime(message.created_at)}</span>
                    </div>
                    <p class="text-sm text-gray-300 mt-1">${this.formatMessage(message.message)}</p>
                </div>
            </div>
        `;

        chatContainer.appendChild(messageEl);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    /**
     * Check if user is mentioned in message
     */
    isUserMentioned(message) {
        const userName = document.querySelector('meta[name="user-name"]')?.getAttribute('content');
        return userName && message.toLowerCase().includes(`@${userName.toLowerCase()}`);
    }

    /**
     * Play chat sound notification
     */
    playChatSound() {
        const audio = new Audio('/sounds/notification.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
            // Ignore errors if sound can't be played
        });
    }

    /**
     * Show connection status
     */
    showConnectionStatus(status) {
        const statusEl = document.getElementById('connection-status');
        if (statusEl) {
            statusEl.className = `connection-status ${status}`;
            statusEl.textContent = status === 'connected' ? 'Connected' : 'Disconnected';
        }
    }

    /**
     * Fallback to polling for updates
     */
    fallbackToPolling() {
        console.log('Falling back to polling for updates');
        
        // Poll for notifications every 30 seconds
        setInterval(() => {
            this.updateNotificationCount();
        }, 30000);
    }

    /**
     * Get current chat room ID
     */
    getCurrentChatRoom() {
        const roomMeta = document.querySelector('meta[name="chat-room-id"]');
        return roomMeta ? roomMeta.getAttribute('content') : null;
    }

    /**
     * Get auth token for API requests
     */
    getAuthToken() {
        return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    }

    /**
     * Format message text (basic markdown support)
     */
    formatMessage(message) {
        return message
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/@(\w+)/g, '<span class="text-primary-400">@$1</span>');
    }

    /**
     * Format timestamp
     */
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    /**
     * Update online users list
     */
    updateOnlineUsers(users) {
        const onlineList = document.getElementById('online-users');
        if (!onlineList) return;

        onlineList.innerHTML = users.map(user => `
            <div class="flex items-center space-x-2 p-2">
                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                <span class="text-sm text-white">${user.name}</span>
            </div>
        `).join('');
    }

    /**
     * Handle user joining chat
     */
    userJoinedChat(user) {
        console.log(`${user.name} joined the chat`);
    }

    /**
     * Handle user leaving chat
     */
    userLeftChat(user) {
        console.log(`${user.name} left the chat`);
    }

    /**
     * Update user online status
     */
    updateUserStatus(userId, isOnline) {
        const userElements = document.querySelectorAll(`[data-user-id="${userId}"]`);
        userElements.forEach(el => {
            const statusIndicator = el.querySelector('.status-indicator');
            if (statusIndicator) {
                statusIndicator.className = `status-indicator w-3 h-3 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`;
            }
        });
    }
}

// Initialize real-time manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.realTimeManager = new RealTimeManager();
});

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
