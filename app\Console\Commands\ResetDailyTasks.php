<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Task;
use App\Models\UserTask;
use Carbon\Carbon;

class ResetDailyTasks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tasks:reset-daily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset daily tasks for all users (run at midnight)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting daily task reset...');

        // Get all active daily tasks
        $dailyTasks = Task::where('is_daily', true)
            ->where('is_active', true)
            ->get();

        $this->info("Found {$dailyTasks->count()} daily tasks to reset.");

        // Get all active users
        $users = User::where('is_active', true)->get();
        $this->info("Processing {$users->count()} active users.");

        $streaksBroken = 0;
        $streaksMaintained = 0;

        foreach ($users as $user) {
            // Check if user completed all tasks yesterday
            $yesterday = Carbon::yesterday();
            $completedYesterday = UserTask::where('user_id', $user->id)
                ->whereDate('completed_at', $yesterday)
                ->count();

            // If user didn't complete all tasks yesterday, their streak is broken
            if ($completedYesterday < $dailyTasks->count()) {
                $streaksBroken++;
                // You could add streak tracking logic here
            } else {
                $streaksMaintained++;
            }
        }

        $this->info("Daily task reset completed!");
        $this->info("Streaks maintained: {$streaksMaintained}");
        $this->info("Streaks broken: {$streaksBroken}");

        // Optional: Clean up old task completions (keep last 30 days)
        $cutoffDate = Carbon::now()->subDays(30);
        $deletedRecords = UserTask::where('completed_at', '<', $cutoffDate)->delete();

        if ($deletedRecords > 0) {
            $this->info("Cleaned up {$deletedRecords} old task completion records.");
        }

        return Command::SUCCESS;
    }
}
