<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ChatRoom;
use App\Models\Campus;

class ChatRoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get campuses
        $campuses = Campus::all();

        // Get the first campus for general rooms (or create a general campus)
        $generalCampus = $campuses->first();
        if (!$generalCampus) {
            $generalCampus = Campus::create([
                'name' => 'General',
                'slug' => 'general',
                'description' => 'General community discussions',
                'color' => '#6B7280',
                'is_active' => true,
                'sort_order' => 0,
            ]);
        }

        $rooms = [
            // General public rooms
            [
                'campus_id' => $generalCampus->id,
                'name' => 'General Chat',
                'description' => 'General discussion for all members',
                'type' => 'general',
                'is_active' => true,
                'is_moderated' => false,
                'allowed_roles' => null,
                'message_retention_days' => 30,
            ],
            [
                'campus_id' => $generalCampus->id,
                'name' => 'Announcements',
                'description' => 'Important announcements from The Real World team',
                'type' => 'announcements',
                'is_active' => true,
                'is_moderated' => true,
                'allowed_roles' => ['admin', 'mentor'],
                'message_retention_days' => 90,
            ],
            [
                'campus_id' => $generalCampus->id,
                'name' => 'Success Stories',
                'description' => 'Share your wins and celebrate achievements',
                'type' => 'general',
                'is_active' => true,
                'is_moderated' => false,
                'allowed_roles' => null,
                'message_retention_days' => 60,
            ],
            [
                'campus_id' => $generalCampus->id,
                'name' => 'Help & Support',
                'description' => 'Get help with technical issues and questions',
                'type' => 'help',
                'is_active' => true,
                'is_moderated' => true,
                'allowed_roles' => null,
                'message_retention_days' => 14,
            ],
        ];

        // Create campus-specific rooms
        foreach ($campuses as $campus) {
            $rooms[] = [
                'campus_id' => $campus->id,
                'name' => $campus->name . ' Campus',
                'description' => 'Discussion for ' . $campus->name . ' students',
                'type' => 'general',
                'is_active' => true,
                'is_moderated' => false,
                'allowed_roles' => null,
                'message_retention_days' => 30,
            ];

            // Add a help room for each campus
            if (in_array($campus->slug, ['ecommerce', 'freelancing', 'cryptocurrency'])) {
                $rooms[] = [
                    'campus_id' => $campus->id,
                    'name' => $campus->name . ' Help',
                    'description' => 'Get help with ' . $campus->name . ' related questions',
                    'type' => 'help',
                    'is_active' => true,
                    'is_moderated' => true,
                    'allowed_roles' => null,
                    'message_retention_days' => 14,
                ];
            }
        }

        foreach ($rooms as $roomData) {
            ChatRoom::updateOrCreate(
                [
                    'name' => $roomData['name'],
                    'campus_id' => $roomData['campus_id']
                ],
                $roomData
            );
        }
    }
}
