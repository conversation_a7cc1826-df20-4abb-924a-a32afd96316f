@extends('layouts.dashboard')

@section('title', 'Advanced Search - The Real World')
@section('page-title', 'Advanced Search')

@section('content')
<div class="p-6" x-data="advancedSearch()">
    <!-- Header -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">Advanced Search</h2>
        <p class="text-gray-400">Find exactly what you're looking for with powerful filters</p>
    </div>

    <!-- Search Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8 border-b border-gray-700">
            <button @click="activeTab = 'courses'" 
                    :class="{ 'border-primary-500 text-primary-400': activeTab === 'courses', 'border-transparent text-gray-400': activeTab !== 'courses' }"
                    class="pb-2 border-b-2 font-medium transition-colors">
                📚 Courses
            </button>
            <button @click="activeTab = 'events'" 
                    :class="{ 'border-primary-500 text-primary-400': activeTab === 'events', 'border-transparent text-gray-400': activeTab !== 'events' }"
                    class="pb-2 border-b-2 font-medium transition-colors">
                📅 Events
            </button>
            <button @click="activeTab = 'members'" 
                    :class="{ 'border-primary-500 text-primary-400': activeTab === 'members', 'border-transparent text-gray-400': activeTab !== 'members' }"
                    class="pb-2 border-b-2 font-medium transition-colors">
                👤 Members
            </button>
        </nav>
    </div>

    <!-- Course Search -->
    <div x-show="activeTab === 'courses'" class="space-y-6">
        <!-- Course Filters -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Course Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Search Term</label>
                    <input type="text" 
                           x-model="courseFilters.query"
                           placeholder="Course title, description..."
                           class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Campus</label>
                    <select x-model="courseFilters.campus" class="input-field">
                        <option value="">All Campuses</option>
                        <option value="1">Freelancing</option>
                        <option value="2">E-commerce</option>
                        <option value="3">Copywriting</option>
                        <option value="4">Crypto</option>
                        <option value="5">Stocks</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Difficulty</label>
                    <select x-model="courseFilters.difficulty" class="input-field">
                        <option value="">Any Level</option>
                        <option value="beginner">Beginner</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Duration</label>
                    <select x-model="courseFilters.duration" class="input-field">
                        <option value="">Any Duration</option>
                        <option value="short">Short (< 30 min)</option>
                        <option value="medium">Medium (30-120 min)</option>
                        <option value="long">Long (> 120 min)</option>
                    </select>
                </div>
            </div>
            
            <div class="flex items-center justify-between mt-6">
                <div class="flex items-center space-x-4">
                    <label class="block text-sm font-medium text-gray-300">Sort by:</label>
                    <select x-model="courseFilters.sort" class="input-field w-auto">
                        <option value="relevance">Relevance</option>
                        <option value="newest">Newest</option>
                        <option value="popular">Most Popular</option>
                        <option value="rating">Highest Rated</option>
                        <option value="duration">Duration</option>
                    </select>
                </div>
                
                <div class="flex space-x-3">
                    <button @click="clearCourseFilters()" class="btn-secondary">Clear</button>
                    <button @click="searchCourses()" class="btn-primary">Search Courses</button>
                </div>
            </div>
        </div>

        <!-- Course Results -->
        <div x-show="courseResults.length > 0 || courseSearched" class="space-y-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">
                    Course Results 
                    <span x-show="coursePagination.total" class="text-gray-400 font-normal">
                        (<span x-text="coursePagination.total"></span> found)
                    </span>
                </h3>
            </div>
            
            <template x-if="courseLoading">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                    <p class="text-gray-400">Searching courses...</p>
                </div>
            </template>
            
            <template x-if="!courseLoading && courseResults.length === 0 && courseSearched">
                <div class="card text-center py-12">
                    <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">No courses found</h4>
                    <p class="text-gray-400">Try adjusting your search criteria</p>
                </div>
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template x-for="course in courseResults" :key="course.id">
                    <div class="card hover:bg-gray-700 transition-colors">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-2xl">📚</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-sm mb-1" x-text="course.title"></h4>
                                <p class="text-xs text-gray-400 mb-2" x-text="course.description"></p>
                                <div class="flex items-center justify-between text-xs text-gray-500">
                                    <span x-text="course.campus"></span>
                                    <span x-text="course.difficulty_level"></span>
                                </div>
                                <div class="mt-3">
                                    <a :href="course.url" class="btn-primary text-xs">View Course</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Event Search -->
    <div x-show="activeTab === 'events'" class="space-y-6">
        <!-- Event Filters -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Event Filters</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Search Term</label>
                    <input type="text" 
                           x-model="eventFilters.query"
                           placeholder="Event title, description..."
                           class="input-field">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Event Type</label>
                    <select x-model="eventFilters.type" class="input-field">
                        <option value="">All Types</option>
                        <option value="webinar">Webinar</option>
                        <option value="workshop">Workshop</option>
                        <option value="live_session">Live Session</option>
                        <option value="networking">Networking</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Date Range</label>
                    <select x-model="eventFilters.date" class="input-field">
                        <option value="">All Dates</option>
                        <option value="today">Today</option>
                        <option value="tomorrow">Tomorrow</option>
                        <option value="this_week">This Week</option>
                        <option value="next_week">Next Week</option>
                        <option value="this_month">This Month</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Sort by</label>
                    <select x-model="eventFilters.sort" class="input-field">
                        <option value="date">Date</option>
                        <option value="popular">Most Popular</option>
                        <option value="newest">Newest</option>
                    </select>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button @click="clearEventFilters()" class="btn-secondary">Clear</button>
                <button @click="searchEvents()" class="btn-primary">Search Events</button>
            </div>
        </div>

        <!-- Event Results -->
        <div x-show="eventResults.length > 0 || eventSearched" class="space-y-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">
                    Event Results 
                    <span x-show="eventPagination.total" class="text-gray-400 font-normal">
                        (<span x-text="eventPagination.total"></span> found)
                    </span>
                </h3>
            </div>
            
            <template x-if="eventLoading">
                <div class="text-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
                    <p class="text-gray-400">Searching events...</p>
                </div>
            </template>
            
            <template x-if="!eventLoading && eventResults.length === 0 && eventSearched">
                <div class="card text-center py-12">
                    <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold mb-2">No events found</h4>
                    <p class="text-gray-400">Try adjusting your search criteria</p>
                </div>
            </template>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <template x-for="event in eventResults" :key="event.id">
                    <div class="card hover:bg-gray-700 transition-colors">
                        <div class="flex items-start space-x-4">
                            <div class="w-16 h-16 bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <span class="text-2xl">📅</span>
                            </div>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-semibold text-sm mb-1" x-text="event.title"></h4>
                                <p class="text-xs text-gray-400 mb-2" x-text="event.description"></p>
                                <div class="flex items-center justify-between text-xs text-gray-500 mb-3">
                                    <span x-text="event.type"></span>
                                    <span x-text="new Date(event.start_time).toLocaleDateString()"></span>
                                </div>
                                <a :href="event.url" class="btn-primary text-xs">View Event</a>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>

    <!-- Members Tab -->
    <div x-show="activeTab === 'members'" class="card text-center py-12">
        <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">👤</span>
        </div>
        <h3 class="text-lg font-semibold mb-2">Member Search</h3>
        <p class="text-gray-400 mb-4">Member search functionality coming soon</p>
        <p class="text-sm text-gray-500">Connect with other entrepreneurs in the community chat</p>
    </div>
</div>

<script>
function advancedSearch() {
    return {
        activeTab: 'courses',
        
        // Course search
        courseFilters: {
            query: '',
            campus: '',
            difficulty: '',
            duration: '',
            sort: 'relevance'
        },
        courseResults: [],
        coursePagination: {},
        courseLoading: false,
        courseSearched: false,
        
        // Event search
        eventFilters: {
            query: '',
            type: '',
            date: '',
            sort: 'date'
        },
        eventResults: [],
        eventPagination: {},
        eventLoading: false,
        eventSearched: false,

        async searchCourses() {
            this.courseLoading = true;
            this.courseSearched = true;
            
            const params = new URLSearchParams(this.courseFilters);
            
            try {
                const response = await fetch(`{{ route('search.courses') }}?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    this.courseResults = data.courses;
                    this.coursePagination = data.pagination;
                }
            } catch (error) {
                console.error('Course search error:', error);
            } finally {
                this.courseLoading = false;
            }
        },

        async searchEvents() {
            this.eventLoading = true;
            this.eventSearched = true;
            
            const params = new URLSearchParams(this.eventFilters);
            
            try {
                const response = await fetch(`{{ route('search.events') }}?${params}`);
                const data = await response.json();
                
                if (data.success) {
                    this.eventResults = data.events;
                    this.eventPagination = data.pagination;
                }
            } catch (error) {
                console.error('Event search error:', error);
            } finally {
                this.eventLoading = false;
            }
        },

        clearCourseFilters() {
            this.courseFilters = {
                query: '',
                campus: '',
                difficulty: '',
                duration: '',
                sort: 'relevance'
            };
            this.courseResults = [];
            this.courseSearched = false;
        },

        clearEventFilters() {
            this.eventFilters = {
                query: '',
                type: '',
                date: '',
                sort: 'date'
            };
            this.eventResults = [];
            this.eventSearched = false;
        }
    }
}
</script>
@endsection
