<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdminMediaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $files = collect(Storage::disk('public')->allFiles())
            ->map(function ($file) {
                return [
                    'name' => basename($file),
                    'path' => $file,
                    'url' => Storage::url($file),
                    'size' => Storage::size('public/' . $file),
                    'modified' => Storage::lastModified('public/' . $file),
                    'type' => $this->getFileType($file),
                ];
            })
            ->sortByDesc('modified')
            ->values();

        return view('admin.media.index', compact('files'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.media.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'files.*' => 'required|file|max:10240', // 10MB max
        ]);

        $uploadedFiles = [];

        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                $path = $file->store('uploads', 'public');
                $uploadedFiles[] = [
                    'name' => $file->getClientOriginalName(),
                    'path' => $path,
                    'url' => Storage::url($path),
                ];
            }
        }

        return redirect()->route('admin.media.index')
            ->with('success', count($uploadedFiles) . ' file(s) uploaded successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show($file)
    {
        $filePath = base64_decode($file);
        
        if (!Storage::disk('public')->exists($filePath)) {
            abort(404);
        }

        $fileInfo = [
            'name' => basename($filePath),
            'path' => $filePath,
            'url' => Storage::url($filePath),
            'size' => Storage::size('public/' . $filePath),
            'modified' => Storage::lastModified('public/' . $filePath),
            'type' => $this->getFileType($filePath),
        ];

        return view('admin.media.show', compact('fileInfo'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($file)
    {
        $filePath = base64_decode($file);
        
        if (Storage::disk('public')->exists($filePath)) {
            Storage::disk('public')->delete($filePath);
            
            return redirect()->route('admin.media.index')
                ->with('success', 'File deleted successfully.');
        }

        return redirect()->route('admin.media.index')
            ->with('error', 'File not found.');
    }

    /**
     * Get file type based on extension
     */
    private function getFileType($file)
    {
        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
        $videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv'];
        $audioTypes = ['mp3', 'wav', 'ogg', 'aac'];
        $documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];

        if (in_array($extension, $imageTypes)) {
            return 'image';
        } elseif (in_array($extension, $videoTypes)) {
            return 'video';
        } elseif (in_array($extension, $audioTypes)) {
            return 'audio';
        } elseif (in_array($extension, $documentTypes)) {
            return 'document';
        }

        return 'other';
    }
}
