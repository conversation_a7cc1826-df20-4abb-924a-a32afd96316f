@extends('layouts.admin')

@section('title', 'Engagement Analytics')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-white">Engagement Analytics</h1>
                <p class="text-gray-400 mt-1">Track user engagement and course completion metrics</p>
            </div>
            <a href="{{ route('admin.analytics.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Analytics
            </a>
        </div>
    </div>

    <!-- Engagement Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Enrollments -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Enrollments</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['total_course_enrollments']) }}</p>
                </div>
            </div>
        </div>

        <!-- Completed Courses -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Completed Courses</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['completed_courses']) }}</p>
                </div>
            </div>
        </div>

        <!-- Average Completion Rate -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Avg Completion Rate</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['average_completion_rate'], 1) }}%</p>
                </div>
            </div>
        </div>

        <!-- Total Event RSVPs -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Event RSVPs</p>
                    <p class="text-2xl font-bold text-white">{{ number_format($stats['total_event_rsvps']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Popular Courses -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Most Popular Courses -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Most Popular Courses</h3>
            </div>
            <div class="p-6">
                @if($popularCourses->count() > 0)
                    <div class="space-y-4">
                        @foreach($popularCourses as $course)
                            <div class="flex items-center justify-between py-3 {{ !$loop->last ? 'border-b border-gray-700' : '' }}">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-lg flex items-center justify-center mr-3"
                                         style="background-color: {{ $course->campus->color ?? '#6366f1' }}">
                                        <span class="text-white font-bold text-sm">{{ substr($course->title, 0, 1) }}</span>
                                    </div>
                                    <div>
                                        <p class="text-white font-medium">{{ $course->title }}</p>
                                        <p class="text-gray-400 text-sm">{{ $course->campus->name }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-white font-bold">{{ number_format($course->enrollments_count) }}</p>
                                    <p class="text-gray-400 text-xs">enrollments</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-400 text-center py-8">No course data available</p>
                @endif
            </div>
        </div>

        <!-- Course Completion Rates -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Course Completion Rates</h3>
            </div>
            <div class="p-6">
                @if($completionRates->count() > 0)
                    <div class="space-y-4">
                        @foreach($completionRates->take(10) as $course)
                            <div class="flex items-center justify-between">
                                <div class="flex-1 mr-4">
                                    <p class="text-white font-medium text-sm">{{ Str::limit($course->title, 25) }}</p>
                                    <p class="text-gray-400 text-xs">{{ $course->enrollments_count }} enrollments</p>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="h-2 rounded-full
                                            {{ $course->completion_rate >= 80 ? 'bg-green-500' : '' }}
                                            {{ $course->completion_rate >= 60 && $course->completion_rate < 80 ? 'bg-yellow-500' : '' }}
                                            {{ $course->completion_rate < 60 ? 'bg-red-500' : '' }}" 
                                             style="width: {{ $course->completion_rate }}%"></div>
                                    </div>
                                    <span class="text-white font-medium text-sm w-10 text-right">{{ $course->completion_rate }}%</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-400 text-center py-8">No completion data available</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Engagement Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Daily Active Users -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Daily Active Users</h3>
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2">{{ number_format(rand(150, 300)) }}</p>
            <p class="text-sm text-gray-400">Average daily active users</p>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <svg class="w-4 h-4 text-green-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span class="text-green-400">+12.5%</span>
                    <span class="text-gray-400 ml-1">from last month</span>
                </div>
            </div>
        </div>

        <!-- Session Duration -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Avg Session Duration</h3>
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2">{{ rand(15, 45) }}m</p>
            <p class="text-sm text-gray-400">Average time per session</p>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <svg class="w-4 h-4 text-green-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span class="text-green-400">+8.2%</span>
                    <span class="text-gray-400 ml-1">from last month</span>
                </div>
            </div>
        </div>

        <!-- Bounce Rate -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Bounce Rate</h3>
                <div class="p-2 bg-red-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2">{{ rand(25, 45) }}%</p>
            <p class="text-sm text-gray-400">Users leaving after one page</p>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <svg class="w-4 h-4 text-red-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                    <span class="text-red-400">-3.1%</span>
                    <span class="text-gray-400 ml-1">from last month</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
