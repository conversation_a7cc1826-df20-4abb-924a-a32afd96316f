<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\EventRsvp;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class EventController extends Controller
{
    /**
     * Display upcoming and past events
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $filter = $request->get('filter', 'upcoming'); // upcoming, past, my_events

        $query = Event::with(['campus', 'rsvps']);

        // Apply filters
        switch ($filter) {
            case 'past':
                $query->where('status', 'completed')
                      ->orderBy('start_datetime', 'desc');
                break;
            case 'my_events':
                $query->whereHas('rsvps', function($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->orderBy('start_datetime', 'asc');
                break;
            default: // upcoming
                $query->whereIn('status', ['scheduled', 'live'])
                      ->where('start_datetime', '>=', now())
                      ->orderBy('start_datetime', 'asc');
        }

        $events = $query->paginate(12);

        // Add RSVP status and countdown for each event
        foreach ($events as $event) {
            $event->user_rsvp = $event->rsvps->where('user_id', $user->id)->first();
            $event->is_user_registered = (bool) $event->user_rsvp;
            $event->rsvp_count = $event->rsvps->count();
            $event->countdown = $event->getCountdownData();
            $event->can_join = $event->canUserJoin($user);
        }

        return view('dashboard.events.index', compact('events', 'filter', 'user'));
    }

    /**
     * Show a specific event
     */
    public function show(Event $event)
    {
        $user = Auth::user();

        // Check if user can access this event
        if (!$event->canUserAccess($user)) {
            return redirect()->route('subscription.plans')
                ->with('error', 'You need to upgrade your plan to access this event.');
        }

        // Load relationships
        $event->load(['campus', 'rsvps.user']);

        // Add user-specific data
        $event->user_rsvp = $event->rsvps->where('user_id', $user->id)->first();
        $event->is_user_registered = (bool) $event->user_rsvp;
        $event->rsvp_count = $event->rsvps->count();
        $event->countdown = $event->getCountdownData();
        $event->can_join = $event->canUserJoin($user);

        // Get recent attendees (for social proof)
        $recentAttendees = $event->rsvps()
            ->with('user')
            ->latest()
            ->take(12)
            ->get();

        return view('dashboard.events.show', compact('event', 'recentAttendees', 'user'));
    }

    /**
     * RSVP to an event
     */
    public function rsvp(Request $request, Event $event)
    {
        $user = Auth::user();

        // Check if user can access this event
        if (!$event->canUserAccess($user)) {
            return response()->json([
                'success' => false,
                'message' => 'You need to upgrade your plan to access this event.'
            ], 403);
        }

        // Check if event is still open for RSVP
        if (!$event->isRsvpOpen()) {
            return response()->json([
                'success' => false,
                'message' => 'RSVP is no longer available for this event.'
            ]);
        }

        // Check if user already RSVP'd
        $existingRsvp = EventRsvp::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if ($existingRsvp) {
            return response()->json([
                'success' => false,
                'message' => 'You have already RSVP\'d to this event.'
            ]);
        }

        // Create RSVP
        EventRsvp::create([
            'user_id' => $user->id,
            'event_id' => $event->id,
            'rsvp_status' => 'attending',
            'rsvp_at' => now(),
        ]);

        // Award XP for RSVP
        $user->addXp(10);

        return response()->json([
            'success' => true,
            'message' => 'Successfully RSVP\'d to the event! +10 XP',
            'rsvp_count' => $event->rsvps()->count() + 1,
        ]);
    }

    /**
     * Cancel RSVP to an event
     */
    public function cancelRsvp(Request $request, Event $event)
    {
        $user = Auth::user();

        $rsvp = EventRsvp::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->first();

        if (!$rsvp) {
            return response()->json([
                'success' => false,
                'message' => 'You have not RSVP\'d to this event.'
            ]);
        }

        $rsvp->delete();

        return response()->json([
            'success' => true,
            'message' => 'RSVP cancelled successfully.',
            'rsvp_count' => $event->rsvps()->count() - 1,
        ]);
    }

    /**
     * Join live event (when it's starting)
     */
    public function join(Event $event)
    {
        $user = Auth::user();

        // Check if user can join
        if (!$event->canUserJoin($user)) {
            return redirect()->back()
                ->with('error', 'You cannot join this event at this time.');
        }

        // Update RSVP status to attended
        EventRsvp::where('user_id', $user->id)
            ->where('event_id', $event->id)
            ->update(['rsvp_status' => 'attended']);

        // Award XP for attending
        $user->addXp(25);

        // Redirect to live stream or meeting room
        if ($event->meeting_url) {
            return redirect($event->meeting_url);
        }

        return view('dashboard.events.live', compact('event', 'user'));
    }
}
