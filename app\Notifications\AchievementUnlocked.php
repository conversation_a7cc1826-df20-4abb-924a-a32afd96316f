<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\UserAchievement;

class AchievementUnlocked extends Notification implements ShouldQueue
{
    use Queueable;

    protected $achievement;

    /**
     * Create a new notification instance.
     */
    public function __construct(UserAchievement $achievement)
    {
        $this->achievement = $achievement;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('🏆 Achievement Unlocked!')
            ->greeting('Congratulations!')
            ->line("You've unlocked the \"{$this->achievement->achievement_name}\" achievement!")
            ->line($this->achievement->description)
            ->line("You've earned {$this->achievement->xp_reward} XP for this achievement.")
            ->action('View Your Achievements', route('dashboard.badges.index'))
            ->line('Keep up the great work and continue your journey to success!');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Achievement Unlocked!',
            'message' => "You've earned the \"{$this->achievement->achievement_name}\" achievement and gained {$this->achievement->xp_reward} XP!",
            'type' => 'achievement',
            'icon' => $this->achievement->icon ?? '🏆',
            'action_url' => route('dashboard.badges.index'),
            'action_text' => 'View Achievements',
            'achievement_id' => $this->achievement->id,
            'xp_reward' => $this->achievement->xp_reward,
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return $this->toDatabase($notifiable);
    }
}
