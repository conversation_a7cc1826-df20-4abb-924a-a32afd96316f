<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Media;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class MediaController extends Controller
{
    /**
     * Upload a file
     */
    public function upload(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
            'collection' => 'nullable|string|max:255',
            'model_type' => 'nullable|string|max:255',
            'model_id' => 'nullable|integer',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $file = $request->file('file');
            $disk = config('filesystems.default', 'public');

            // Validate file type
            if (!$this->isAllowedFileType($file)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File type not allowed.',
                ], 422);
            }

            // Generate directory and filename
            $directory = $this->generateDirectory($request->collection);
            $filename = $this->generateUniqueFilename($file);
            $path = $file->storeAs($directory, $filename, $disk);

            // Create media record
            $media = Media::create([
                'name' => $file->getClientOriginalName(),
                'file_name' => $filename,
                'mime_type' => $file->getMimeType(),
                'path' => $path,
                'disk' => $disk,
                'size' => $file->getSize(),
                'metadata' => $this->extractMetadata($file),
                'collection_name' => $request->collection,
                'model_type' => $request->model_type,
                'model_id' => $request->model_id,
                'user_id' => auth()->id(),
                'alt_text' => $request->alt_text,
                'description' => $request->description,
                'is_public' => $request->boolean('is_public', true),
                'uploaded_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'media' => [
                    'id' => $media->id,
                    'name' => $media->name,
                    'url' => $media->getUrl(),
                    'type' => $media->getType(),
                    'size' => $media->getHumanReadableSize(),
                    'mime_type' => $media->mime_type,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get media by ID
     */
    public function show(Media $media): JsonResponse
    {
        // Check if user can access this media
        if (!$media->is_public && $media->user_id !== auth()->id()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'media' => [
                'id' => $media->id,
                'name' => $media->name,
                'file_name' => $media->file_name,
                'url' => $media->getUrl(),
                'type' => $media->getType(),
                'size' => $media->getHumanReadableSize(),
                'mime_type' => $media->mime_type,
                'alt_text' => $media->alt_text,
                'description' => $media->description,
                'uploaded_at' => $media->uploaded_at->format('Y-m-d H:i:s'),
                'metadata' => $media->metadata,
            ],
        ]);
    }

    /**
     * Update media
     */
    public function update(Request $request, Media $media): JsonResponse
    {
        // Check if user can update this media
        if ($media->user_id !== auth()->id() && !auth()->user()->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'alt_text' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $media->update($request->only(['name', 'alt_text', 'description', 'is_public']));

        return response()->json([
            'success' => true,
            'media' => [
                'id' => $media->id,
                'name' => $media->name,
                'alt_text' => $media->alt_text,
                'description' => $media->description,
                'is_public' => $media->is_public,
            ],
        ]);
    }

    /**
     * Delete media
     */
    public function destroy(Media $media): JsonResponse
    {
        // Check if user can delete this media
        if ($media->user_id !== auth()->id() && !auth()->user()->hasRole('admin')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.',
            ], 403);
        }

        try {
            // Delete file from storage
            $media->deleteFile();

            // Delete database record
            $media->delete();

            return response()->json([
                'success' => true,
                'message' => 'Media deleted successfully.',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Delete failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * List media for user
     */
    public function index(Request $request): JsonResponse
    {
        $query = Media::where('user_id', auth()->id());

        // Filter by collection
        if ($request->filled('collection')) {
            $query->where('collection_name', $request->collection);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('mime_type', 'like', $request->type . '/%');
        }

        // Search by name
        if ($request->filled('search')) {
            $query->where('name', 'like', '%' . $request->search . '%');
        }

        $media = $query->orderBy('uploaded_at', 'desc')
                      ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'media' => $media->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->name,
                    'url' => $item->getUrl(),
                    'type' => $item->getType(),
                    'size' => $item->getHumanReadableSize(),
                    'mime_type' => $item->mime_type,
                    'uploaded_at' => $item->uploaded_at->format('Y-m-d H:i:s'),
                ];
            }),
            'pagination' => [
                'current_page' => $media->currentPage(),
                'last_page' => $media->lastPage(),
                'per_page' => $media->perPage(),
                'total' => $media->total(),
            ],
        ]);
    }

    /**
     * Check if file type is allowed
     */
    private function isAllowedFileType($file): bool
    {
        $allowedTypes = [
            // Images
            'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
            // Videos
            'video/mp4', 'video/mpeg', 'video/quicktime', 'video/webm',
            // Audio
            'audio/mpeg', 'audio/wav', 'audio/ogg',
            // Documents
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv',
        ];

        return in_array($file->getMimeType(), $allowedTypes);
    }

    /**
     * Generate directory for file storage
     */
    private function generateDirectory(string $collection = null): string
    {
        $directory = 'media/' . date('Y/m');

        if ($collection) {
            $directory .= '/' . $collection;
        }

        return $directory;
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename($file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = Str::slug($name);

        return $name . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    /**
     * Extract metadata from file
     */
    private function extractMetadata($file): array
    {
        $metadata = [];

        if ($file->getMimeType() && str_starts_with($file->getMimeType(), 'image/')) {
            try {
                $imageInfo = getimagesize($file->getPathname());
                if ($imageInfo) {
                    $metadata['width'] = $imageInfo[0];
                    $metadata['height'] = $imageInfo[1];
                    $metadata['aspect_ratio'] = round($imageInfo[0] / $imageInfo[1], 2);
                }
            } catch (\Exception $e) {
                // Ignore errors
            }
        }

        return $metadata;
    }
}
