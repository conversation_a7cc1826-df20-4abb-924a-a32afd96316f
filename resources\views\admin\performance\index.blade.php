@extends('layouts.admin')

@section('title', 'Performance Dashboard - Admin')
@section('page-title', 'Performance Dashboard')

@section('content')
<div class="p-6" x-data="performanceDashboard()">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold mb-2">Performance Dashboard</h2>
            <p class="text-gray-400">Monitor and optimize application performance</p>
        </div>
        <div class="flex space-x-3">
            <button @click="optimizePerformance()" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Optimize Now
            </button>
            <button @click="refreshData()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Cache Status -->
        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Cache Status</h3>
                <div class="w-3 h-3 rounded-full" :class="cacheStatus ? 'bg-green-500' : 'bg-red-500'"></div>
            </div>
            <div class="text-2xl font-bold mb-2" x-text="cacheDriver"></div>
            <p class="text-sm text-gray-400">Cache Driver</p>
        </div>

        <!-- Database Performance -->
        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Database</h3>
                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2" x-text="dbTableCount"></div>
            <p class="text-sm text-gray-400">Tables Analyzed</p>
        </div>

        <!-- Memory Usage -->
        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Memory</h3>
                <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2" x-text="memoryLimit"></div>
            <p class="text-sm text-gray-400">PHP Memory Limit</p>
        </div>

        <!-- System Status -->
        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">System</h3>
                <div class="w-3 h-3 rounded-full" :class="systemStatus ? 'bg-green-500' : 'bg-yellow-500'"></div>
            </div>
            <div class="text-2xl font-bold mb-2" x-text="phpVersion"></div>
            <p class="text-sm text-gray-400">PHP Version</p>
        </div>
    </div>

    <!-- Cache Management -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Cache Management</h3>
            
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <h4 class="font-medium">Application Cache</h4>
                        <p class="text-sm text-gray-400">Clear application data cache</p>
                    </div>
                    <button @click="clearCache('application')" class="btn-secondary text-sm">Clear</button>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <h4 class="font-medium">Configuration Cache</h4>
                        <p class="text-sm text-gray-400">Clear config cache</p>
                    </div>
                    <button @click="clearCache('config')" class="btn-secondary text-sm">Clear</button>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <h4 class="font-medium">Route Cache</h4>
                        <p class="text-sm text-gray-400">Clear route cache</p>
                    </div>
                    <button @click="clearCache('route')" class="btn-secondary text-sm">Clear</button>
                </div>
                
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <div>
                        <h4 class="font-medium">View Cache</h4>
                        <p class="text-sm text-gray-400">Clear compiled views</p>
                    </div>
                    <button @click="clearCache('view')" class="btn-secondary text-sm">Clear</button>
                </div>
            </div>
            
            <div class="mt-6 flex space-x-3">
                <button @click="clearCache('all')" class="btn-secondary flex-1">Clear All Caches</button>
                <button @click="warmupCache()" class="btn-primary flex-1">Warm Up Caches</button>
            </div>
        </div>

        <!-- Database Optimization -->
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Database Optimization</h3>
            
            <div class="space-y-4">
                <div class="p-3 bg-gray-700 rounded-lg">
                    <h4 class="font-medium mb-2">Table Analysis</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-400">Total Tables:</span>
                            <span class="font-medium ml-2" x-text="dbStats.tableCount || 'Loading...'"></span>
                        </div>
                        <div>
                            <span class="text-gray-400">Total Size:</span>
                            <span class="font-medium ml-2" x-text="dbStats.totalSize || 'Loading...'"></span>
                        </div>
                    </div>
                </div>
                
                <div class="p-3 bg-gray-700 rounded-lg">
                    <h4 class="font-medium mb-2">Index Analysis</h4>
                    <div class="text-sm">
                        <span class="text-gray-400">Missing Indexes:</span>
                        <span class="font-medium ml-2 text-yellow-400" x-text="dbStats.missingIndexes || '0'"></span>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 flex space-x-3">
                <button @click="analyzeDatabase()" class="btn-secondary flex-1">Analyze</button>
                <button @click="optimizeDatabase()" class="btn-primary flex-1">Optimize</button>
            </div>
        </div>
    </div>

    <!-- Performance Recommendations -->
    <div class="card">
        <h3 class="text-lg font-semibold mb-4">Performance Recommendations</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
                <h4 class="font-medium mb-3 text-primary-400">Caching</h4>
                <ul class="space-y-2 text-sm text-gray-400">
                    <li>• Use Redis for production</li>
                    <li>• Enable OPcache</li>
                    <li>• Implement CDN</li>
                    <li>• Browser caching headers</li>
                </ul>
            </div>
            
            <div>
                <h4 class="font-medium mb-3 text-blue-400">Database</h4>
                <ul class="space-y-2 text-sm text-gray-400">
                    <li>• Add missing indexes</li>
                    <li>• Connection pooling</li>
                    <li>• Query optimization</li>
                    <li>• Regular maintenance</li>
                </ul>
            </div>
            
            <div>
                <h4 class="font-medium mb-3 text-green-400">Application</h4>
                <ul class="space-y-2 text-sm text-gray-400">
                    <li>• Queue workers</li>
                    <li>• Asset optimization</li>
                    <li>• Gzip compression</li>
                    <li>• Lazy loading</li>
                </ul>
            </div>
            
            <div>
                <h4 class="font-medium mb-3 text-purple-400">Monitoring</h4>
                <ul class="space-y-2 text-sm text-gray-400">
                    <li>• APM tools</li>
                    <li>• Slow query monitoring</li>
                    <li>• Cache hit rates</li>
                    <li>• Resource monitoring</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div x-show="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-800 rounded-lg p-6 max-w-sm w-full mx-4">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
                <span x-text="loadingMessage">Processing...</span>
            </div>
        </div>
    </div>
</div>

<script>
function performanceDashboard() {
    return {
        loading: false,
        loadingMessage: 'Processing...',
        cacheStatus: true,
        cacheDriver: '{{ $cacheStats["cache_driver"] ?? "file" }}',
        systemStatus: true,
        phpVersion: 'Loading...',
        memoryLimit: 'Loading...',
        dbTableCount: 'Loading...',
        dbStats: {
            tableCount: 0,
            totalSize: '0 MB',
            missingIndexes: 0,
        },

        init() {
            this.loadSystemInfo();
            this.loadDatabaseStats();
        },

        async loadSystemInfo() {
            try {
                const response = await fetch('/admin/performance/system-info');
                const data = await response.json();
                
                if (data.success) {
                    this.phpVersion = data.info.php_version;
                    this.memoryLimit = data.info.memory_limit;
                    this.cacheDriver = data.info.cache_driver;
                    this.systemStatus = data.info.opcache_enabled;
                }
            } catch (error) {
                console.error('Error loading system info:', error);
            }
        },

        async loadDatabaseStats() {
            try {
                const response = await fetch('/admin/performance/database-analysis');
                const data = await response.json();
                
                if (data.success) {
                    const analysis = data.analysis;
                    this.dbTableCount = analysis.table_analysis?.length || 0;
                    this.dbStats.tableCount = analysis.table_analysis?.length || 0;
                    this.dbStats.missingIndexes = analysis.recommendations?.find(r => r.type === 'missing_indexes')?.details?.length || 0;
                    
                    // Calculate total size
                    if (analysis.table_analysis) {
                        const totalSize = analysis.table_analysis.reduce((sum, table) => sum + (table.total_size_mb || 0), 0);
                        this.dbStats.totalSize = totalSize.toFixed(2) + ' MB';
                    }
                }
            } catch (error) {
                console.error('Error loading database stats:', error);
            }
        },

        async clearCache(type) {
            this.loading = true;
            this.loadingMessage = `Clearing ${type} cache...`;
            
            try {
                const response = await fetch('/admin/performance/clear-cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    },
                    body: JSON.stringify({ type })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showNotification(data.message, 'success');
                } else {
                    this.showNotification(data.message, 'error');
                }
            } catch (error) {
                this.showNotification('Failed to clear cache', 'error');
            } finally {
                this.loading = false;
            }
        },

        async warmupCache() {
            this.loading = true;
            this.loadingMessage = 'Warming up caches...';
            
            try {
                const response = await fetch('/admin/performance/warmup-cache', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showNotification(data.message, 'success');
                } else {
                    this.showNotification(data.message, 'error');
                }
            } catch (error) {
                this.showNotification('Failed to warm up caches', 'error');
            } finally {
                this.loading = false;
            }
        },

        async optimizePerformance() {
            this.loading = true;
            this.loadingMessage = 'Optimizing performance...';
            
            try {
                const response = await fetch('/admin/performance/optimize', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showNotification(data.message, 'success');
                    this.refreshData();
                } else {
                    this.showNotification(data.message, 'error');
                }
            } catch (error) {
                this.showNotification('Optimization failed', 'error');
            } finally {
                this.loading = false;
            }
        },

        async analyzeDatabase() {
            this.loading = true;
            this.loadingMessage = 'Analyzing database...';
            
            try {
                await this.loadDatabaseStats();
                this.showNotification('Database analysis completed', 'success');
            } catch (error) {
                this.showNotification('Database analysis failed', 'error');
            } finally {
                this.loading = false;
            }
        },

        async optimizeDatabase() {
            this.loading = true;
            this.loadingMessage = 'Optimizing database...';
            
            try {
                const response = await fetch('/admin/performance/optimize-database', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    this.showNotification(data.message, 'success');
                    this.loadDatabaseStats();
                } else {
                    this.showNotification(data.message, 'error');
                }
            } catch (error) {
                this.showNotification('Database optimization failed', 'error');
            } finally {
                this.loading = false;
            }
        },

        refreshData() {
            this.loadSystemInfo();
            this.loadDatabaseStats();
        },

        showNotification(message, type) {
            // Simple notification - in production you'd use a proper notification system
            alert(message);
        }
    }
}
</script>
@endsection
