<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Task;
use App\Models\Campus;
use App\Models\UserTask;
use Illuminate\Http\Request;

class AdminTaskController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Task::with(['campus', 'userTasks']);

        // Filter by campus if provided
        if ($request->has('campus') && $request->campus) {
            $query->where('campus_id', $request->campus);
        }

        // Filter by status
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by type
        if ($request->has('type') && $request->type) {
            $query->where('task_type', $request->type);
        }

        $tasks = $query->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // Add completion statistics for each task
        foreach ($tasks as $task) {
            $task->total_completions = $task->userTasks()->count();
            $task->completions_today = $task->userTasks()
                ->whereDate('completed_at', today())
                ->count();
            $task->unique_users = $task->userTasks()
                ->distinct('user_id')
                ->count('user_id');
        }

        $campuses = Campus::active()->orderBy('name')->get();
        $taskTypes = Task::distinct('task_type')->pluck('task_type');

        return view('admin.tasks.index', compact('tasks', 'campuses', 'taskTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $campuses = Campus::active()->orderBy('name')->get();
        return view('admin.tasks.create', compact('campuses'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'campus_id' => 'required|exists:campuses,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructions' => 'nullable|string',
            'task_type' => 'required|string|max:50',
            'xp_reward' => 'required|integer|min:1|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_daily' => 'boolean',
            'difficulty' => 'required|in:easy,medium,hard',
        ]);

        Task::create($request->all());

        return redirect()->route('admin.tasks.index')
            ->with('success', 'Task created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Task $task)
    {
        $task->load(['campus', 'userTasks.user']);

        // Get completion statistics
        $stats = [
            'total_completions' => $task->userTasks()->count(),
            'unique_users' => $task->userTasks()->distinct('user_id')->count('user_id'),
            'completions_today' => $task->userTasks()->whereDate('completed_at', today())->count(),
            'completions_this_week' => $task->userTasks()->where('completed_at', '>=', now()->startOfWeek())->count(),
            'completions_this_month' => $task->userTasks()->where('completed_at', '>=', now()->startOfMonth())->count(),
            'average_xp_per_completion' => $task->xp_reward,
            'total_xp_awarded' => $task->userTasks()->sum('xp_earned'),
        ];

        // Get recent completions
        $recentCompletions = $task->userTasks()
            ->with('user')
            ->latest('completed_at')
            ->take(10)
            ->get();

        return view('admin.tasks.show', compact('task', 'stats', 'recentCompletions'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Task $task)
    {
        $campuses = Campus::active()->orderBy('name')->get();
        return view('admin.tasks.edit', compact('task', 'campuses'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Task $task)
    {
        $request->validate([
            'campus_id' => 'required|exists:campuses,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'instructions' => 'nullable|string',
            'task_type' => 'required|string|max:50',
            'xp_reward' => 'required|integer|min:1|max:1000',
            'sort_order' => 'nullable|integer|min:0',
            'is_active' => 'boolean',
            'is_daily' => 'boolean',
            'difficulty' => 'required|in:easy,medium,hard',
        ]);

        $task->update($request->all());

        return redirect()->route('admin.tasks.index')
            ->with('success', 'Task updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Task $task)
    {
        // Check if task has any completions
        if ($task->userTasks()->count() > 0) {
            return redirect()->route('admin.tasks.index')
                ->with('error', 'Cannot delete task that has user completions. Deactivate it instead.');
        }

        $task->delete();

        return redirect()->route('admin.tasks.index')
            ->with('success', 'Task deleted successfully.');
    }

    /**
     * Toggle task active status
     */
    public function toggleStatus(Task $task)
    {
        $task->update(['is_active' => !$task->is_active]);

        $status = $task->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
            ->with('success', "Task {$status} successfully.");
    }
}
