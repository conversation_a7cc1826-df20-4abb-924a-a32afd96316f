<?php $__env->startSection('title', 'Leaderboard - The Real World'); ?>
<?php $__env->startSection('page-title', 'Leaderboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Period Selector -->
    <div class="card mb-8">
        <div class="flex items-center justify-between">
            <h2 class="text-2xl font-bold">Community Leaderboard</h2>
            <div class="flex space-x-2">
                <a href="<?php echo e(route('dashboard.leaderboard.index', ['period' => 'daily'])); ?>"
                   class="btn-<?php echo e($period === 'daily' ? 'primary' : 'secondary'); ?> px-4 py-2">
                    Daily
                </a>
                <a href="<?php echo e(route('dashboard.leaderboard.index', ['period' => 'weekly'])); ?>"
                   class="btn-<?php echo e($period === 'weekly' ? 'primary' : 'secondary'); ?> px-4 py-2">
                    Weekly
                </a>
                <a href="<?php echo e(route('dashboard.leaderboard.index', ['period' => 'monthly'])); ?>"
                   class="btn-<?php echo e($period === 'monthly' ? 'primary' : 'secondary'); ?> px-4 py-2">
                    Monthly
                </a>
                <a href="<?php echo e(route('dashboard.leaderboard.index', ['period' => 'all_time'])); ?>"
                   class="btn-<?php echo e($period === 'all_time' ? 'primary' : 'secondary'); ?> px-4 py-2">
                    All Time
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Leaderboard -->
        <div class="lg:col-span-2">
            <div class="card">
                <h3 class="text-xl font-semibold mb-6">
                    Top Performers 
                    <span class="text-gray-400 text-base font-normal">
                        (<?php echo e(ucfirst(str_replace('_', ' ', $period))); ?>)
                    </span>
                </h3>
                
                <div class="space-y-4">
                    <?php $__currentLoopData = $topPerformers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center p-4 rounded-lg <?php echo e($performer->id === $user->id ? 'bg-primary-600/20 border border-primary-600' : 'bg-gray-800/50'); ?> hover:bg-gray-700/50 transition-colors">
                        <!-- Rank -->
                        <div class="w-12 h-12 rounded-full flex items-center justify-center mr-4 flex-shrink-0 <?php echo e($index < 3 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' : 'bg-gray-600'); ?>">
                            <?php if($index === 0): ?>
                                <span class="text-2xl">🥇</span>
                            <?php elseif($index === 1): ?>
                                <span class="text-2xl">🥈</span>
                            <?php elseif($index === 2): ?>
                                <span class="text-2xl">🥉</span>
                            <?php else: ?>
                                <span class="text-white font-bold"><?php echo e($index + 1); ?></span>
                            <?php endif; ?>
                        </div>

                        <!-- User Info -->
                        <div class="flex items-center flex-1">
                            <img src="<?php echo e($performer->getAvatarUrl()); ?>" 
                                 alt="<?php echo e($performer->name); ?>" 
                                 class="w-10 h-10 rounded-full mr-3">
                            <div class="flex-1">
                                <h4 class="font-semibold <?php echo e($performer->id === $user->id ? 'text-primary-400' : 'text-white'); ?>">
                                    <?php echo e($performer->name); ?>

                                    <?php if($performer->id === $user->id): ?>
                                        <span class="text-sm text-primary-300">(You)</span>
                                    <?php endif; ?>
                                </h4>
                                <p class="text-sm text-gray-400">Level <?php echo e($performer->level); ?></p>
                            </div>
                        </div>

                        <!-- Stats -->
                        <div class="text-right">
                            <?php if($period !== 'all_time' && isset($performer->period_xp)): ?>
                                <p class="text-lg font-bold text-yellow-400"><?php echo e($performer->period_xp); ?> XP</p>
                                <p class="text-sm text-gray-400"><?php echo e($performer->xp); ?> total</p>
                            <?php else: ?>
                                <p class="text-lg font-bold text-yellow-400"><?php echo e($performer->xp); ?> XP</p>
                                <p class="text-sm text-gray-400">Level <?php echo e($performer->level); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                    <?php if($topPerformers->count() === 0): ?>
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold mb-2">No Data Available</h3>
                        <p class="text-gray-400">Complete some tasks to appear on the leaderboard!</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Your Stats -->
        <div class="space-y-6">
            <!-- Your Rank -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Your Ranking</h3>
                <div class="text-center">
                    <?php if($userRank): ?>
                        <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold text-white">#<?php echo e($userRank); ?></span>
                        </div>
                        <p class="text-lg font-semibold">Rank #<?php echo e($userRank); ?></p>
                        <p class="text-sm text-gray-400"><?php echo e(ucfirst(str_replace('_', ' ', $period))); ?> leaderboard</p>
                    <?php else: ?>
                        <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl">🚀</span>
                        </div>
                        <p class="text-lg font-semibold">Not Ranked</p>
                        <p class="text-sm text-gray-400">Complete tasks to get ranked!</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Your Stats -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Your Stats</h3>
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400"><?php echo e(ucfirst(str_replace('_', ' ', $period))); ?> XP:</span>
                        <span class="font-semibold text-yellow-400"><?php echo e($userStats['period_xp'] ?? 0); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Tasks Completed:</span>
                        <span class="font-semibold text-green-400"><?php echo e($userStats['tasks_completed'] ?? 0); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total XP:</span>
                        <span class="font-semibold text-blue-400"><?php echo e($userStats['total_xp']); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Current Level:</span>
                        <span class="font-semibold text-purple-400"><?php echo e($userStats['level']); ?></span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Climb the Ranks</h3>
                <div class="space-y-3">
                    <a href="<?php echo e(route('dashboard.tasks')); ?>" class="btn-primary w-full text-center">
                        Complete Daily Tasks
                    </a>
                    <a href="<?php echo e(route('dashboard.campuses')); ?>" class="btn-secondary w-full text-center">
                        Take Lessons
                    </a>
                    <a href="<?php echo e(route('dashboard.events.index')); ?>" class="btn-secondary w-full text-center">
                        Join Live Events
                    </a>
                </div>
            </div>

            <!-- Leaderboard Info -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">How Rankings Work</h3>
                <div class="text-sm text-gray-300 space-y-2">
                    <p><strong>Daily:</strong> XP earned today</p>
                    <p><strong>Weekly:</strong> XP earned this week</p>
                    <p><strong>Monthly:</strong> XP earned this month</p>
                    <p><strong>All Time:</strong> Total XP accumulated</p>
                </div>
                <div class="mt-4 p-3 bg-blue-600/20 rounded-lg">
                    <p class="text-sm text-blue-300">
                        💡 <strong>Tip:</strong> Complete daily tasks consistently to maintain your position!
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/leaderboard/index.blade.php ENDPATH**/ ?>