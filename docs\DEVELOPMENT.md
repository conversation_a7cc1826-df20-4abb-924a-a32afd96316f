# The Real World - Development Guide

This guide covers development setup, coding standards, and contribution guidelines for The Real World Laravel application.

## 🛠️ Development Environment Setup

### Prerequisites

- **PHP 8.1+** with required extensions
- **Composer 2.x**
- **Node.js 18.x+** and npm
- **MySQL 8.0+** or MariaDB 10.3+
- **Redis 6.0+**
- **Git**

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd realworld-clone
   ```

2. **Install dependencies**
   ```bash
   composer install
   npm install
   ```

3. **Environment configuration**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

4. **Database setup**
   ```bash
   # Create database
   mysql -u root -p -e "CREATE DATABASE realworld_dev"
   
   # Run migrations and seeders
   php artisan migrate
   php artisan db:seed
   ```

5. **Storage setup**
   ```bash
   php artisan storage:link
   chmod -R 775 storage bootstrap/cache
   ```

6. **Start development servers**
   ```bash
   # Terminal 1: Laravel development server
   php artisan serve
   
   # Terminal 2: Vite development server
   npm run dev
   
   # Terminal 3: Queue worker
   php artisan queue:work
   
   # Terminal 4: WebSocket server (if using)
   php artisan websockets:serve
   ```

### Docker Development (Alternative)

```bash
# Using Laravel Sail
./vendor/bin/sail up -d
./vendor/bin/sail artisan migrate
./vendor/bin/sail artisan db:seed
./vendor/bin/sail npm run dev
```

## 📁 Project Structure

```
realworld-clone/
├── app/
│   ├── Console/Commands/          # Artisan commands
│   ├── Http/
│   │   ├── Controllers/           # HTTP controllers
│   │   ├── Middleware/            # Custom middleware
│   │   └── Requests/              # Form request validation
│   ├── Models/                    # Eloquent models
│   ├── Services/                  # Business logic services
│   ├── Jobs/                      # Queue jobs
│   └── Policies/                  # Authorization policies
├── database/
│   ├── migrations/                # Database migrations
│   ├── seeders/                   # Database seeders
│   └── factories/                 # Model factories
├── resources/
│   ├── views/                     # Blade templates
│   ├── js/                        # JavaScript files
│   └── css/                       # CSS files
├── routes/
│   ├── web.php                    # Web routes
│   ├── api.php                    # API routes
│   └── channels.php               # Broadcast channels
├── tests/
│   ├── Feature/                   # Feature tests
│   ├── Unit/                      # Unit tests
│   └── Integration/               # Integration tests
└── docs/                          # Documentation
```

## 🎯 Coding Standards

### PHP Standards

We follow **PSR-12** coding standards with Laravel conventions:

```bash
# Format code with Laravel Pint
./vendor/bin/pint

# Check code with PHPStan
./vendor/bin/phpstan analyse
```

### Key Conventions

1. **Class Naming**
   - Controllers: `PascalCase` + `Controller` suffix
   - Models: `PascalCase` (singular)
   - Services: `PascalCase` + `Service` suffix
   - Jobs: `PascalCase` + `Job` suffix

2. **Method Naming**
   - Use `camelCase` for methods
   - Use descriptive names: `getUserCourses()` not `get()`

3. **Variable Naming**
   - Use `camelCase` for variables
   - Use descriptive names: `$userCourses` not `$data`

4. **Database Conventions**
   - Table names: `snake_case` (plural)
   - Column names: `snake_case`
   - Foreign keys: `model_id` format

### JavaScript/CSS Standards

```bash
# Format JavaScript/CSS
npm run lint:fix

# Check for issues
npm run lint
```

## 🧪 Testing Guidelines

### Test Structure

- **Unit Tests**: Test individual classes/methods
- **Feature Tests**: Test HTTP endpoints and user flows
- **Integration Tests**: Test component interactions

### Writing Tests

1. **Test Naming**
   ```php
   public function test_user_can_enroll_in_course(): void
   public function test_guest_cannot_access_admin_panel(): void
   ```

2. **Test Organization**
   ```php
   class CourseTest extends TestCase
   {
       use RefreshDatabase, TestHelpers;
       
       public function test_user_can_view_published_courses(): void
       {
           // Arrange
           $courses = Course::factory()->published()->count(3)->create();
           
           // Act
           $response = $this->get('/courses');
           
           // Assert
           $response->assertStatus(200);
           $response->assertViewHas('courses');
       }
   }
   ```

3. **Using Test Helpers**
   ```php
   // Use custom test helpers
   $this->actingAsAdmin();
   $this->createCourseWithLessons(5);
   $this->assertUserEnrolledInCourse($user, $course);
   ```

### Running Tests

```bash
# Run all tests
php artisan test

# Run specific test file
php artisan test tests/Feature/CourseTest.php

# Run with coverage
php artisan test --coverage

# Run specific test method
php artisan test --filter test_user_can_enroll_in_course
```

## 🔄 Git Workflow

### Branch Naming

- `feature/feature-name` - New features
- `bugfix/bug-description` - Bug fixes
- `hotfix/critical-fix` - Critical production fixes
- `refactor/component-name` - Code refactoring

### Commit Messages

Use conventional commit format:

```
type(scope): description

feat(courses): add course enrollment functionality
fix(auth): resolve login redirect issue
docs(api): update authentication documentation
test(courses): add course enrollment tests
refactor(services): extract user service methods
```

### Pull Request Process

1. Create feature branch from `main`
2. Make changes with tests
3. Run quality checks:
   ```bash
   ./vendor/bin/pint
   ./vendor/bin/phpstan analyse
   php artisan test
   ```
4. Create pull request with:
   - Clear description
   - Screenshots (if UI changes)
   - Test coverage information
   - Breaking changes (if any)

## 🏗️ Architecture Patterns

### Service Layer Pattern

Business logic should be in service classes:

```php
class CourseService
{
    public function enrollUser(User $user, Course $course): CourseEnrollment
    {
        // Validate enrollment eligibility
        if (!$this->canUserEnroll($user, $course)) {
            throw new EnrollmentException('User cannot enroll in this course');
        }
        
        // Create enrollment
        $enrollment = $user->courseEnrollments()->create([
            'course_id' => $course->id,
            'enrolled_at' => now(),
        ]);
        
        // Award enrollment XP
        $user->earnXp(config('gamification.enrollment_xp', 50));
        
        // Send notification
        $user->notify(new CourseEnrollmentNotification($course));
        
        return $enrollment;
    }
}
```

### Repository Pattern (Optional)

For complex queries, consider repository pattern:

```php
class CourseRepository
{
    public function getPopularCourses(int $limit = 10): Collection
    {
        return Course::query()
            ->published()
            ->withCount('enrollments')
            ->orderBy('enrollments_count', 'desc')
            ->limit($limit)
            ->get();
    }
}
```

### Event-Driven Architecture

Use Laravel events for decoupled functionality:

```php
// Event
class UserEnrolledInCourse
{
    public function __construct(
        public User $user,
        public Course $course,
        public CourseEnrollment $enrollment
    ) {}
}

// Listener
class SendEnrollmentNotification
{
    public function handle(UserEnrolledInCourse $event): void
    {
        $event->user->notify(new CourseEnrollmentNotification($event->course));
    }
}
```

## 🔧 Development Tools

### Debugging

```bash
# Enable query logging
DB::enableQueryLog();
// ... your code
dd(DB::getQueryLog());

# Use Laravel Debugbar (development only)
composer require barryvdh/laravel-debugbar --dev

# Use Telescope for advanced debugging
composer require laravel/telescope --dev
php artisan telescope:install
```

### Performance Profiling

```bash
# Analyze performance
php artisan performance:analyze

# Monitor slow queries
php artisan db:monitor

# Cache optimization
php artisan optimize:clear
php artisan optimize
```

### Code Quality Tools

```bash
# PHP CS Fixer (Laravel Pint)
./vendor/bin/pint

# Static Analysis (PHPStan)
./vendor/bin/phpstan analyse

# Security Analysis
composer audit

# Dependency Analysis
composer outdated
```

## 📦 Package Development

### Creating Custom Packages

1. Create package structure in `packages/` directory
2. Add to `composer.json`:
   ```json
   "autoload": {
       "psr-4": {
           "App\\": "app/",
           "YourPackage\\": "packages/your-package/src/"
       }
   }
   ```

3. Register service provider
4. Add tests in `packages/your-package/tests/`

### Package Guidelines

- Follow PSR-4 autoloading
- Include comprehensive tests
- Provide clear documentation
- Use semantic versioning
- Include CI/CD configuration

## 🚀 Deployment Preparation

### Pre-deployment Checklist

- [ ] All tests passing
- [ ] Code quality checks passed
- [ ] Database migrations tested
- [ ] Environment variables documented
- [ ] Performance optimizations applied
- [ ] Security review completed
- [ ] Documentation updated

### Build Process

```bash
# Production build
composer install --no-dev --optimize-autoloader
npm ci --production
npm run build

# Optimize Laravel
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
```

## 🤝 Contributing Guidelines

1. **Fork the repository**
2. **Create feature branch**
3. **Follow coding standards**
4. **Write comprehensive tests**
5. **Update documentation**
6. **Submit pull request**

### Code Review Checklist

- [ ] Code follows project standards
- [ ] Tests cover new functionality
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance impact considered
- [ ] Backward compatibility maintained

## 📚 Additional Resources

- [Laravel Documentation](https://laravel.com/docs)
- [PHP The Right Way](https://phptherightway.com/)
- [Laravel Best Practices](https://github.com/alexeymezenin/laravel-best-practices)
- [PSR Standards](https://www.php-fig.org/psr/)

## 🆘 Getting Help

- **Documentation**: Check `docs/` directory
- **Issues**: Create GitHub issue
- **Discussions**: Use GitHub discussions
- **Chat**: Join development Discord channel

---

Happy coding! 🚀
