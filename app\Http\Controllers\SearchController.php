<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Event;
use App\Models\User;
use App\Models\Campus;
use App\Models\ChatMessage;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;

class SearchController extends Controller
{
    /**
     * Global search across all content types
     */
    public function search(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'all');
        $limit = $request->get('limit', 20);

        if (empty($query)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'results' => [],
                    'total' => 0,
                    'query' => $query,
                ]);
            }
            return view('search.index', ['results' => [], 'query' => $query]);
        }

        $results = $this->performSearch($query, $type, $limit);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'results' => $results,
                'total' => collect($results)->sum('count'),
                'query' => $query,
            ]);
        }

        return view('search.index', compact('results', 'query', 'type'));
    }

    /**
     * Quick search for autocomplete
     */
    public function quickSearch(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $limit = $request->get('limit', 10);

        if (empty($query) || strlen($query) < 2) {
            return response()->json([
                'success' => true,
                'suggestions' => [],
            ]);
        }

        $suggestions = [];

        // Search courses
        $courses = Course::where('title', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->limit(3)
            ->get(['id', 'title', 'slug'])
            ->map(function ($course) {
                return [
                    'type' => 'course',
                    'title' => $course->title,
                    'url' => route('dashboard.courses.show', $course),
                    'icon' => '📚',
                ];
            });

        // Search events
        $events = Event::where('title', 'like', "%{$query}%")
            ->orWhere('description', 'like', "%{$query}%")
            ->where('start_time', '>=', now())
            ->limit(3)
            ->get(['id', 'title', 'slug'])
            ->map(function ($event) {
                return [
                    'type' => 'event',
                    'title' => $event->title,
                    'url' => route('dashboard.events.show', $event),
                    'icon' => '📅',
                ];
            });

        // Search users (if user has permission)
        $users = collect();
        if (auth()->user()->can('search_users')) {
            $users = User::where('name', 'like', "%{$query}%")
                ->orWhere('username', 'like', "%{$query}%")
                ->limit(3)
                ->get(['id', 'name', 'username'])
                ->map(function ($user) {
                    return [
                        'type' => 'user',
                        'title' => $user->name . ' (@' . $user->username . ')',
                        'url' => route('dashboard.profile.show', $user),
                        'icon' => '👤',
                    ];
                });
        }

        $suggestions = $courses->concat($events)->concat($users)->take($limit);

        return response()->json([
            'success' => true,
            'suggestions' => $suggestions,
        ]);
    }

    /**
     * Search courses with advanced filtering
     */
    public function searchCourses(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $campus = $request->get('campus');
        $difficulty = $request->get('difficulty');
        $duration = $request->get('duration');
        $sort = $request->get('sort', 'relevance');
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 12);

        $coursesQuery = Course::query();

        // Text search
        if (!empty($query)) {
            $coursesQuery->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('content', 'like', "%{$query}%");
            });
        }

        // Campus filter
        if ($campus) {
            $coursesQuery->where('campus_id', $campus);
        }

        // Difficulty filter
        if ($difficulty) {
            $coursesQuery->where('difficulty_level', $difficulty);
        }

        // Duration filter
        if ($duration) {
            switch ($duration) {
                case 'short':
                    $coursesQuery->where('estimated_duration', '<=', 30);
                    break;
                case 'medium':
                    $coursesQuery->whereBetween('estimated_duration', [31, 120]);
                    break;
                case 'long':
                    $coursesQuery->where('estimated_duration', '>', 120);
                    break;
            }
        }

        // Sorting
        switch ($sort) {
            case 'newest':
                $coursesQuery->orderBy('created_at', 'desc');
                break;
            case 'popular':
                $coursesQuery->withCount('enrollments')->orderBy('enrollments_count', 'desc');
                break;
            case 'rating':
                $coursesQuery->orderBy('rating', 'desc');
                break;
            case 'duration':
                $coursesQuery->orderBy('estimated_duration', 'asc');
                break;
            default: // relevance
                if (!empty($query)) {
                    $coursesQuery->orderByRaw("
                        CASE
                            WHEN title LIKE ? THEN 1
                            WHEN description LIKE ? THEN 2
                            ELSE 3
                        END
                    ", ["%{$query}%", "%{$query}%"]);
                } else {
                    $coursesQuery->orderBy('created_at', 'desc');
                }
                break;
        }

        $courses = $coursesQuery->with(['campus'])
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'courses' => $courses->map(function ($course) {
                return [
                    'id' => $course->id,
                    'title' => $course->title,
                    'description' => $course->description,
                    'slug' => $course->slug,
                    'campus' => $course->campus->name ?? 'General',
                    'difficulty_level' => $course->difficulty_level,
                    'estimated_duration' => $course->estimated_duration,
                    'rating' => $course->rating,
                    'url' => route('dashboard.courses.show', $course),
                    'image_url' => $course->getFirstMediaUrl('thumbnail'),
                ];
            }),
            'pagination' => [
                'current_page' => $courses->currentPage(),
                'last_page' => $courses->lastPage(),
                'per_page' => $courses->perPage(),
                'total' => $courses->total(),
            ],
        ]);
    }

    /**
     * Search events with filtering
     */
    public function searchEvents(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $type = $request->get('event_type');
        $date = $request->get('date');
        $sort = $request->get('sort', 'date');
        $page = $request->get('page', 1);
        $perPage = $request->get('per_page', 12);

        $eventsQuery = Event::query();

        // Text search
        if (!empty($query)) {
            $eventsQuery->where(function ($q) use ($query) {
                $q->where('title', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            });
        }

        // Type filter
        if ($type) {
            $eventsQuery->where('type', $type);
        }

        // Date filter
        if ($date) {
            switch ($date) {
                case 'today':
                    $eventsQuery->whereDate('start_time', today());
                    break;
                case 'tomorrow':
                    $eventsQuery->whereDate('start_time', today()->addDay());
                    break;
                case 'this_week':
                    $eventsQuery->whereBetween('start_time', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'next_week':
                    $eventsQuery->whereBetween('start_time', [now()->addWeek()->startOfWeek(), now()->addWeek()->endOfWeek()]);
                    break;
                case 'this_month':
                    $eventsQuery->whereBetween('start_time', [now()->startOfMonth(), now()->endOfMonth()]);
                    break;
            }
        } else {
            // Only show future events by default
            $eventsQuery->where('start_time', '>=', now());
        }

        // Sorting
        switch ($sort) {
            case 'popular':
                $eventsQuery->withCount('rsvps')->orderBy('rsvps_count', 'desc');
                break;
            case 'newest':
                $eventsQuery->orderBy('created_at', 'desc');
                break;
            default: // date
                $eventsQuery->orderBy('start_time', 'asc');
                break;
        }

        $events = $eventsQuery->with(['creator'])
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'events' => $events->map(function ($event) {
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'description' => $event->description,
                    'slug' => $event->slug,
                    'type' => $event->type,
                    'start_time' => $event->start_time->format('Y-m-d H:i:s'),
                    'end_time' => $event->end_time?->format('Y-m-d H:i:s'),
                    'creator' => $event->creator->name ?? 'System',
                    'url' => route('dashboard.events.show', $event),
                    'rsvp_count' => $event->rsvps_count ?? 0,
                ];
            }),
            'pagination' => [
                'current_page' => $events->currentPage(),
                'last_page' => $events->lastPage(),
                'per_page' => $events->perPage(),
                'total' => $events->total(),
            ],
        ]);
    }

    /**
     * Perform comprehensive search across all content types
     */
    private function performSearch(string $query, string $type = 'all', int $limit = 20): array
    {
        $results = [];

        if ($type === 'all' || $type === 'courses') {
            $courses = Course::where('title', 'like', "%{$query}%")
                ->orWhere('description', 'like', "%{$query}%")
                ->orWhere('content', 'like', "%{$query}%")
                ->with(['campus'])
                ->limit($limit)
                ->get();

            $results['courses'] = [
                'type' => 'courses',
                'title' => 'Courses',
                'count' => $courses->count(),
                'items' => $courses->map(function ($course) {
                    return [
                        'id' => $course->id,
                        'title' => $course->title,
                        'description' => $course->description,
                        'url' => route('dashboard.courses.show', $course),
                        'meta' => $course->campus->name ?? 'General',
                        'image' => $course->getFirstMediaUrl('thumbnail'),
                    ];
                }),
            ];
        }

        if ($type === 'all' || $type === 'events') {
            $events = Event::where('title', 'like', "%{$query}%")
                ->orWhere('description', 'like', "%{$query}%")
                ->where('start_time', '>=', now())
                ->with(['creator'])
                ->limit($limit)
                ->get();

            $results['events'] = [
                'type' => 'events',
                'title' => 'Events',
                'count' => $events->count(),
                'items' => $events->map(function ($event) {
                    return [
                        'id' => $event->id,
                        'title' => $event->title,
                        'description' => $event->description,
                        'url' => route('dashboard.events.show', $event),
                        'meta' => $event->start_time->format('M j, Y \a\t g:i A'),
                        'image' => null,
                    ];
                }),
            ];
        }

        if ($type === 'all' || $type === 'users') {
            if (auth()->user()->can('search_users')) {
                $users = User::where('name', 'like', "%{$query}%")
                    ->orWhere('username', 'like', "%{$query}%")
                    ->limit($limit)
                    ->get();

                $results['users'] = [
                    'type' => 'users',
                    'title' => 'Members',
                    'count' => $users->count(),
                    'items' => $users->map(function ($user) {
                        return [
                            'id' => $user->id,
                            'title' => $user->name,
                            'description' => '@' . $user->username,
                            'url' => route('dashboard.profile.show', $user),
                            'meta' => 'Level ' . $user->level . ' • ' . number_format($user->xp) . ' XP',
                            'image' => $user->avatar_url,
                        ];
                    }),
                ];
            }
        }

        return $results;
    }
}
