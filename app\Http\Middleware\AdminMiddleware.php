<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        // Check if user has admin role (with fallback to is_admin field)
        $isAdmin = false;

        try {
            // Primary check: Spatie Permission role
            $isAdmin = $user->hasRole('admin');
        } catch (\Exception $e) {
            // Fallback: Check if user has is_admin field
            $isAdmin = $user->is_admin ?? false;
        }

        // Additional fallback: Check if user is the first user (super admin)
        if (!$isAdmin && $user->id === 1) {
            $isAdmin = true;
        }

        if (!$isAdmin) {
            abort(403, 'Access denied. Admin privileges required.');
        }

        return $next($request);
    }
}
