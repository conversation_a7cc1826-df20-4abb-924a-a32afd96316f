<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Event;

class EventReminder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $event;
    protected $reminderType;

    /**
     * Create a new notification instance.
     */
    public function __construct(Event $event, string $reminderType = '24h')
    {
        $this->event = $event;
        $this->reminderType = $reminderType;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $timeText = $this->getTimeText();

        return (new MailMessage)
                    ->subject('🎯 Event Reminder: ' . $this->event->title)
                    ->greeting('Don\'t miss out, ' . $notifiable->name . '!')
                    ->line('This is a reminder that you have an upcoming event:')
                    ->line('**' . $this->event->title . '**')
                    ->line('📅 **When:** ' . $this->event->start_time->format('l, F j, Y \a\t g:i A T'))
                    ->line('⏰ **Starting ' . $timeText . '**')
                    ->when($this->event->description, function ($message) {
                        return $message->line('📝 **Description:** ' . $this->event->description);
                    })
                    ->when($this->event->meeting_url, function ($message) {
                        return $message->line('🔗 **Meeting Link:** ' . $this->event->meeting_url);
                    })
                    ->line('Make sure you\'re ready to attend and get the most out of this exclusive session.')
                    ->action('View Event Details', route('dashboard.events.show', $this->event))
                    ->line('Success waits for no one. Be there!')
                    ->salutation('See you there,')
                    ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Event Reminder: ' . $this->event->title,
            'message' => 'Your event "' . $this->event->title . '" starts ' . $this->getTimeText() . '.',
            'action_url' => route('dashboard.events.show', $this->event),
            'action_text' => 'View Event',
            'type' => 'event_reminder',
            'icon' => '🎯',
            'data' => [
                'event_id' => $this->event->id,
                'event_title' => $this->event->title,
                'start_time' => $this->event->start_time->toISOString(),
                'reminder_type' => $this->reminderType,
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Event Reminder',
            'message' => 'Your event "' . $this->event->title . '" starts ' . $this->getTimeText() . '.',
            'type' => 'event_reminder',
            'event_id' => $this->event->id,
        ];
    }

    /**
     * Get the time text for the reminder
     */
    private function getTimeText(): string
    {
        return match ($this->reminderType) {
            '1h' => 'in 1 hour',
            '24h' => 'in 24 hours',
            '1w' => 'in 1 week',
            default => 'soon',
        };
    }
}
