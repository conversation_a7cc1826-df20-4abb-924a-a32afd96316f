@extends('layouts.admin')

@section('title', 'Analytics - Admin')
@section('page-title', 'Analytics')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-2">Analytics Dashboard</h2>
        <p class="text-gray-400">Monitor user activity and platform performance</p>
    </div>

    <!-- User Activity Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Daily Active Users</h3>
                <svg class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ number_format($userActivity['daily_active_users']) }}</div>
            <p class="text-sm text-gray-400">Last 24 hours</p>
        </div>

        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Weekly Active Users</h3>
                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ number_format($userActivity['weekly_active_users']) }}</div>
            <p class="text-sm text-gray-400">Last 7 days</p>
        </div>

        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">Monthly Active Users</h3>
                <svg class="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ number_format($userActivity['monthly_active_users']) }}</div>
            <p class="text-sm text-gray-400">Last 30 days</p>
        </div>

        <div class="card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-semibold">New Registrations</h3>
                <svg class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <div class="text-2xl font-bold mb-2">{{ number_format($userActivity['new_registrations_today']) }}</div>
            <p class="text-sm text-gray-400">Today</p>
        </div>
    </div>

    <!-- Course Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Course Performance</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Total Enrollments</span>
                    <span class="font-bold">{{ number_format($courseAnalytics['total_enrollments']) }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Completed Courses</span>
                    <span class="font-bold">{{ number_format($courseAnalytics['completed_courses']) }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Average Completion Rate</span>
                    <span class="font-bold">{{ number_format($courseAnalytics['completion_rate'], 1) }}%</span>
                </div>
            </div>
        </div>

        <div class="card">
            <h3 class="text-lg font-semibold mb-4">Revenue Analytics</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Monthly Revenue</span>
                    <span class="font-bold text-green-400">${{ number_format($revenueAnalytics['monthly_revenue'], 2) }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Total Revenue</span>
                    <span class="font-bold text-green-400">${{ number_format($revenueAnalytics['total_revenue'], 2) }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                    <span>Active Subscriptions</span>
                    <span class="font-bold">{{ number_format($revenueAnalytics['active_subscriptions']) }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Event Analytics -->
    <div class="card">
        <h3 class="text-lg font-semibold mb-4">Event Analytics</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
                <div class="text-3xl font-bold text-primary-400 mb-2">{{ number_format($eventAnalytics['total_events']) }}</div>
                <p class="text-gray-400">Total Events</p>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-400 mb-2">{{ number_format($eventAnalytics['upcoming_events']) }}</div>
                <p class="text-gray-400">Upcoming Events</p>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-400 mb-2">{{ number_format($eventAnalytics['total_rsvps']) }}</div>
                <p class="text-gray-400">Total RSVPs</p>
            </div>
        </div>
    </div>
</div>
@endsection
