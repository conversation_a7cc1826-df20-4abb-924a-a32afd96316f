<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Campus;

class CampusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $campuses = [
            [
                'name' => 'E-commerce',
                'slug' => 'ecommerce',
                'description' => 'Learn how to build and scale profitable e-commerce businesses from scratch.',
                'teaser_description' => 'Master dropshipping, Amazon FBA, and online retail strategies.',
                'color' => '#10b981',
                'sort_order' => 1,
                'is_active' => true,
                'is_premium' => true,
                'required_plans' => ['conquer', 'champions'],
            ],
            [
                'name' => 'Freelancing',
                'slug' => 'freelancing',
                'description' => 'Build a successful freelancing career and escape the 9-5 grind.',
                'teaser_description' => 'Learn high-income skills like copywriting, web design, and consulting.',
                'color' => '#3b82f6',
                'sort_order' => 2,
                'is_active' => true,
                'is_premium' => false,
                'required_plans' => null,
            ],
            [
                'name' => 'Cryptocurrency',
                'slug' => 'cryptocurrency',
                'description' => 'Master cryptocurrency trading and DeFi strategies.',
                'teaser_description' => 'Learn trading, staking, and crypto investment strategies.',
                'color' => '#f59e0b',
                'sort_order' => 3,
                'is_active' => true,
                'is_premium' => true,
                'required_plans' => ['conquer', 'champions'],
            ],
            [
                'name' => 'AI & Automation',
                'slug' => 'ai-automation',
                'description' => 'Leverage AI and automation to build scalable businesses.',
                'teaser_description' => 'Use AI tools and automation to create passive income streams.',
                'color' => '#8b5cf6',
                'sort_order' => 4,
                'is_active' => true,
                'is_premium' => true,
                'required_plans' => ['champions'],
            ],
            [
                'name' => 'Real Estate',
                'slug' => 'real-estate',
                'description' => 'Build wealth through real estate investing and property management.',
                'teaser_description' => 'Learn rental properties, flipping, and real estate investing.',
                'color' => '#ef4444',
                'sort_order' => 5,
                'is_active' => true,
                'is_premium' => true,
                'required_plans' => ['conquer', 'champions'],
            ],
            [
                'name' => 'Fitness',
                'slug' => 'fitness',
                'description' => 'Build a strong body and mind to support your entrepreneurial journey.',
                'teaser_description' => 'Workout plans, nutrition, and mental toughness training.',
                'color' => '#06b6d4',
                'sort_order' => 6,
                'is_active' => true,
                'is_premium' => false,
                'required_plans' => null,
            ],
        ];

        foreach ($campuses as $campus) {
            Campus::updateOrCreate(
                ['slug' => $campus['slug']],
                $campus
            );
        }
    }
}
