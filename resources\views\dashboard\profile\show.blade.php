@extends('layouts.dashboard')

@section('title', 'My Profile - The Real World')
@section('page-title', 'My Profile')

@section('content')
<div class="p-6">
    <!-- Profile Header -->
    <div class="card mb-8">
        <div class="relative">
            <!-- Cover Image -->
            <div class="h-32 bg-gradient-to-r from-primary-600 to-primary-800 rounded-t-lg"></div>
            
            <!-- Profile Info -->
            <div class="relative px-6 pb-6">
                <!-- Avatar -->
                <div class="absolute -top-16 left-6">
                    <img src="{{ $user->getAvatarUrl() }}" 
                         alt="{{ $user->name }}" 
                         class="w-32 h-32 rounded-full border-4 border-gray-800 bg-gray-800">
                </div>

                <!-- User Info -->
                <div class="pt-20 flex items-start justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <h1 class="text-2xl font-bold mr-4">{{ $user->name }}</h1>
                            
                            <!-- User Roles -->
                            @foreach($user->roles as $role)
                                <span class="px-3 py-1 text-sm rounded-full mr-2 {{ $role->name === 'admin' ? 'bg-red-600' : ($role->name === 'mentor' ? 'bg-blue-600' : ($role->name === 'champion' ? 'bg-yellow-600' : 'bg-gray-600')) }} text-white">
                                    {{ ucfirst($role->name) }}
                                </span>
                            @endforeach
                        </div>

                        <p class="text-gray-400 mb-4">{{ $user->bio ?? 'No bio available.' }}</p>

                        <!-- User Meta -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            @if($user->location)
                            <div class="flex items-center text-gray-300">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                {{ $user->location }}
                            </div>
                            @endif

                            <div class="flex items-center text-gray-300">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                Joined {{ $stats['member_since']->format('M Y') }}
                            </div>

                            <div class="flex items-center text-gray-300">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Last active {{ $stats['last_active']->diffForHumans() }}
                            </div>
                        </div>

                        <!-- Social Links -->
                        @if($user->website || $user->twitter || $user->linkedin)
                        <div class="flex items-center space-x-4 mt-4">
                            @if($user->website)
                                <a href="{{ $user->website }}" target="_blank" class="text-primary-400 hover:text-primary-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                                    </svg>
                                </a>
                            @endif
                            @if($user->twitter)
                                <a href="https://twitter.com/{{ $user->twitter }}" target="_blank" class="text-blue-400 hover:text-blue-300">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                                    </svg>
                                </a>
                            @endif
                            @if($user->linkedin)
                                <a href="https://linkedin.com/in/{{ $user->linkedin }}" target="_blank" class="text-blue-600 hover:text-blue-500">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                    </svg>
                                </a>
                            @endif
                        </div>
                        @endif
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-3">
                        <a href="{{ route('dashboard.profile.edit') }}" class="btn-secondary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Profile
                        </a>
                        <a href="{{ route('dashboard.settings.index') }}" class="btn-secondary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Level Progress -->
            <div class="card">
                <h3 class="text-xl font-semibold mb-6">Level Progress</h3>
                
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mr-4">
                            <span class="text-xl font-bold text-white">{{ $stats['current_level'] }}</span>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold">Level {{ $stats['current_level'] }}</h4>
                            <p class="text-gray-400">{{ number_format($stats['total_xp']) }} XP</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-400">Next Level</p>
                        <p class="font-semibold">{{ number_format($stats['next_level_xp'] - $stats['total_xp']) }} XP to go</p>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="w-full bg-gray-700 rounded-full h-3 mb-2">
                    <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-300" 
                         style="width: {{ $stats['progress_to_next_level'] }}%"></div>
                </div>
                <p class="text-sm text-gray-400 text-center">{{ $stats['progress_to_next_level'] }}% to Level {{ $stats['current_level'] + 1 }}</p>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <h3 class="text-xl font-semibold mb-6">Recent Activity</h3>
                
                @if($recentActivity->count() > 0)
                <div class="space-y-4">
                    @foreach($recentActivity as $activity)
                    <div class="flex items-start p-4 bg-gray-700 rounded-lg">
                        <div class="text-2xl mr-4">{{ $activity['icon'] }}</div>
                        <div class="flex-1">
                            <h4 class="font-medium">{{ $activity['title'] }}</h4>
                            <p class="text-gray-400 text-sm">{{ $activity['description'] }}</p>
                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                <span>{{ $activity['date']->diffForHumans() }}</span>
                                @if($activity['xp'] > 0)
                                    <span class="ml-4 text-yellow-400">+{{ $activity['xp'] }} XP</span>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">No Recent Activity</h3>
                    <p class="text-gray-400">Start learning to see your activity here!</p>
                </div>
                @endif
            </div>

            <!-- Earned Badges -->
            @if($earnedBadges->count() > 0)
            <div class="card">
                <h3 class="text-xl font-semibold mb-6">Recent Badges</h3>
                
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                    @foreach($earnedBadges as $badge)
                    <div class="text-center p-4 bg-gray-700 rounded-lg">
                        <div class="text-4xl mb-2">{{ $badge->icon ?? '🏆' }}</div>
                        <h4 class="font-medium text-sm">{{ $badge->name }}</h4>
                        <p class="text-xs text-gray-400 mt-1">{{ $badge->pivot->awarded_at->format('M j, Y') }}</p>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar Stats -->
        <div class="space-y-6">
            <!-- Stats Overview -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Lessons Completed</span>
                        <span class="font-semibold">{{ $stats['lessons_completed'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Courses Started</span>
                        <span class="font-semibold">{{ $stats['courses_started'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Courses Completed</span>
                        <span class="font-semibold">{{ $stats['courses_completed'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Events Attended</span>
                        <span class="font-semibold">{{ $stats['events_attended'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Messages Sent</span>
                        <span class="font-semibold">{{ $stats['messages_sent'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Tasks Completed</span>
                        <span class="font-semibold">{{ $stats['tasks_completed'] }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">Badges Earned</span>
                        <span class="font-semibold">{{ $stats['badges_earned'] }}</span>
                    </div>
                </div>
            </div>

            <!-- Subscription Info -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Subscription</h3>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                    </div>
                    <h4 class="text-xl font-bold mb-2">{{ ucfirst($user->subscription_plan ?? 'free') }}</h4>
                    <p class="text-gray-400 text-sm mb-4">Current Plan</p>
                    
                    <a href="{{ route('dashboard.settings.subscription') }}" class="btn-primary w-full">
                        Manage Subscription
                    </a>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('dashboard.campuses') }}" class="block w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <span class="text-sm font-medium">Continue Learning</span>
                        </div>
                    </a>
                    
                    <a href="{{ route('dashboard.events.index') }}" class="block w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-sm font-medium">View Events</span>
                        </div>
                    </a>
                    
                    <a href="{{ route('dashboard.chat') }}" class="block w-full text-left p-3 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <span class="text-sm font-medium">Join Chat</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
