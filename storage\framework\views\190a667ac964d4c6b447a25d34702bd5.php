<?php $__env->startSection('title', 'Tasks Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Tasks Management</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Tasks</li>
                </ol>
            </nav>
        </div>
        <a href="<?php echo e(route('admin.tasks.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Task
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.tasks.index')); ?>" class="row g-3">
                <div class="col-md-3">
                    <label for="campus" class="form-label">Campus</label>
                    <select name="campus" id="campus" class="form-select">
                        <option value="">All Campuses</option>
                        <?php $__currentLoopData = $campuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($campus->id); ?>" <?php echo e(request('campus') == $campus->id ? 'selected' : ''); ?>>
                                <?php echo e($campus->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="type" class="form-label">Type</label>
                    <select name="type" id="type" class="form-select">
                        <option value="">All Types</option>
                        <?php $__currentLoopData = $taskTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($type); ?>" <?php echo e(request('type') === $type ? 'selected' : ''); ?>>
                                <?php echo e(ucfirst($type)); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                    <a href="<?php echo e(route('admin.tasks.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Tasks Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Tasks (<?php echo e($tasks->total()); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if($tasks->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Campus</th>
                                <th>Type</th>
                                <th>XP Reward</th>
                                <th>Difficulty</th>
                                <th>Status</th>
                                <th>Completions</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo e($task->title); ?></strong>
                                        <?php if($task->is_daily): ?>
                                            <span class="badge badge-info badge-sm ml-1">Daily</span>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted"><?php echo e(Str::limit($task->description, 60)); ?></small>
                                </td>
                                <td>
                                    <span class="badge" style="background-color: <?php echo e($task->campus->color); ?>; color: white;">
                                        <?php echo e($task->campus->name); ?>

                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-secondary"><?php echo e(ucfirst($task->task_type)); ?></span>
                                </td>
                                <td>
                                    <span class="badge badge-success"><?php echo e($task->xp_reward); ?> XP</span>
                                </td>
                                <td>
                                    <span class="badge badge-<?php echo e($task->difficulty === 'easy' ? 'success' : ($task->difficulty === 'medium' ? 'warning' : 'danger')); ?>">
                                        <?php echo e(ucfirst($task->difficulty)); ?>

                                    </span>
                                </td>
                                <td>
                                    <?php if($task->is_active): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-center">
                                        <div><strong><?php echo e($task->total_completions); ?></strong></div>
                                        <small class="text-muted">
                                            <?php echo e($task->unique_users); ?> users<br>
                                            <?php echo e($task->completions_today); ?> today
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.tasks.show', $task)); ?>" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.tasks.edit', $task)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.tasks.toggle-status', $task)); ?>" method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-<?php echo e($task->is_active ? 'secondary' : 'success'); ?>" 
                                                    title="<?php echo e($task->is_active ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas fa-<?php echo e($task->is_active ? 'pause' : 'play'); ?>"></i>
                                            </button>
                                        </form>
                                        <?php if($task->total_completions == 0): ?>
                                        <form action="<?php echo e(route('admin.tasks.destroy', $task)); ?>" method="POST" class="d-inline" 
                                              onsubmit="return confirm('Are you sure you want to delete this task?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    <?php echo e($tasks->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No tasks found</h5>
                    <p class="text-muted">Create your first task to get started.</p>
                    <a href="<?php echo e(route('admin.tasks.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Task
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const filters = ['campus', 'status', 'type'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/tasks/index.blade.php ENDPATH**/ ?>