<div class="relative" x-data="notificationDropdown()">
    <!-- Notification Bell -->
    <button @click="toggleDropdown()" 
            class="relative p-2 text-gray-400 hover:text-white transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707a5 5 0 013.535-1.465z"></path>
        </svg>
        
        <!-- Unread count badge -->
        <span x-show="unreadCount > 0" 
              x-text="unreadCount > 99 ? '99+' : unreadCount"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
        </span>
    </button>

    <!-- Dropdown -->
    <div x-show="isOpen" 
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="transition ease-in duration-75"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95"
         @click.away="closeDropdown()"
         class="absolute right-0 mt-2 w-96 bg-gray-800 rounded-lg shadow-lg border border-gray-700 z-50">
        
        <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-700">
            <h3 class="font-semibold">Notifications</h3>
            <div class="flex items-center space-x-2">
                <button @click="markAllAsRead()" 
                        class="text-xs text-primary-400 hover:text-primary-300">
                    Mark all read
                </button>
                <a href="{{ route('dashboard.notifications.index') }}" 
                   class="text-xs text-gray-400 hover:text-white">
                    View all
                </a>
            </div>
        </div>

        <!-- Notifications List -->
        <div class="max-h-96 overflow-y-auto">
            <template x-if="loading">
                <div class="p-4 text-center">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500 mx-auto"></div>
                    <p class="text-sm text-gray-400 mt-2">Loading...</p>
                </div>
            </template>

            <template x-if="!loading && notifications.length === 0">
                <div class="p-8 text-center">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707a5 5 0 013.535-1.465z"></path>
                    </svg>
                    <p class="text-gray-400">No notifications</p>
                </div>
            </template>

            <template x-for="notification in notifications" :key="notification.id">
                <div class="notification-item border-b border-gray-700 last:border-b-0 hover:bg-gray-700 transition-colors"
                     :class="{ 'bg-gray-750': !notification.read_at }"
                     @click="handleNotificationClick(notification)">
                    <div class="p-4 cursor-pointer">
                        <div class="flex items-start space-x-3">
                            <div class="text-lg" x-text="notification.icon"></div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center space-x-2 mb-1">
                                    <h4 class="font-medium text-sm truncate" x-text="notification.title"></h4>
                                    <template x-if="!notification.read_at">
                                        <span class="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"></span>
                                    </template>
                                </div>
                                <p class="text-xs text-gray-400 line-clamp-2" x-text="notification.message"></p>
                                <p class="text-xs text-gray-500 mt-1" x-text="notification.created_at"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Footer -->
        <div class="p-3 border-t border-gray-700 text-center">
            <a href="{{ route('dashboard.notifications.index') }}" 
               class="text-sm text-primary-400 hover:text-primary-300">
                View all notifications
            </a>
        </div>
    </div>
</div>

<script>
function notificationDropdown() {
    return {
        isOpen: false,
        loading: false,
        notifications: [],
        unreadCount: 0,

        init() {
            this.loadUnreadCount();
            // Poll for new notifications every 30 seconds
            setInterval(() => {
                if (!this.isOpen) {
                    this.loadUnreadCount();
                }
            }, 30000);
        },

        toggleDropdown() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.loadNotifications();
            }
        },

        closeDropdown() {
            this.isOpen = false;
        },

        async loadUnreadCount() {
            try {
                const response = await fetch('/dashboard/notifications/unread-count');
                const data = await response.json();
                if (data.success) {
                    this.unreadCount = data.count;
                }
            } catch (error) {
                console.error('Error loading unread count:', error);
            }
        },

        async loadNotifications() {
            this.loading = true;
            try {
                const response = await fetch('/dashboard/notifications/recent');
                const data = await response.json();
                if (data.success) {
                    this.notifications = data.notifications;
                    this.unreadCount = data.unread_count;
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
            } finally {
                this.loading = false;
            }
        },

        async markAllAsRead() {
            try {
                const response = await fetch('/dashboard/notifications/mark-all-read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    }
                });
                
                const data = await response.json();
                if (data.success) {
                    this.notifications.forEach(notification => {
                        notification.read_at = new Date().toISOString();
                    });
                    this.unreadCount = 0;
                }
            } catch (error) {
                console.error('Error marking all as read:', error);
            }
        },

        async markAsRead(notificationId) {
            try {
                const response = await fetch(`/dashboard/notifications/${notificationId}/read`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    }
                });
                
                const data = await response.json();
                if (data.success) {
                    const notification = this.notifications.find(n => n.id === notificationId);
                    if (notification && !notification.read_at) {
                        notification.read_at = new Date().toISOString();
                        this.unreadCount = Math.max(0, this.unreadCount - 1);
                    }
                }
            } catch (error) {
                console.error('Error marking as read:', error);
            }
        },

        handleNotificationClick(notification) {
            // Mark as read if unread
            if (!notification.read_at) {
                this.markAsRead(notification.id);
            }

            // Navigate to action URL if available
            if (notification.action_url) {
                window.location.href = notification.action_url;
            }
        }
    }
}
</script>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.bg-gray-750 {
    background-color: rgba(55, 65, 81, 0.5);
}
</style>
