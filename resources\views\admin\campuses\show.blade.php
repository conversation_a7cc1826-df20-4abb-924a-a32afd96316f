@extends('layouts.admin')

@section('title', 'Campus Details - ' . $campus->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Campus Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.campuses.index') }}">Campuses</a></li>
                    <li class="breadcrumb-item active">{{ $campus->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.campuses.edit', $campus) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Campus
            </a>
            <a href="{{ route('admin.campuses.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Campus Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campus Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Name:</label>
                                <p class="mb-0">{{ $campus->name }}</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Slug:</label>
                                <p class="mb-0"><code>{{ $campus->slug }}</code></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Color:</label>
                                <p class="mb-0">
                                    <span class="badge" style="background-color: {{ $campus->color }}; color: white;">
                                        {{ $campus->color }}
                                    </span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Sort Order:</label>
                                <p class="mb-0">{{ $campus->sort_order }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Status:</label>
                                <p class="mb-0">
                                    @if($campus->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Type:</label>
                                <p class="mb-0">
                                    @if($campus->is_premium)
                                        <span class="badge badge-warning">Premium</span>
                                    @else
                                        <span class="badge badge-info">Free</span>
                                    @endif
                                </p>
                            </div>
                            @if($campus->is_premium && $campus->required_plans)
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Required Plans:</label>
                                <p class="mb-0">
                                    @foreach($campus->required_plans as $plan)
                                        <span class="badge badge-secondary mr-1">{{ ucfirst($plan) }}</span>
                                    @endforeach
                                </p>
                            </div>
                            @endif
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Created:</label>
                                <p class="mb-0">{{ $campus->created_at->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    @if($campus->icon_url)
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Icon:</label>
                        <div>
                            <img src="{{ $campus->icon_url }}" alt="{{ $campus->name }} Icon" class="img-thumbnail" style="max-width: 100px;">
                        </div>
                    </div>
                    @endif

                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Description:</label>
                        <div class="border p-3 rounded bg-light">
                            {!! nl2br(e($campus->description)) !!}
                        </div>
                    </div>

                    @if($campus->teaser_description)
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Teaser Description:</label>
                        <div class="border p-3 rounded bg-light">
                            {!! nl2br(e($campus->teaser_description)) !!}
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $campus->courses_count ?? 0 }}</h4>
                                <small class="text-muted">Courses</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $campus->users_count ?? 0 }}</h4>
                            <small class="text-muted">Enrolled Users</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.courses.index', ['campus' => $campus->id]) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-book"></i> View Courses
                        </a>
                        <a href="{{ route('admin.events.index', ['campus' => $campus->id]) }}" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-calendar"></i> View Events
                        </a>
                        <a href="{{ route('admin.tasks.index', ['campus' => $campus->id]) }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-tasks"></i> View Tasks
                        </a>
                        @if($campus->is_active)
                        <a href="{{ route('campuses.show', $campus->slug) }}" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-external-link-alt"></i> View Public Page
                        </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Courses -->
    @if($campus->courses->count() > 0)
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Courses ({{ $campus->courses->count() }})</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Difficulty</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Enrollments</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($campus->courses->take(10) as $course)
                        <tr>
                            <td>
                                <strong>{{ $course->title }}</strong>
                                @if($course->is_featured)
                                    <span class="badge badge-warning badge-sm ml-1">Featured</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge badge-{{ $course->difficulty === 'beginner' ? 'success' : ($course->difficulty === 'intermediate' ? 'warning' : 'danger') }}">
                                    {{ ucfirst($course->difficulty) }}
                                </span>
                            </td>
                            <td>{{ $course->duration_minutes ? $course->duration_minutes . ' min' : 'N/A' }}</td>
                            <td>
                                @if($course->is_active)
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-danger">Inactive</span>
                                @endif
                            </td>
                            <td>{{ $course->enrollments_count ?? 0 }}</td>
                            <td>
                                <a href="{{ route('admin.courses.show', $course) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.courses.edit', $course) }}" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                @if($campus->courses->count() > 10)
                <div class="text-center mt-3">
                    <a href="{{ route('admin.courses.index', ['campus' => $campus->id]) }}" class="btn btn-outline-primary">
                        View All {{ $campus->courses->count() }} Courses
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
    @endif
</div>
@endsection
