@extends('layouts.admin')

@section('title', 'Event Details - ' . $event->title)
@section('page-title', 'Event Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">{{ $event->title }}</h1>
            <p class="text-gray-400 mt-1">Event details and management</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.events.edit', $event) }}" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Event
            </a>
            <a href="{{ route('admin.events.index') }}" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Events
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Event Overview -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event Overview</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300 leading-relaxed">{{ $event->description }}</p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Category</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                {{ $event->category === 'workshop' ? 'bg-blue-100 text-blue-800' : 
                                   ($event->category === 'webinar' ? 'bg-green-100 text-green-800' : 
                                   ($event->category === 'networking' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800')) }}">
                                {{ ucfirst($event->category) }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            @if($event->start_time > now())
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Upcoming
                                </span>
                            @elseif($event->end_time > now())
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Live
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Completed
                                </span>
                            @endif
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Start Time</label>
                            <p class="text-white">{{ $event->start_time->format('M j, Y g:i A') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">End Time</label>
                            <p class="text-white">{{ $event->end_time->format('M j, Y g:i A') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Duration</label>
                            <p class="text-white">{{ $event->start_time->diffInMinutes($event->end_time) }} minutes</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Max Attendees</label>
                            <p class="text-white">{{ $event->max_attendees ?? 'Unlimited' }}</p>
                        </div>
                    </div>

                    @if($event->meeting_url)
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Meeting URL</label>
                            <a href="{{ $event->meeting_url }}" target="_blank" class="text-primary-400 hover:text-primary-300 break-all">
                                {{ $event->meeting_url }}
                            </a>
                        </div>
                    @endif

                    @if($event->recording_url)
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Recording URL</label>
                            <a href="{{ $event->recording_url }}" target="_blank" class="text-primary-400 hover:text-primary-300 break-all">
                                {{ $event->recording_url }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Event RSVPs -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event RSVPs</h3>
                
                @if($event->rsvps->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Attendee</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">RSVP Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-700">
                                @foreach($event->rsvps->take(10) as $rsvp)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-sm font-medium text-white mr-3">
                                                    {{ substr($rsvp->user->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-white">{{ $rsvp->user->name }}</div>
                                                    <div class="text-sm text-gray-400">{{ $rsvp->user->email }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            {{ $rsvp->created_at->format('M j, Y g:i A') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Confirmed
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <form method="POST" action="{{ route('admin.events.rsvps.destroy', [$event, $rsvp]) }}" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-400 hover:text-red-300" 
                                                        onclick="return confirm('Are you sure you want to remove this RSVP?')">
                                                    Remove
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($event->rsvps->count() > 10)
                        <div class="mt-4 text-center">
                            <a href="{{ route('admin.events.rsvps', $event) }}" class="text-primary-400 hover:text-primary-300">
                                View all {{ $event->rsvps->count() }} RSVPs
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-8">
                        <div class="text-gray-400">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-300">No RSVPs</h3>
                            <p class="mt-1 text-sm text-gray-400">No one has RSVP'd to this event yet.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Event Stats -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total RSVPs</span>
                        <span class="text-white font-medium">{{ $event->rsvps->count() }}</span>
                    </div>
                    @if($event->max_attendees)
                        <div class="flex justify-between">
                            <span class="text-gray-400">Capacity</span>
                            <span class="text-white font-medium">
                                {{ round($event->rsvps->count() / $event->max_attendees * 100) }}%
                            </span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full" style="width: {{ min(100, round($event->rsvps->count() / $event->max_attendees * 100)) }}%"></div>
                        </div>
                    @endif
                    <div class="flex justify-between">
                        <span class="text-gray-400">Created</span>
                        <span class="text-white font-medium">{{ $event->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Last Updated</span>
                        <span class="text-white font-medium">{{ $event->updated_at->format('M j, Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    @if($event->start_time > now())
                        <button onclick="sendReminders()" class="w-full btn-outline-primary">
                            Send Reminders
                        </button>
                    @endif
                    
                    @if($event->meeting_url)
                        <a href="{{ $event->meeting_url }}" target="_blank" class="w-full btn-outline-primary">
                            Join Meeting
                        </a>
                    @endif
                    
                    <a href="{{ route('admin.events.export', $event) }}" class="w-full btn-outline-secondary">
                        Export Attendees
                    </a>
                    
                    <form method="POST" action="{{ route('admin.events.destroy', $event) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this event? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full btn-outline-danger">
                            Delete Event
                        </button>
                    </form>
                </div>
            </div>

            <!-- Event Timeline -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event Timeline</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                        <span class="text-gray-300">Event created</span>
                        <span class="text-gray-400 ml-auto">{{ $event->created_at->format('M j') }}</span>
                    </div>
                    
                    @if($event->start_time > now())
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                            <span class="text-gray-300">Event starts</span>
                            <span class="text-gray-400 ml-auto">{{ $event->start_time->format('M j') }}</span>
                        </div>
                    @else
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                            <span class="text-gray-300">Event started</span>
                            <span class="text-gray-400 ml-auto">{{ $event->start_time->format('M j') }}</span>
                        </div>
                    @endif
                    
                    @if($event->end_time <= now())
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                            <span class="text-gray-300">Event ended</span>
                            <span class="text-gray-400 ml-auto">{{ $event->end_time->format('M j') }}</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendReminders() {
    if (confirm('Send reminder notifications to all RSVP\'d attendees?')) {
        fetch(`/admin/events/{{ $event->id }}/send-reminders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Reminders sent successfully!');
            } else {
                alert('Failed to send reminders. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to send reminders. Please try again.');
        });
    }
}
</script>
@endsection
