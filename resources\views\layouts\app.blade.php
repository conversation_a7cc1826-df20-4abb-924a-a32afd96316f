<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', config('app.name', 'The Real World'))</title>
    <meta name="description" content="@yield('description', 'Join The Real World and learn real-world skills to build wealth and achieve financial freedom.')">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=montserrat:400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    @stack('styles')
</head>
<body class="font-sans antialiased bg-gray-900 text-white">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800" x-data="{ open: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ route('home') }}" class="flex items-center">
                        <span class="text-2xl font-bold text-gradient font-display">The Real World</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'text-red-400' : 'text-gray-300 hover:text-white' }}">
                            Home
                        </a>
                        <a href="{{ route('campuses') }}" class="nav-link {{ request()->routeIs('campuses') ? 'text-red-400' : 'text-gray-300 hover:text-white' }}">
                            Campuses
                        </a>
                        <a href="{{ route('pricing') }}" class="nav-link {{ request()->routeIs('pricing') ? 'text-red-400' : 'text-gray-300 hover:text-white' }}">
                            Pricing
                        </a>
                        <a href="{{ route('testimonials') }}" class="nav-link {{ request()->routeIs('testimonials') ? 'text-red-400' : 'text-gray-300 hover:text-white' }}">
                            Testimonials
                        </a>
                        <a href="{{ route('faq') }}" class="nav-link {{ request()->routeIs('faq') ? 'text-red-400' : 'text-gray-300 hover:text-white' }}">
                            FAQ
                        </a>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:block">
                    @auth
                        <div class="flex items-center space-x-4">
                            @if(auth()->user()->subscription_plan === 'free' || !auth()->user()->subscription_plan)
                                <a href="{{ route('pricing') }}" class="btn-primary">
                                    Upgrade
                                </a>
                            @endif
                            <a href="{{ route('dashboard.index') }}" class="btn-secondary">
                                Dashboard
                            </a>
                            <form method="POST" action="{{ route('logout') }}" class="inline">
                                @csrf
                                <button type="submit" class="text-gray-400 hover:text-white">
                                    Logout
                                </button>
                            </form>
                        </div>
                    @else
                        <div class="flex items-center space-x-4">
                            <a href="{{ route('login') }}" class="text-gray-300 hover:text-white">
                                Login
                            </a>
                            <a href="{{ route('register') }}" class="btn-primary">
                                Join Now
                            </a>
                        </div>
                    @endauth
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="open = !open" class="text-gray-400 hover:text-white focus:outline-none focus:text-white">
                        <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                            <path :class="{'hidden': open, 'inline-flex': !open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            <path :class="{'hidden': !open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div :class="{'block': open, 'hidden': !open}" class="hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800">
                <a href="{{ route('home') }}" class="block px-3 py-2 text-gray-300 hover:text-white">Home</a>
                <a href="{{ route('campuses') }}" class="block px-3 py-2 text-gray-300 hover:text-white">Campuses</a>
                <a href="{{ route('pricing') }}" class="block px-3 py-2 text-gray-300 hover:text-white">Pricing</a>
                <a href="{{ route('testimonials') }}" class="block px-3 py-2 text-gray-300 hover:text-white">Testimonials</a>
                <a href="{{ route('faq') }}" class="block px-3 py-2 text-gray-300 hover:text-white">FAQ</a>
                
                @auth
                    <a href="{{ route('dashboard.index') }}" class="block px-3 py-2 text-gray-300 hover:text-white">Dashboard</a>
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit" class="block w-full text-left px-3 py-2 text-gray-300 hover:text-white">
                            Logout
                        </button>
                    </form>
                @else
                    <a href="{{ route('login') }}" class="block px-3 py-2 text-gray-300 hover:text-white">Login</a>
                    <a href="{{ route('register') }}" class="block px-3 py-2 text-red-400 hover:text-red-300">Join Now</a>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-gray-700">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo & Description -->
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-2xl font-bold text-gradient font-display mb-4">The Real World</h3>
                    <p class="text-gray-400 mb-4">
                        Join the ultimate online university where you learn real-world skills to build wealth and achieve financial freedom.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.928-.875 2.079-1.365 3.323-1.365s2.395.49 3.323 1.365c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.928.796-2.079 1.297-3.323 1.297zm7.83-9.405c-.49 0-.928-.438-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .49-.438.928-.928.928zm-7.83 1.297c1.297 0 2.343 1.046 2.343 2.343s-1.046 2.343-2.343 2.343-2.343-1.046-2.343-2.343 1.046-2.343 2.343-2.343z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ route('campuses') }}" class="text-gray-400 hover:text-white">Campuses</a></li>
                        <li><a href="{{ route('pricing') }}" class="text-gray-400 hover:text-white">Pricing</a></li>
                        <li><a href="{{ route('testimonials') }}" class="text-gray-400 hover:text-white">Testimonials</a></li>
                        <li><a href="{{ route('faq') }}" class="text-gray-400 hover:text-white">FAQ</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2">
                        <li><a href="{{ route('terms') }}" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                        <li><a href="{{ route('privacy') }}" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        <li><a href="{{ route('earnings-disclaimer') }}" class="text-gray-400 hover:text-white">Earnings Disclaimer</a></li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-700">
                <p class="text-center text-gray-400">
                    &copy; {{ date('Y') }} The Real World. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    @stack('scripts')
</body>
</html>
