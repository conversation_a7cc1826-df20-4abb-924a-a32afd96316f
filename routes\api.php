<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// API Routes for authenticated users
Route::middleware(['auth:sanctum', 'api.rate_limit:120,1'])->group(function () {
    // User API
    Route::prefix('user')->group(function () {
        Route::get('/stats', function (Request $request) {
            return app(\App\Services\UserService::class)->getUserStats($request->user());
        });
        Route::get('/daily-tasks', function (Request $request) {
            return app(\App\Services\UserService::class)->getUserDailyTasks($request->user());
        });
        Route::post('/tasks/{task}/complete', function (Request $request, $taskId) {
            return app(\App\Services\UserService::class)->completeTask($request->user(), $taskId);
        });
    });

    // Notifications API
    Route::prefix('notifications')->group(function () {
        Route::get('/', function (Request $request) {
            return app(\App\Services\NotificationService::class)->getRecentNotifications($request->user());
        });
        Route::get('/unread', function (Request $request) {
            return app(\App\Services\NotificationService::class)->getUnreadNotifications($request->user());
        });
        Route::post('/{notification}/read', function (Request $request, $notificationId) {
            return app(\App\Services\NotificationService::class)->markAsRead($notificationId);
        });
        Route::post('/mark-all-read', function (Request $request) {
            return app(\App\Services\NotificationService::class)->markAllAsRead($request->user());
        });
    });

    // Search API
    Route::prefix('search')->group(function () {
        Route::get('/quick', function (Request $request) {
            $query = $request->get('q');
            $type = $request->get('type', 'all');

            // Quick search implementation
            $results = [];

            if ($type === 'all' || $type === 'courses') {
                $courses = \App\Models\Course::where('title', 'like', "%{$query}%")
                    ->where('is_published', true)
                    ->take(5)
                    ->get(['id', 'title', 'description']);
                $results['courses'] = $courses;
            }

            if ($type === 'all' || $type === 'events') {
                $events = \App\Models\Event::where('title', 'like', "%{$query}%")
                    ->where('is_active', true)
                    ->take(5)
                    ->get(['id', 'title', 'description', 'start_time']);
                $results['events'] = $events;
            }

            return $results;
        });
    });

    // Leaderboard API
    Route::get('/leaderboard/{type?}', function (Request $request, $type = 'xp') {
        return app(\App\Services\AchievementService::class)->getLeaderboard($type, 20);
    });

    // Course Progress API
    Route::prefix('courses')->group(function () {
        Route::post('/{course}/enroll', function (Request $request, \App\Models\Course $course) {
            $enrollment = app(\App\Services\CourseService::class)->enrollUser($request->user(), $course);
            return ['success' => true, 'enrollment' => $enrollment];
        });

        Route::get('/{course}/progress', function (Request $request, \App\Models\Course $course) {
            $enrollment = $course->getUserEnrollment($request->user());
            return $enrollment ? ['progress' => $enrollment->progress_percentage] : ['progress' => 0];
        });
    });

    // Event RSVP API
    Route::prefix('events')->group(function () {
        Route::post('/{event}/rsvp', function (Request $request, \App\Models\Event $event) {
            $success = app(\App\Services\EventService::class)->rsvpToEvent($request->user(), $event);
            return ['success' => $success];
        });

        Route::delete('/{event}/rsvp', function (Request $request, \App\Models\Event $event) {
            $success = app(\App\Services\EventService::class)->cancelRsvp($request->user(), $event);
            return ['success' => $success];
        });
    });

    // Chat API
    Route::prefix('chat')->group(function () {
        Route::get('/messages', function (Request $request) {
            return \App\Models\ChatMessage::with('user')
                ->orderBy('created_at', 'desc')
                ->take(50)
                ->get();
        });

        Route::post('/messages', function (Request $request) {
            $request->validate([
                'message' => 'required|string|max:1000',
            ]);

            $message = \App\Models\ChatMessage::create([
                'user_id' => $request->user()->id,
                'message' => $request->message,
            ]);

            // Check for chat achievements
            app(\App\Services\AchievementService::class)->checkAchievements($request->user(), 'chat_message');

            return $message->load('user');
        });
    });

    // Dashboard Stats API
    Route::get('/dashboard/stats', function (Request $request) {
        $user = $request->user();

        return [
            'user' => [
                'name' => $user->name,
                'level' => $user->level,
                'xp' => $user->xp,
                'login_streak' => $user->login_streak ?? 0,
            ],
            'progress' => [
                'courses_enrolled' => $user->courseEnrollments()->count(),
                'courses_completed' => $user->courseEnrollments()->where('progress_percentage', 100)->count(),
                'events_attended' => $user->eventRsvps()->where('attended', true)->count(),
                'badges_earned' => $user->badges()->count(),
            ],
            'recent_activity' => [
                'recent_courses' => $user->courseEnrollments()
                    ->with('course')
                    ->orderBy('last_accessed_at', 'desc')
                    ->take(3)
                    ->get(),
                'upcoming_events' => \App\Models\Event::whereHas('rsvps', function ($query) use ($user) {
                        $query->where('user_id', $user->id);
                    })
                    ->where('start_time', '>', now())
                    ->orderBy('start_time', 'asc')
                    ->take(3)
                    ->get(),
            ],
        ];
    });

    // Notification routes
    Route::prefix('notifications')->group(function () {
        Route::get('/count', function (Request $request) {
            return [
                'count' => $request->user()->unreadNotifications()->count()
            ];
        });

        Route::post('/mark-all-read', function (Request $request) {
            $request->user()->unreadNotifications()->update(['read_at' => now()]);
            return ['success' => true];
        });

        Route::post('/{notification}/mark-read', function (Request $request, $notificationId) {
            $notification = $request->user()->notifications()->find($notificationId);
            if ($notification) {
                $notification->markAsRead();
            }
            return ['success' => true];
        });
    });

    // Chat routes
    Route::prefix('chat')->group(function () {
        Route::get('/rooms', function (Request $request) {
            return \App\Models\ChatRoom::where('is_active', true)
                ->withCount('messages')
                ->orderBy('name')
                ->get();
        });

        Route::get('/rooms/{room}/messages', function (Request $request, \App\Models\ChatRoom $room) {
            return $room->messages()
                ->with('user:id,name')
                ->orderBy('created_at', 'desc')
                ->take(50)
                ->get()
                ->reverse()
                ->values();
        });

        Route::post('/rooms/{room}/messages', function (Request $request, \App\Models\ChatRoom $room) {
            $request->validate([
                'message' => 'required|string|max:1000',
                'reply_to_id' => 'nullable|exists:chat_messages,id'
            ]);

            $message = $room->messages()->create([
                'user_id' => $request->user()->id,
                'message' => $request->message,
                'reply_to_id' => $request->reply_to_id,
                'type' => 'text'
            ]);

            return $message->load('user:id,name');
        });

        Route::put('/messages/{message}', function (Request $request, \App\Models\ChatMessage $message) {
            if ($message->user_id !== $request->user()->id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            if (!$message->canBeEditedBy($request->user())) {
                return response()->json(['error' => 'Message can no longer be edited'], 422);
            }

            $request->validate(['message' => 'required|string|max:1000']);

            $message->update(['message' => $request->message]);
            $message->markAsEdited();

            return $message->load('user:id,name');
        });

        Route::delete('/messages/{message}', function (Request $request, \App\Models\ChatMessage $message) {
            if (!$message->canBeDeletedBy($request->user())) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $message->delete();
            return ['success' => true];
        });
    });

    // Performance API
    Route::prefix('performance')->group(function () {
        Route::get('/cache-stats', function () {
            return app(\App\Services\CacheService::class)->getCacheStats();
        });

        Route::post('/clear-cache', function () {
            return app(\App\Services\CacheService::class)->clearAllCaches();
        });
    });
});
