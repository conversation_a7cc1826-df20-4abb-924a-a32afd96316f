<?php $__env->startSection('title', $user->name . ' - User Details - Admin'); ?>
<?php $__env->startSection('page-title', 'User Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
            <a href="<?php echo e(route('admin.users.index')); ?>" class="text-gray-400 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold"><?php echo e($user->name); ?></h1>
                <p class="text-gray-400">User ID: <?php echo e($user->id); ?></p>
            </div>
        </div>
        
        <div class="flex space-x-3">
            <button onclick="openEditModal()" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit User
            </button>
            
            <?php if($user->is_active): ?>
                <button onclick="suspendUser()" class="btn-danger">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                    Suspend
                </button>
            <?php else: ?>
                <button onclick="activateUser()" class="btn-success">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Activate
                </button>
            <?php endif; ?>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- User Profile -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="card">
                <h2 class="text-lg font-semibold mb-6">Basic Information</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Full Name</label>
                        <div class="text-white"><?php echo e($user->name); ?></div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Email Address</label>
                        <div class="text-white"><?php echo e($user->email); ?></div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Registration Date</label>
                        <div class="text-white"><?php echo e($user->created_at->format('M j, Y g:i A')); ?></div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Last Login</label>
                        <div class="text-white">
                            <?php echo e($user->last_login_at ? $user->last_login_at->diffForHumans() : 'Never'); ?>

                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Status</label>
                        <div>
                            <?php if($user->is_active): ?>
                                <span class="bg-green-600 text-white px-2 py-1 rounded text-sm">Active</span>
                            <?php else: ?>
                                <span class="bg-red-600 text-white px-2 py-1 rounded text-sm">Suspended</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Email Verified</label>
                        <div>
                            <?php if($user->email_verified_at): ?>
                                <span class="bg-green-600 text-white px-2 py-1 rounded text-sm">Verified</span>
                            <?php else: ?>
                                <span class="bg-yellow-600 text-white px-2 py-1 rounded text-sm">Unverified</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Information -->
            <div class="card">
                <h2 class="text-lg font-semibold mb-6">Subscription Details</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Current Plan</label>
                        <div class="text-white">
                            <span class="capitalize"><?php echo e($user->subscription_plan ?? 'Free'); ?></span>
                            <?php if($user->subscription_plan && $user->subscription_plan !== 'free'): ?>
                                <span class="ml-2 bg-primary-600 text-white px-2 py-1 rounded text-xs">Premium</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-1">Subscription Status</label>
                        <div class="text-white">
                            <?php if($user->subscription_plan && $user->subscription_plan !== 'free'): ?>
                                <span class="text-green-400">Active</span>
                            <?php else: ?>
                                <span class="text-gray-400">Free Plan</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if($user->subscription_started_at): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-1">Subscription Started</label>
                            <div class="text-white"><?php echo e($user->subscription_started_at->format('M j, Y')); ?></div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($user->subscription_ends_at): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-1">Next Billing</label>
                            <div class="text-white"><?php echo e($user->subscription_ends_at->format('M j, Y')); ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Activity Log -->
            <div class="card">
                <h2 class="text-lg font-semibold mb-6">Recent Activity</h2>
                
                <div class="space-y-4">
                    <?php $__empty_1 = true; $__currentLoopData = $user->activities()->latest()->take(10)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex items-center space-x-4 p-3 bg-gray-800 rounded-lg">
                            <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <div class="text-white"><?php echo e($activity->description); ?></div>
                                <div class="text-gray-400 text-sm"><?php echo e($activity->created_at->diffForHumans()); ?></div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8">
                            <div class="text-4xl mb-4">📝</div>
                            <p class="text-gray-400">No recent activity</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Stats -->
        <div class="space-y-6">
            <!-- User Stats -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">User Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Level</span>
                        <span class="font-semibold"><?php echo e($user->level ?? 1); ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total XP</span>
                        <span class="font-semibold"><?php echo e(number_format($user->xp ?? 0)); ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Login Streak</span>
                        <span class="font-semibold"><?php echo e($user->login_streak ?? 0); ?> days</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Courses Enrolled</span>
                        <span class="font-semibold"><?php echo e($user->courseEnrollments()->count()); ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Courses Completed</span>
                        <span class="font-semibold"><?php echo e($user->courseEnrollments()->where('progress_percentage', 100)->count()); ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Events Attended</span>
                        <span class="font-semibold"><?php echo e($user->eventRsvps()->where('attended', true)->count()); ?></span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Badges Earned</span>
                        <span class="font-semibold"><?php echo e($user->badges()->count()); ?></span>
                    </div>
                </div>
            </div>

            <!-- Roles & Permissions -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Roles & Permissions</h3>
                
                <div class="space-y-3">
                    <?php $__empty_1 = true; $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="flex items-center justify-between">
                            <span class="capitalize"><?php echo e($role->name); ?></span>
                            <button onclick="removeRole('<?php echo e($role->name); ?>')" class="text-red-400 hover:text-red-300">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <p class="text-gray-400 text-sm">No roles assigned</p>
                    <?php endif; ?>
                </div>
                
                <div class="mt-4">
                    <select id="roleSelect" class="input-field mb-2">
                        <option value="">Select role to add...</option>
                        <?php $__currentLoopData = \Spatie\Permission\Models\Role::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(!$user->hasRole($role->name)): ?>
                                <option value="<?php echo e($role->name); ?>"><?php echo e(ucfirst($role->name)); ?></option>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <button onclick="addRole()" class="btn-secondary w-full text-sm">Add Role</button>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                
                <div class="space-y-2">
                    <button onclick="sendPasswordReset()" class="btn-secondary w-full text-sm">
                        Send Password Reset
                    </button>
                    
                    <button onclick="resendVerification()" class="btn-secondary w-full text-sm">
                        Resend Email Verification
                    </button>
                    
                    <button onclick="loginAsUser()" class="btn-secondary w-full text-sm">
                        Login as User
                    </button>
                    
                    <button onclick="viewUserActivity()" class="btn-secondary w-full text-sm">
                        View Full Activity Log
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Edit User</h3>
        
        <form id="editForm" class="space-y-4">
            <div>
                <label class="block text-sm font-medium mb-1">Name</label>
                <input type="text" id="editName" value="<?php echo e($user->name); ?>" class="input-field">
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">Email</label>
                <input type="email" id="editEmail" value="<?php echo e($user->email); ?>" class="input-field">
            </div>
            
            <div>
                <label class="block text-sm font-medium mb-1">Subscription Plan</label>
                <select id="editPlan" class="input-field">
                    <option value="free" <?php echo e($user->subscription_plan === 'free' ? 'selected' : ''); ?>>Free</option>
                    <option value="prosper" <?php echo e($user->subscription_plan === 'prosper' ? 'selected' : ''); ?>>Prosper</option>
                    <option value="conquer" <?php echo e($user->subscription_plan === 'conquer' ? 'selected' : ''); ?>>Conquer</option>
                    <option value="champions" <?php echo e($user->subscription_plan === 'champions' ? 'selected' : ''); ?>>Champions</option>
                </select>
            </div>
            
            <div class="flex space-x-4">
                <button type="button" onclick="closeEditModal()" class="btn-secondary flex-1">Cancel</button>
                <button type="submit" class="btn-primary flex-1">Save Changes</button>
            </div>
        </form>
    </div>
</div>

<script>
function openEditModal() {
    document.getElementById('editModal').classList.remove('hidden');
    document.getElementById('editModal').classList.add('flex');
}

function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
    document.getElementById('editModal').classList.remove('flex');
}

function suspendUser() {
    if (confirm('Are you sure you want to suspend this user?')) {
        fetch(`/admin/users/<?php echo e($user->id); ?>/suspend`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        }).then(() => location.reload());
    }
}

function activateUser() {
    fetch(`/admin/users/<?php echo e($user->id); ?>/activate`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    }).then(() => location.reload());
}

function addRole() {
    const role = document.getElementById('roleSelect').value;
    if (role) {
        fetch(`/admin/users/<?php echo e($user->id); ?>/roles`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ role: role })
        }).then(() => location.reload());
    }
}

function removeRole(role) {
    if (confirm(`Remove ${role} role from this user?`)) {
        fetch(`/admin/users/<?php echo e($user->id); ?>/roles/${role}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        }).then(() => location.reload());
    }
}

function sendPasswordReset() {
    fetch(`/admin/users/<?php echo e($user->id); ?>/password-reset`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    }).then(() => alert('Password reset email sent!'));
}

function resendVerification() {
    fetch(`/admin/users/<?php echo e($user->id); ?>/resend-verification`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    }).then(() => alert('Verification email sent!'));
}

function loginAsUser() {
    if (confirm('Login as this user? This will log you out of the admin panel.')) {
        window.open(`/admin/users/<?php echo e($user->id); ?>/login-as`, '_blank');
    }
}

function viewUserActivity() {
    window.location.href = `/admin/users/<?php echo e($user->id); ?>/activity`;
}

document.getElementById('editForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const data = {
        name: document.getElementById('editName').value,
        email: document.getElementById('editEmail').value,
        subscription_plan: document.getElementById('editPlan').value
    };
    
    fetch(`/admin/users/<?php echo e($user->id); ?>`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    }).then(() => {
        closeEditModal();
        location.reload();
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/users/show.blade.php ENDPATH**/ ?>