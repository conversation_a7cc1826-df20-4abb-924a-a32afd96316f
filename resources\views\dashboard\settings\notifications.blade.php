@extends('layouts.dashboard')

@section('title', 'Notification Settings')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">Notification Settings</h1>
        <p class="text-gray-400">Manage how and when you receive notifications</p>
    </div>

    <!-- Settings Form -->
    <div class="bg-gray-800 rounded-lg p-6">
        <form method="POST" action="{{ route('dashboard.settings.notifications.update') }}">
            @csrf
            @method('PUT')

            <!-- Email Notifications -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-white mb-4">Email Notifications</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Course Updates</h4>
                            <p class="text-gray-400 text-sm">Get notified when new courses are added or updated</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_course_updates" value="1" 
                                   {{ auth()->user()->notification_preferences['email_course_updates'] ?? true ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Event Reminders</h4>
                            <p class="text-gray-400 text-sm">Receive reminders about upcoming events you've RSVP'd to</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_event_reminders" value="1" 
                                   {{ auth()->user()->notification_preferences['email_event_reminders'] ?? true ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Achievement Notifications</h4>
                            <p class="text-gray-400 text-sm">Get notified when you earn badges or reach milestones</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_achievements" value="1" 
                                   {{ auth()->user()->notification_preferences['email_achievements'] ?? true ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Weekly Summary</h4>
                            <p class="text-gray-400 text-sm">Receive a weekly summary of your progress and activity</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_weekly_summary" value="1" 
                                   {{ auth()->user()->notification_preferences['email_weekly_summary'] ?? true ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Marketing Emails</h4>
                            <p class="text-gray-400 text-sm">Receive promotional emails and special offers</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="email_marketing" value="1" 
                                   {{ auth()->user()->notification_preferences['email_marketing'] ?? false ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Push Notifications -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-white mb-4">Push Notifications</h3>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Browser Notifications</h4>
                            <p class="text-gray-400 text-sm">Receive notifications in your browser when the site is open</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="push_browser" value="1" 
                                   {{ auth()->user()->notification_preferences['push_browser'] ?? true ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Chat Messages</h4>
                            <p class="text-gray-400 text-sm">Get notified when someone mentions you in chat</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" name="push_chat_mentions" value="1" 
                                   {{ auth()->user()->notification_preferences['push_chat_mentions'] ?? true ? 'checked' : '' }}
                                   class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Notification Frequency -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold text-white mb-4">Notification Frequency</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Email Digest Frequency</label>
                        <select name="email_digest_frequency" 
                                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <option value="immediate" {{ (auth()->user()->notification_preferences['email_digest_frequency'] ?? 'daily') === 'immediate' ? 'selected' : '' }}>
                                Immediate
                            </option>
                            <option value="daily" {{ (auth()->user()->notification_preferences['email_digest_frequency'] ?? 'daily') === 'daily' ? 'selected' : '' }}>
                                Daily Digest
                            </option>
                            <option value="weekly" {{ (auth()->user()->notification_preferences['email_digest_frequency'] ?? 'daily') === 'weekly' ? 'selected' : '' }}>
                                Weekly Digest
                            </option>
                            <option value="never" {{ (auth()->user()->notification_preferences['email_digest_frequency'] ?? 'daily') === 'never' ? 'selected' : '' }}>
                                Never
                            </option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Quiet Hours</label>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-xs text-gray-400 mb-1">From</label>
                                <input type="time" 
                                       name="quiet_hours_start" 
                                       value="{{ auth()->user()->notification_preferences['quiet_hours_start'] ?? '22:00' }}"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-xs text-gray-400 mb-1">To</label>
                                <input type="time" 
                                       name="quiet_hours_end" 
                                       value="{{ auth()->user()->notification_preferences['quiet_hours_end'] ?? '08:00' }}"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>
                        </div>
                        <p class="text-xs text-gray-400 mt-1">No notifications will be sent during these hours</p>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('dashboard.settings.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Save Preferences
                </button>
            </div>
        </form>
    </div>

    <!-- Test Notification -->
    <div class="mt-6 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Test Notifications</h3>
        <p class="text-gray-400 mb-4">Send yourself a test notification to make sure everything is working correctly.</p>
        <button type="button" 
                onclick="sendTestNotification()" 
                class="btn-outline-primary">
            Send Test Notification
        </button>
    </div>
</div>

<script>
function sendTestNotification() {
    fetch('/api/notifications/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Test notification sent! Check your email and browser notifications.');
        } else {
            alert('Failed to send test notification. Please try again.');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to send test notification. Please try again.');
    });
}

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}
</script>
@endsection
