<?php

namespace Tests\Feature;

use App\Models\Event;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EventTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_guest_can_view_published_events(): void
    {
        Event::factory()->published()->upcoming()->count(3)->create();
        Event::factory()->unpublished()->count(2)->create();

        $response = $this->get('/events');

        $response->assertStatus(200);
        $response->assertViewIs('events.index');
        $response->assertViewHas('events');
        
        // Should only see published events
        $events = $response->viewData('events');
        $this->assertCount(3, $events);
    }

    public function test_guest_can_view_individual_published_event(): void
    {
        $event = Event::factory()->published()->upcoming()->create();

        $response = $this->get("/events/{$event->slug}");

        $response->assertStatus(200);
        $response->assertViewIs('events.show');
        $response->assertViewHas('event', $event);
    }

    public function test_guest_cannot_view_unpublished_event(): void
    {
        $event = Event::factory()->unpublished()->create();

        $response = $this->get("/events/{$event->slug}");

        $response->assertStatus(404);
    }

    public function test_authenticated_user_can_rsvp_to_event(): void
    {
        $user = User::factory()->create();
        $event = Event::factory()->published()->upcoming()->create();

        $response = $this->actingAs($user)->post("/events/{$event->slug}/rsvp");

        $response->assertRedirect();
        $this->assertDatabaseHas('event_rsvps', [
            'user_id' => $user->id,
            'event_id' => $event->id,
            'status' => 'confirmed',
        ]);
    }

    public function test_guest_cannot_rsvp_to_event(): void
    {
        $event = Event::factory()->published()->upcoming()->create();

        $response = $this->post("/events/{$event->slug}/rsvp");

        $response->assertRedirect('/login');
    }

    public function test_user_cannot_rsvp_to_premium_event_without_subscription(): void
    {
        $user = User::factory()->create();
        $event = Event::factory()->published()->upcoming()->premium()->create();

        $response = $this->actingAs($user)->post("/events/{$event->slug}/rsvp");

        $response->assertRedirect('/pricing');
        $this->assertDatabaseMissing('event_rsvps', [
            'user_id' => $user->id,
            'event_id' => $event->id,
        ]);
    }

    public function test_user_cannot_rsvp_to_past_event(): void
    {
        $user = User::factory()->create();
        $event = Event::factory()->published()->past()->create();

        $response = $this->actingAs($user)->post("/events/{$event->slug}/rsvp");

        $response->assertSessionHasErrors();
        $this->assertDatabaseMissing('event_rsvps', [
            'user_id' => $user->id,
            'event_id' => $event->id,
        ]);
    }

    public function test_user_cannot_rsvp_to_full_event(): void
    {
        $user = User::factory()->create();
        $event = Event::factory()->published()->upcoming()->create(['max_attendees' => 1]);
        
        // Fill the event
        $otherUser = User::factory()->create();
        $event->rsvps()->create(['user_id' => $otherUser->id, 'status' => 'confirmed']);

        $response = $this->actingAs($user)->post("/events/{$event->slug}/rsvp");

        $response->assertSessionHasErrors();
        $this->assertDatabaseMissing('event_rsvps', [
            'user_id' => $user->id,
            'event_id' => $event->id,
        ]);
    }

    public function test_user_can_cancel_rsvp(): void
    {
        $user = User::factory()->create();
        $event = Event::factory()->published()->upcoming()->create();
        
        // First RSVP
        $event->rsvps()->create(['user_id' => $user->id, 'status' => 'confirmed']);

        $response = $this->actingAs($user)->delete("/events/{$event->slug}/rsvp");

        $response->assertRedirect();
        $this->assertDatabaseMissing('event_rsvps', [
            'user_id' => $user->id,
            'event_id' => $event->id,
        ]);
    }

    public function test_admin_can_view_event_management_page(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $response = $this->actingAs($admin)->get('/admin/events');

        $response->assertStatus(200);
        $response->assertViewIs('admin.events.index');
    }

    public function test_admin_can_create_event(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $eventData = [
            'title' => 'Test Event',
            'description' => 'This is a test event description that is long enough to pass validation.',
            'category' => 'workshop',
            'start_time' => Carbon::now()->addDays(7)->format('Y-m-d\TH:i'),
            'end_time' => Carbon::now()->addDays(7)->addHours(2)->format('Y-m-d\TH:i'),
            'max_attendees' => 50,
            'is_published' => true,
        ];

        $response = $this->actingAs($admin)->post('/admin/events', $eventData);

        $response->assertRedirect();
        $this->assertDatabaseHas('events', [
            'title' => 'Test Event',
            'category' => 'workshop',
        ]);
    }

    public function test_admin_can_update_event(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        $event = Event::factory()->upcoming()->create();

        $updateData = [
            'title' => 'Updated Event Title',
            'description' => 'This is an updated event description that is long enough to pass validation.',
            'category' => 'webinar',
            'start_time' => $event->start_time->format('Y-m-d\TH:i'),
            'end_time' => $event->end_time->format('Y-m-d\TH:i'),
            'max_attendees' => 100,
            'is_published' => false,
        ];

        $response = $this->actingAs($admin)->put("/admin/events/{$event->id}", $updateData);

        $response->assertRedirect();
        $this->assertDatabaseHas('events', [
            'id' => $event->id,
            'title' => 'Updated Event Title',
            'category' => 'webinar',
        ]);
    }

    public function test_admin_can_delete_event(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('admin');
        $event = Event::factory()->create();

        $response = $this->actingAs($admin)->delete("/admin/events/{$event->id}");

        $response->assertRedirect();
        $this->assertDatabaseMissing('events', [
            'id' => $event->id,
        ]);
    }

    public function test_event_search_functionality(): void
    {
        Event::factory()->published()->upcoming()->create(['title' => 'Laravel Workshop']);
        Event::factory()->published()->upcoming()->create(['title' => 'React Webinar']);
        Event::factory()->published()->upcoming()->create(['title' => 'Python Masterclass']);

        $response = $this->get('/events?search=Laravel');

        $response->assertStatus(200);
        $response->assertSee('Laravel Workshop');
        $response->assertDontSee('React Webinar');
        $response->assertDontSee('Python Masterclass');
    }

    public function test_event_filtering_by_category(): void
    {
        Event::factory()->published()->upcoming()->create(['category' => 'workshop']);
        Event::factory()->published()->upcoming()->create(['category' => 'webinar']);
        Event::factory()->published()->upcoming()->create(['category' => 'networking']);

        $response = $this->get('/events?category=workshop');

        $response->assertStatus(200);
        $events = $response->viewData('events');
        $this->assertTrue($events->every(fn($event) => $event->category === 'workshop'));
    }

    public function test_upcoming_events_appear_first(): void
    {
        $futureEvent = Event::factory()->published()->create([
            'start_time' => Carbon::now()->addDays(10),
            'end_time' => Carbon::now()->addDays(10)->addHours(2),
        ]);
        $soonEvent = Event::factory()->published()->create([
            'start_time' => Carbon::now()->addDays(1),
            'end_time' => Carbon::now()->addDays(1)->addHours(2),
        ]);

        $response = $this->get('/events');

        $response->assertStatus(200);
        $events = $response->viewData('events');
        $this->assertEquals($soonEvent->id, $events->first()->id);
    }

    public function test_event_capacity_is_enforced(): void
    {
        $event = Event::factory()->published()->upcoming()->create(['max_attendees' => 2]);
        $users = User::factory()->count(3)->create();

        // First two users should be able to RSVP
        foreach ($users->take(2) as $user) {
            $response = $this->actingAs($user)->post("/events/{$event->slug}/rsvp");
            $response->assertRedirect();
        }

        // Third user should be rejected
        $response = $this->actingAs($users->last())->post("/events/{$event->slug}/rsvp");
        $response->assertSessionHasErrors();
    }

    public function test_event_rsvp_count_is_tracked(): void
    {
        $event = Event::factory()->published()->upcoming()->create();
        $users = User::factory()->count(3)->create();

        foreach ($users as $user) {
            $this->actingAs($user)->post("/events/{$event->slug}/rsvp");
        }

        $event->refresh();
        $this->assertEquals(3, $event->rsvps()->count());
    }
}
