@extends('layouts.admin')

@section('title', 'Media Library')
@section('page-title', 'Media Library')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Media Library</h1>
            <p class="text-gray-400 mt-1">Manage your uploaded files and media assets</p>
        </div>
        <a href="{{ route('admin.media.create') }}" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            Upload Files
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-primary-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Files</p>
                    <p class="text-2xl font-bold text-white">{{ $files->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Images</p>
                    <p class="text-2xl font-bold text-white">{{ $files->where('type', 'image')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Videos</p>
                    <p class="text-2xl font-bold text-white">{{ $files->where('type', 'video')->count() }}</p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Documents</p>
                    <p class="text-2xl font-bold text-white">{{ $files->where('type', 'document')->count() }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter and Search -->
    <div class="bg-gray-800 rounded-lg p-6 mb-6">
        <div class="flex flex-col md:flex-row gap-4">
            <!-- Search -->
            <div class="flex-1">
                <input type="text" 
                       id="searchFiles"
                       placeholder="Search files..."
                       class="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
            </div>
            
            <!-- Filter by Type -->
            <div>
                <select id="filterType" 
                        class="px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <option value="">All Types</option>
                    <option value="image">Images</option>
                    <option value="video">Videos</option>
                    <option value="audio">Audio</option>
                    <option value="document">Documents</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <!-- View Toggle -->
            <div class="flex bg-gray-700 rounded-lg p-1">
                <button id="gridView" class="px-3 py-1 rounded text-sm font-medium bg-primary-600 text-white">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                    </svg>
                </button>
                <button id="listView" class="px-3 py-1 rounded text-sm font-medium text-gray-400 hover:text-white">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Files Grid -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
        @if($files->count() > 0)
            <!-- Grid View -->
            <div id="filesGrid" class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    @foreach($files as $file)
                        <div class="file-item bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors cursor-pointer" 
                             data-type="{{ $file['type'] }}" 
                             data-name="{{ strtolower($file['name']) }}">
                            
                            <!-- File Preview -->
                            <div class="aspect-square mb-3 bg-gray-600 rounded-lg flex items-center justify-center overflow-hidden">
                                @if($file['type'] === 'image')
                                    <img src="{{ $file['url'] }}" 
                                         alt="{{ $file['name'] }}" 
                                         class="w-full h-full object-cover rounded-lg">
                                @else
                                    <!-- File Type Icon -->
                                    <div class="text-3xl">
                                        @switch($file['type'])
                                            @case('video')
                                                🎥
                                                @break
                                            @case('audio')
                                                🎵
                                                @break
                                            @case('document')
                                                📄
                                                @break
                                            @default
                                                📁
                                        @endswitch
                                    </div>
                                @endif
                            </div>
                            
                            <!-- File Info -->
                            <div class="text-center">
                                <h3 class="text-sm font-medium text-white truncate mb-1" title="{{ $file['name'] }}">
                                    {{ Str::limit($file['name'], 15) }}
                                </h3>
                                <p class="text-xs text-gray-400">
                                    {{ number_format($file['size'] / 1024, 1) }} KB
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ date('M j, Y', $file['modified']) }}
                                </p>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex items-center justify-center space-x-2 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
                                <a href="{{ route('admin.media.show', base64_encode($file['path'])) }}" 
                                   class="text-primary-400 hover:text-primary-300 text-xs">
                                    View
                                </a>
                                <button onclick="copyUrl('{{ $file['url'] }}')" 
                                        class="text-blue-400 hover:text-blue-300 text-xs">
                                    Copy URL
                                </button>
                                <form method="POST" action="{{ route('admin.media.destroy', base64_encode($file['path'])) }}" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" 
                                            class="text-red-400 hover:text-red-300 text-xs" 
                                            onclick="return confirm('Are you sure you want to delete this file?')">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- List View (Hidden by default) -->
            <div id="filesList" class="hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-700">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">File</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Size</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Modified</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-gray-800 divide-y divide-gray-700">
                            @foreach($files as $file)
                                <tr class="file-row hover:bg-gray-700" 
                                    data-type="{{ $file['type'] }}" 
                                    data-name="{{ strtolower($file['name']) }}">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                @if($file['type'] === 'image')
                                                    <img src="{{ $file['url'] }}" 
                                                         alt="{{ $file['name'] }}" 
                                                         class="h-10 w-10 rounded object-cover">
                                                @else
                                                    <div class="h-10 w-10 bg-gray-600 rounded flex items-center justify-center text-lg">
                                                        @switch($file['type'])
                                                            @case('video') 🎥 @break
                                                            @case('audio') 🎵 @break
                                                            @case('document') 📄 @break
                                                            @default 📁
                                                        @endswitch
                                                    </div>
                                                @endif
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-white">{{ $file['name'] }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            {{ $file['type'] === 'image' ? 'bg-green-100 text-green-800' : 
                                               ($file['type'] === 'video' ? 'bg-blue-100 text-blue-800' : 
                                               ($file['type'] === 'audio' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800')) }}">
                                            {{ ucfirst($file['type']) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ number_format($file['size'] / 1024, 1) }} KB
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        {{ date('M j, Y g:i A', $file['modified']) }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex items-center justify-end space-x-2">
                                            <a href="{{ route('admin.media.show', base64_encode($file['path'])) }}" 
                                               class="text-primary-400 hover:text-primary-300">
                                                View
                                            </a>
                                            <button onclick="copyUrl('{{ $file['url'] }}')" 
                                                    class="text-blue-400 hover:text-blue-300">
                                                Copy URL
                                            </button>
                                            <form method="POST" action="{{ route('admin.media.destroy', base64_encode($file['path'])) }}" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" 
                                                        class="text-red-400 hover:text-red-300" 
                                                        onclick="return confirm('Are you sure you want to delete this file?')">
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @else
            <!-- Empty State -->
            <div class="p-12 text-center">
                <div class="text-gray-400">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-300">No files uploaded</h3>
                    <p class="mt-1 text-sm text-gray-400">Get started by uploading your first file.</p>
                    <div class="mt-6">
                        <a href="{{ route('admin.media.create') }}" class="btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            Upload Files
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>

<script>
// View toggle functionality
document.getElementById('gridView').addEventListener('click', function() {
    document.getElementById('filesGrid').classList.remove('hidden');
    document.getElementById('filesList').classList.add('hidden');
    this.classList.add('bg-primary-600', 'text-white');
    this.classList.remove('text-gray-400');
    document.getElementById('listView').classList.remove('bg-primary-600', 'text-white');
    document.getElementById('listView').classList.add('text-gray-400');
});

document.getElementById('listView').addEventListener('click', function() {
    document.getElementById('filesList').classList.remove('hidden');
    document.getElementById('filesGrid').classList.add('hidden');
    this.classList.add('bg-primary-600', 'text-white');
    this.classList.remove('text-gray-400');
    document.getElementById('gridView').classList.remove('bg-primary-600', 'text-white');
    document.getElementById('gridView').classList.add('text-gray-400');
});

// Search functionality
document.getElementById('searchFiles').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    filterFiles();
});

// Filter functionality
document.getElementById('filterType').addEventListener('change', function() {
    filterFiles();
});

function filterFiles() {
    const searchTerm = document.getElementById('searchFiles').value.toLowerCase();
    const filterType = document.getElementById('filterType').value;
    
    const items = document.querySelectorAll('.file-item, .file-row');
    
    items.forEach(item => {
        const name = item.dataset.name;
        const type = item.dataset.type;
        
        const matchesSearch = name.includes(searchTerm);
        const matchesType = !filterType || type === filterType;
        
        if (matchesSearch && matchesType) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

// Copy URL functionality
function copyUrl(url) {
    navigator.clipboard.writeText(url).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg z-50';
        toast.textContent = 'URL copied to clipboard!';
        document.body.appendChild(toast);
        
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 3000);
    });
}
</script>
@endsection
