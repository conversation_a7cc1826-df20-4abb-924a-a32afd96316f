<?php $__env->startSection('title', 'Upload Files'); ?>
<?php $__env->startSection('page-title', 'Upload Files'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Upload Files</h1>
            <p class="text-gray-400 mt-1">Upload images, videos, documents and other media files</p>
        </div>
        <a href="<?php echo e(route('admin.media.index')); ?>" class="btn-secondary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Media Library
        </a>
    </div>

    <!-- Upload Form -->
    <div class="bg-gray-800 rounded-lg p-6">
        <form method="POST" action="<?php echo e(route('admin.media.store')); ?>" enctype="multipart/form-data" id="uploadForm">
            <?php echo csrf_field(); ?>

            <!-- Drag and Drop Upload Area -->
            <div class="mb-8">
                <div id="dropZone" 
                     class="border-2 border-dashed border-gray-600 rounded-lg p-12 text-center hover:border-primary-500 transition-colors cursor-pointer">
                    <div id="dropZoneContent">
                        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <h3 class="text-lg font-medium text-white mb-2">Drop files here to upload</h3>
                        <p class="text-gray-400 mb-4">or click to browse files</p>
                        <button type="button" 
                                onclick="document.getElementById('fileInput').click()" 
                                class="btn-primary">
                            Choose Files
                        </button>
                    </div>
                    
                    <!-- Hidden File Input -->
                    <input type="file" 
                           id="fileInput" 
                           name="files[]" 
                           multiple 
                           accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar"
                           class="hidden">
                </div>
                
                <!-- Upload Guidelines -->
                <div class="mt-4 text-sm text-gray-400">
                    <p class="mb-2"><strong>Supported formats:</strong></p>
                    <ul class="list-disc list-inside space-y-1 ml-4">
                        <li><strong>Images:</strong> JPG, PNG, GIF, WebP, SVG (max 10MB each)</li>
                        <li><strong>Videos:</strong> MP4, AVI, MOV, WMV (max 100MB each)</li>
                        <li><strong>Audio:</strong> MP3, WAV, OGG, AAC (max 50MB each)</li>
                        <li><strong>Documents:</strong> PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT (max 25MB each)</li>
                        <li><strong>Archives:</strong> ZIP, RAR (max 50MB each)</li>
                    </ul>
                </div>
            </div>

            <!-- File Preview Area -->
            <div id="filePreview" class="hidden mb-6">
                <h3 class="text-lg font-medium text-white mb-4">Selected Files</h3>
                <div id="fileList" class="space-y-3"></div>
            </div>

            <!-- Upload Progress -->
            <div id="uploadProgress" class="hidden mb-6">
                <h3 class="text-lg font-medium text-white mb-4">Upload Progress</h3>
                <div class="space-y-3" id="progressList"></div>
            </div>

            <!-- Upload Button -->
            <div class="flex items-center justify-end space-x-4">
                <button type="button" 
                        id="clearFiles" 
                        class="btn-secondary hidden">
                    Clear All
                </button>
                <button type="submit" 
                        id="uploadButton" 
                        class="btn-primary hidden" 
                        disabled>
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Upload Files
                </button>
            </div>
        </form>
    </div>

    <!-- Recent Uploads -->
    <div class="mt-8 bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-white mb-4">Recent Uploads</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4" id="recentUploads">
            <!-- Recent uploads will be populated here -->
            <div class="text-center text-gray-400 col-span-full py-8">
                <p>No recent uploads</p>
            </div>
        </div>
    </div>
</div>

<script>
let selectedFiles = [];

// Drag and drop functionality
const dropZone = document.getElementById('dropZone');
const fileInput = document.getElementById('fileInput');

dropZone.addEventListener('dragover', (e) => {
    e.preventDefault();
    dropZone.classList.add('border-primary-500', 'bg-primary-50');
});

dropZone.addEventListener('dragleave', (e) => {
    e.preventDefault();
    dropZone.classList.remove('border-primary-500', 'bg-primary-50');
});

dropZone.addEventListener('drop', (e) => {
    e.preventDefault();
    dropZone.classList.remove('border-primary-500', 'bg-primary-50');
    
    const files = Array.from(e.dataTransfer.files);
    handleFiles(files);
});

dropZone.addEventListener('click', () => {
    fileInput.click();
});

fileInput.addEventListener('change', (e) => {
    const files = Array.from(e.target.files);
    handleFiles(files);
});

function handleFiles(files) {
    selectedFiles = [...selectedFiles, ...files];
    updateFilePreview();
    updateUploadButton();
}

function updateFilePreview() {
    const filePreview = document.getElementById('filePreview');
    const fileList = document.getElementById('fileList');
    
    if (selectedFiles.length === 0) {
        filePreview.classList.add('hidden');
        return;
    }
    
    filePreview.classList.remove('hidden');
    fileList.innerHTML = '';
    
    selectedFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'flex items-center justify-between bg-gray-700 rounded-lg p-4';
        
        const fileInfo = document.createElement('div');
        fileInfo.className = 'flex items-center';
        
        const fileIcon = getFileIcon(file.type);
        const fileName = file.name;
        const fileSize = formatFileSize(file.size);
        
        fileInfo.innerHTML = `
            <div class="text-2xl mr-3">${fileIcon}</div>
            <div>
                <div class="text-sm font-medium text-white">${fileName}</div>
                <div class="text-xs text-gray-400">${fileSize}</div>
            </div>
        `;
        
        const removeButton = document.createElement('button');
        removeButton.type = 'button';
        removeButton.className = 'text-red-400 hover:text-red-300';
        removeButton.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        `;
        removeButton.addEventListener('click', () => removeFile(index));
        
        fileItem.appendChild(fileInfo);
        fileItem.appendChild(removeButton);
        fileList.appendChild(fileItem);
    });
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFilePreview();
    updateUploadButton();
}

function updateUploadButton() {
    const uploadButton = document.getElementById('uploadButton');
    const clearButton = document.getElementById('clearFiles');
    
    if (selectedFiles.length > 0) {
        uploadButton.classList.remove('hidden');
        uploadButton.disabled = false;
        clearButton.classList.remove('hidden');
    } else {
        uploadButton.classList.add('hidden');
        uploadButton.disabled = true;
        clearButton.classList.add('hidden');
    }
}

function getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType.startsWith('video/')) return '🎥';
    if (mimeType.startsWith('audio/')) return '🎵';
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
    if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return '📽️';
    if (mimeType.includes('zip') || mimeType.includes('rar')) return '🗜️';
    return '📁';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Clear all files
document.getElementById('clearFiles').addEventListener('click', () => {
    selectedFiles = [];
    fileInput.value = '';
    updateFilePreview();
    updateUploadButton();
});

// Form submission with progress
document.getElementById('uploadForm').addEventListener('submit', (e) => {
    e.preventDefault();
    
    if (selectedFiles.length === 0) return;
    
    const formData = new FormData();
    selectedFiles.forEach(file => {
        formData.append('files[]', file);
    });
    
    // Add CSRF token
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    
    uploadFiles(formData);
});

function uploadFiles(formData) {
    const uploadProgress = document.getElementById('uploadProgress');
    const progressList = document.getElementById('progressList');
    const uploadButton = document.getElementById('uploadButton');
    
    uploadProgress.classList.remove('hidden');
    uploadButton.disabled = true;
    uploadButton.innerHTML = 'Uploading...';
    
    // Create progress bars for each file
    progressList.innerHTML = '';
    selectedFiles.forEach((file, index) => {
        const progressItem = document.createElement('div');
        progressItem.className = 'bg-gray-700 rounded-lg p-4';
        progressItem.innerHTML = `
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-white">${file.name}</span>
                <span class="text-sm text-gray-400" id="progress-${index}">0%</span>
            </div>
            <div class="w-full bg-gray-600 rounded-full h-2">
                <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                     id="progress-bar-${index}" 
                     style="width: 0%"></div>
            </div>
        `;
        progressList.appendChild(progressItem);
    });
    
    // Upload with XMLHttpRequest for progress tracking
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            
            // Update all progress bars (simplified - in real app you'd track individual files)
            selectedFiles.forEach((file, index) => {
                const progressBar = document.getElementById(`progress-bar-${index}`);
                const progressText = document.getElementById(`progress-${index}`);
                if (progressBar && progressText) {
                    progressBar.style.width = percentComplete + '%';
                    progressText.textContent = Math.round(percentComplete) + '%';
                }
            });
        }
    });
    
    xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
            // Success
            setTimeout(() => {
                window.location.href = '<?php echo e(route("admin.media.index")); ?>';
            }, 1000);
        } else {
            // Error
            alert('Upload failed. Please try again.');
            uploadButton.disabled = false;
            uploadButton.innerHTML = 'Upload Files';
        }
    });
    
    xhr.addEventListener('error', () => {
        alert('Upload failed. Please try again.');
        uploadButton.disabled = false;
        uploadButton.innerHTML = 'Upload Files';
    });
    
    xhr.open('POST', '<?php echo e(route("admin.media.store")); ?>');
    xhr.send(formData);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/media/create.blade.php ENDPATH**/ ?>