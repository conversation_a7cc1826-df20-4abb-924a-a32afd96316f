<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Course;
use App\Models\Event;
use App\Models\Campus;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminAnalyticsController extends Controller
{
    /**
     * Display analytics dashboard.
     */
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('last_seen_at', '>=', now()->subDays(30))->count(),
            'new_users_this_month' => User::where('created_at', '>=', now()->startOfMonth())->count(),
            'total_courses' => Course::count(),
            'published_courses' => Course::where('is_published', true)->count(),
            'total_events' => Event::count(),
            'upcoming_events' => Event::where('start_time', '>', now())->count(),
            'total_campuses' => Campus::count(),
            'active_campuses' => Campus::where('is_active', true)->count(),
        ];

        // User growth over last 12 months
        $userGrowth = User::select(
            DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
            DB::raw('COUNT(*) as count')
        )
        ->where('created_at', '>=', now()->subMonths(12))
        ->groupBy('month')
        ->orderBy('month')
        ->get();

        // Most popular campuses
        $popularCampuses = Campus::withCount('users')
            ->orderBy('users_count', 'desc')
            ->take(5)
            ->get();

        // Recent activity
        $recentUsers = User::latest()->take(10)->get();
        $recentCourses = Course::latest()->take(5)->get();

        return view('admin.analytics.index', compact(
            'stats', 
            'userGrowth', 
            'popularCampuses', 
            'recentUsers', 
            'recentCourses'
        ));
    }

    /**
     * Display user analytics.
     */
    public function users()
    {
        $stats = [
            'total_users' => User::count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'premium_users' => User::where('subscription_plan', '!=', 'free')->count(),
            'active_today' => User::where('last_seen_at', '>=', now()->startOfDay())->count(),
            'active_this_week' => User::where('last_seen_at', '>=', now()->startOfWeek())->count(),
            'active_this_month' => User::where('last_seen_at', '>=', now()->startOfMonth())->count(),
        ];

        // User registration by month
        $registrationsByMonth = User::select(
            DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
            DB::raw('COUNT(*) as count')
        )
        ->where('created_at', '>=', now()->subMonths(12))
        ->groupBy('month')
        ->orderBy('month')
        ->get();

        // User activity by day of week
        $activityByDay = User::select(
            DB::raw('DAYNAME(last_login_at) as day'),
            DB::raw('COUNT(*) as count')
        )
        ->whereNotNull('last_login_at')
        ->where('last_login_at', '>=', now()->subDays(30))
        ->groupBy('day')
        ->get();

        return view('admin.analytics.users', compact(
            'stats', 
            'registrationsByMonth', 
            'activityByDay'
        ));
    }

    /**
     * Display revenue analytics.
     */
    public function revenue()
    {
        // Mock revenue data - in real app this would come from payment records
        $stats = [
            'total_revenue' => 125000,
            'monthly_revenue' => 15000,
            'average_revenue_per_user' => 75,
            'subscription_conversion_rate' => 12.5,
        ];

        $revenueByMonth = collect([
            ['month' => '2024-01', 'revenue' => 8500],
            ['month' => '2024-02', 'revenue' => 9200],
            ['month' => '2024-03', 'revenue' => 11000],
            ['month' => '2024-04', 'revenue' => 12500],
            ['month' => '2024-05', 'revenue' => 13800],
            ['month' => '2024-06', 'revenue' => 15000],
        ]);

        $subscriptionBreakdown = [
            'free' => User::where('subscription_plan', 'free')->count(),
            'prosper' => User::where('subscription_plan', 'prosper')->count(),
            'conquer' => User::where('subscription_plan', 'conquer')->count(),
            'champions' => User::where('subscription_plan', 'champions')->count(),
        ];

        return view('admin.analytics.revenue', compact(
            'stats', 
            'revenueByMonth', 
            'subscriptionBreakdown'
        ));
    }

    /**
     * Display engagement analytics.
     */
    public function engagement()
    {
        $stats = [
            'total_course_enrollments' => DB::table('course_enrollments')->count(),
            'completed_courses' => DB::table('course_enrollments')->where('completed_at', '!=', null)->count(),
            'average_completion_rate' => 68.5, // Mock data
            'total_event_rsvps' => DB::table('event_rsvps')->count(),
        ];

        // Most popular courses
        $popularCourses = Course::withCount('enrollments')
            ->orderBy('enrollments_count', 'desc')
            ->take(10)
            ->get();

        // Course completion rates
        $completionRates = Course::select('title')
            ->withCount([
                'enrollments',
                'enrollments as completed_count' => function ($query) {
                    $query->whereNotNull('completed_at');
                }
            ])
            ->having('enrollments_count', '>', 0)
            ->get()
            ->map(function ($course) {
                $course->completion_rate = $course->enrollments_count > 0 
                    ? round(($course->completed_count / $course->enrollments_count) * 100, 1)
                    : 0;
                return $course;
            });

        return view('admin.analytics.engagement', compact(
            'stats', 
            'popularCourses', 
            'completionRates'
        ));
    }
}
