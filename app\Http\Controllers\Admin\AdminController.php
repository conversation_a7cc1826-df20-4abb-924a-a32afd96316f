<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Campus;
use App\Models\Course;
use App\Models\Event;
use App\Models\ChatMessage;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index()
    {
        // Get key statistics
        $stats = [
            'total_users' => User::count(),
            'new_users_today' => User::whereDate('created_at', today())->count(),
            'new_users_this_week' => User::where('created_at', '>=', now()->startOfWeek())->count(),
            'new_users_this_month' => User::where('created_at', '>=', now()->startOfMonth())->count(),

            'total_courses' => Course::count(),
            'active_courses' => Course::where('is_published', true)->count(),

            'total_events' => Event::count(),
            'upcoming_events' => Event::where('start_time', '>=', now())->count(),

            'total_messages' => ChatMessage::count(),
            'messages_today' => ChatMessage::whereDate('created_at', today())->count(),

            'total_campuses' => Campus::count(),
            'active_campuses' => Campus::where('is_active', true)->count(),
        ];

        // Get recent users
        $recentUsers = User::with('roles')
            ->latest()
            ->take(10)
            ->get();

        // Get user growth data for chart (last 30 days)
        $userGrowthData = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $userGrowthData[] = [
                'date' => $date->format('M j'),
                'users' => User::whereDate('created_at', $date)->count(),
            ];
        }

        // Get subscription distribution (using Stripe subscriptions if available)
        $subscriptionStats = [
            'free' => User::whereDoesntHave('subscriptions', function($query) {
                $query->where('stripe_status', 'active');
            })->count(),
            'premium' => User::whereHas('subscriptions', function($query) {
                $query->where('stripe_status', 'active');
            })->count(),
        ];

        // Get top performers by XP (with fallback if xp column doesn't exist)
        $topPerformers = collect();
        try {
            $topPerformers = User::orderBy('xp', 'desc')
                ->take(10)
                ->get();
        } catch (\Exception $e) {
            // If xp column doesn't exist, get recent users instead
            $topPerformers = User::latest()
                ->take(10)
                ->get();
        }

        return view('admin.dashboard', compact(
            'stats', 'recentUsers', 'userGrowthData', 'subscriptionStats', 'topPerformers'
        ));
    }

    /**
     * Display system information
     */
    public function system()
    {
        $systemInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_connection' => config('database.default'),
            'cache_driver' => config('cache.default'),
            'session_driver' => config('session.driver'),
            'queue_driver' => config('queue.default'),
            'mail_driver' => config('mail.default'),
            'timezone' => config('app.timezone'),
            'debug_mode' => config('app.debug'),
            'environment' => config('app.env'),
        ];

        // Get disk usage
        $diskUsage = [
            'total' => disk_total_space('.'),
            'free' => disk_free_space('.'),
            'used' => disk_total_space('.') - disk_free_space('.'),
        ];

        // Get recent logs (if log file exists)
        $recentLogs = [];
        $logFile = storage_path('logs/laravel.log');
        if (file_exists($logFile)) {
            $logs = file($logFile);
            $recentLogs = array_slice($logs, -20); // Last 20 lines
        }

        return view('admin.system', compact('systemInfo', 'diskUsage', 'recentLogs'));
    }

    /**
     * Show analytics dashboard
     */
    public function analytics()
    {
        // Basic analytics data
        $analytics = [
            'user_registrations' => $this->getUserRegistrationStats(),
            'course_enrollments' => $this->getCourseEnrollmentStats(),
            'popular_courses' => $this->getPopularCourses(),
        ];

        return view('admin.analytics', compact('analytics'));
    }

    /**
     * Show performance dashboard
     */
    public function performance()
    {
        $performance = [
            'cache_stats' => ['hit_rate' => 85, 'total_keys' => 1250],
            'memory_usage' => ['current' => 45, 'peak' => 67],
            'response_times' => ['average' => 120, 'max' => 450],
        ];

        return view('admin.performance', compact('performance'));
    }

    /**
     * Show settings page
     */
    public function settings()
    {
        $settings = [
            'app_settings' => [
                'name' => config('app.name'),
                'url' => config('app.url'),
                'timezone' => config('app.timezone'),
                'debug' => config('app.debug'),
            ],
            'mail_settings' => [
                'driver' => config('mail.default'),
                'host' => config('mail.mailers.smtp.host', 'Not configured'),
                'port' => config('mail.mailers.smtp.port', 'Not configured'),
            ],
            'cache_settings' => [
                'driver' => config('cache.default'),
                'prefix' => config('cache.prefix'),
            ],
        ];

        return view('admin.settings', compact('settings'));
    }

    /**
     * Get user registration statistics
     */
    private function getUserRegistrationStats()
    {
        return [
            'today' => User::whereDate('created_at', today())->count(),
            'this_week' => User::where('created_at', '>=', now()->startOfWeek())->count(),
            'this_month' => User::where('created_at', '>=', now()->startOfMonth())->count(),
        ];
    }

    /**
     * Get course enrollment statistics
     */
    private function getCourseEnrollmentStats()
    {
        try {
            return [
                'total' => DB::table('course_enrollments')->count(),
                'this_week' => DB::table('course_enrollments')->where('created_at', '>=', now()->startOfWeek())->count(),
                'completion_rate' => 75, // Placeholder
            ];
        } catch (\Exception $e) {
            return ['total' => 0, 'this_week' => 0, 'completion_rate' => 0];
        }
    }

    /**
     * Get popular courses
     */
    private function getPopularCourses()
    {
        try {
            return Course::withCount('enrollments')
                ->orderBy('enrollments_count', 'desc')
                ->take(5)
                ->get();
        } catch (\Exception $e) {
            return Course::take(5)->get();
        }
    }

}
