<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

trait Searchable
{
    /**
     * Search scope for the model
     */
    public function scopeSearch(Builder $query, string $term): Builder
    {
        if (empty($term)) {
            return $query;
        }

        $searchableFields = $this->getSearchableFields();
        
        return $query->where(function ($q) use ($term, $searchableFields) {
            foreach ($searchableFields as $field) {
                if (str_contains($field, '.')) {
                    // Handle relationship fields
                    $parts = explode('.', $field);
                    $relation = $parts[0];
                    $relationField = $parts[1];
                    
                    $q->orWhereHas($relation, function ($relationQuery) use ($relationField, $term) {
                        $relationQuery->where($relationField, 'like', "%{$term}%");
                    });
                } else {
                    // Handle direct model fields
                    $q->orWhere($field, 'like', "%{$term}%");
                }
            }
        });
    }

    /**
     * Advanced search scope with multiple terms
     */
    public function scopeAdvancedSearch(Builder $query, array $terms): Builder
    {
        if (empty($terms)) {
            return $query;
        }

        $searchableFields = $this->getSearchableFields();
        
        return $query->where(function ($q) use ($terms, $searchableFields) {
            foreach ($terms as $term) {
                $q->where(function ($subQuery) use ($term, $searchableFields) {
                    foreach ($searchableFields as $field) {
                        if (str_contains($field, '.')) {
                            // Handle relationship fields
                            $parts = explode('.', $field);
                            $relation = $parts[0];
                            $relationField = $parts[1];
                            
                            $subQuery->orWhereHas($relation, function ($relationQuery) use ($relationField, $term) {
                                $relationQuery->where($relationField, 'like', "%{$term}%");
                            });
                        } else {
                            // Handle direct model fields
                            $subQuery->orWhere($field, 'like', "%{$term}%");
                        }
                    }
                });
            }
        });
    }

    /**
     * Search with relevance scoring
     */
    public function scopeSearchWithRelevance(Builder $query, string $term): Builder
    {
        if (empty($term)) {
            return $query;
        }

        $searchableFields = $this->getSearchableFields();
        $selectFields = [$this->getTable() . '.*'];
        $relevanceScore = [];

        foreach ($searchableFields as $index => $field) {
            $weight = $this->getFieldWeight($field);
            
            if (str_contains($field, '.')) {
                // Skip relationship fields for relevance scoring for now
                continue;
            }

            // Exact match gets highest score
            $relevanceScore[] = "CASE WHEN {$field} = '{$term}' THEN {$weight} * 100 ELSE 0 END";
            
            // Starts with gets high score
            $relevanceScore[] = "CASE WHEN {$field} LIKE '{$term}%' THEN {$weight} * 50 ELSE 0 END";
            
            // Contains gets medium score
            $relevanceScore[] = "CASE WHEN {$field} LIKE '%{$term}%' THEN {$weight} * 10 ELSE 0 END";
        }

        if (!empty($relevanceScore)) {
            $selectFields[] = '(' . implode(' + ', $relevanceScore) . ') as relevance_score';
        }

        return $query->selectRaw(implode(', ', $selectFields))
                    ->search($term)
                    ->orderByDesc('relevance_score');
    }

    /**
     * Get searchable fields for the model
     */
    protected function getSearchableFields(): array
    {
        return $this->searchable ?? ['name', 'title', 'description'];
    }

    /**
     * Get field weight for relevance scoring
     */
    protected function getFieldWeight(string $field): int
    {
        $weights = $this->searchWeights ?? [];
        return $weights[$field] ?? 1;
    }

    /**
     * Get search suggestions based on partial input
     */
    public function scopeSearchSuggestions(Builder $query, string $term, int $limit = 10): Builder
    {
        if (empty($term) || strlen($term) < 2) {
            return $query->limit(0);
        }

        $primaryField = $this->getSearchableFields()[0] ?? 'name';
        
        return $query->where($primaryField, 'like', "{$term}%")
                    ->orderBy($primaryField)
                    ->limit($limit);
    }

    /**
     * Highlight search terms in results
     */
    public function highlightSearchTerm(string $text, string $term): string
    {
        if (empty($term)) {
            return $text;
        }

        $highlighted = preg_replace(
            '/(' . preg_quote($term, '/') . ')/i',
            '<mark class="bg-yellow-200 text-yellow-900 px-1 rounded">$1</mark>',
            $text
        );

        return $highlighted ?: $text;
    }

    /**
     * Get search excerpt from content
     */
    public function getSearchExcerpt(string $content, string $term, int $length = 150): string
    {
        if (empty($term)) {
            return substr($content, 0, $length) . (strlen($content) > $length ? '...' : '');
        }

        $position = stripos($content, $term);
        
        if ($position === false) {
            return substr($content, 0, $length) . (strlen($content) > $length ? '...' : '');
        }

        $start = max(0, $position - ($length / 2));
        $excerpt = substr($content, $start, $length);
        
        // Try to start and end on word boundaries
        if ($start > 0) {
            $spacePos = strpos($excerpt, ' ');
            if ($spacePos !== false) {
                $excerpt = substr($excerpt, $spacePos + 1);
            }
            $excerpt = '...' . $excerpt;
        }

        if (strlen($content) > $start + $length) {
            $spacePos = strrpos($excerpt, ' ');
            if ($spacePos !== false) {
                $excerpt = substr($excerpt, 0, $spacePos);
            }
            $excerpt .= '...';
        }

        return $this->highlightSearchTerm($excerpt, $term);
    }

    /**
     * Get search metadata for the model
     */
    public function getSearchMetadata(): array
    {
        return [
            'type' => class_basename($this),
            'id' => $this->getKey(),
            'title' => $this->getSearchTitle(),
            'description' => $this->getSearchDescription(),
            'url' => $this->getSearchUrl(),
            'image' => $this->getSearchImage(),
            'meta' => $this->getSearchMeta(),
        ];
    }

    /**
     * Get search title for the model
     */
    protected function getSearchTitle(): string
    {
        return $this->title ?? $this->name ?? 'Untitled';
    }

    /**
     * Get search description for the model
     */
    protected function getSearchDescription(): string
    {
        return $this->description ?? $this->content ?? '';
    }

    /**
     * Get search URL for the model
     */
    protected function getSearchUrl(): string
    {
        return '#';
    }

    /**
     * Get search image for the model
     */
    protected function getSearchImage(): ?string
    {
        return null;
    }

    /**
     * Get search meta information for the model
     */
    protected function getSearchMeta(): string
    {
        return '';
    }
}
