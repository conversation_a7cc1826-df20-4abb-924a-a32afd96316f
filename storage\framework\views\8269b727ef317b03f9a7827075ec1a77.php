<?php $__env->startSection('title', 'Live Events - The Real World'); ?>
<?php $__env->startSection('page-title', 'Live Events'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold mb-2">Live Events & Workshops</h2>
            <p class="text-gray-400">Join exclusive sessions with successful entrepreneurs and mentors</p>
        </div>
        
        <!-- Filter Tabs -->
        <div class="flex space-x-2">
            <a href="<?php echo e(route('dashboard.events.index', ['filter' => 'upcoming'])); ?>"
               class="btn-<?php echo e($filter === 'upcoming' ? 'primary' : 'secondary'); ?> px-4 py-2">
                Upcoming
            </a>
            <a href="<?php echo e(route('dashboard.events.index', ['filter' => 'my_events'])); ?>"
               class="btn-<?php echo e($filter === 'my_events' ? 'primary' : 'secondary'); ?> px-4 py-2">
                My Events
            </a>
            <a href="<?php echo e(route('dashboard.events.index', ['filter' => 'past'])); ?>"
               class="btn-<?php echo e($filter === 'past' ? 'primary' : 'secondary'); ?> px-4 py-2">
                Past Events
            </a>
        </div>
    </div>

    <!-- Events Grid -->
    <?php if($events->count() > 0): ?>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <?php $__currentLoopData = $events; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="card hover:transform hover:scale-105 transition-all duration-300 overflow-hidden">
            <!-- Event Thumbnail -->
            <div class="relative">
                <?php if($event->thumbnail_url): ?>
                    <img src="<?php echo e($event->thumbnail_url); ?>" alt="<?php echo e($event->title); ?>" 
                         class="w-full h-48 object-cover">
                <?php else: ?>
                    <div class="w-full h-48 bg-gradient-to-br from-primary-600 to-primary-800 flex items-center justify-center">
                        <svg class="w-16 h-16 text-white opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                <?php endif; ?>

                <!-- Status Badge -->
                <?php $statusBadge = $event->getStatusBadge(); ?>
                <div class="absolute top-4 right-4">
                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo e($statusBadge['class']); ?>">
                        <?php echo e($statusBadge['text']); ?>

                    </span>
                </div>

                <!-- Campus Badge -->
                <?php if($event->campus): ?>
                <div class="absolute top-4 left-4">
                    <span class="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs">
                        <?php echo e($event->campus->name); ?>

                    </span>
                </div>
                <?php endif; ?>
            </div>

            <!-- Event Content -->
            <div class="p-6">
                <!-- Event Title -->
                <h3 class="text-xl font-bold mb-2 line-clamp-2"><?php echo e($event->title); ?></h3>
                
                <!-- Event Description -->
                <p class="text-gray-400 text-sm mb-4 line-clamp-3"><?php echo e($event->description); ?></p>

                <!-- Event Details -->
                <div class="space-y-2 mb-4 text-sm">
                    <!-- Date & Time -->
                    <div class="flex items-center text-gray-300">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <?php echo e($event->start_time->format('M j, Y • g:i A')); ?>

                    </div>

                    <!-- Duration -->
                    <div class="flex items-center text-gray-300">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?php echo e($event->getFormattedDuration()); ?>

                    </div>

                    <!-- Attendees -->
                    <div class="flex items-center text-gray-300">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <?php echo e($event->rsvp_count); ?>/<?php echo e($event->max_attendees ?? '∞'); ?> attending
                    </div>
                </div>

                <!-- Countdown Timer -->
                <?php if($event->countdown['status'] === 'upcoming'): ?>
                <div class="bg-primary-600/20 rounded-lg p-3 mb-4">
                    <p class="text-primary-400 text-sm font-medium mb-2"><?php echo e($event->countdown['message']); ?></p>
                    <div class="grid grid-cols-4 gap-2 text-center">
                        <div>
                            <div class="text-lg font-bold text-primary-400"><?php echo e($event->countdown['days']); ?></div>
                            <div class="text-xs text-gray-400">Days</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-primary-400"><?php echo e($event->countdown['hours']); ?></div>
                            <div class="text-xs text-gray-400">Hours</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-primary-400"><?php echo e($event->countdown['minutes']); ?></div>
                            <div class="text-xs text-gray-400">Min</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-primary-400"><?php echo e($event->countdown['seconds']); ?></div>
                            <div class="text-xs text-gray-400">Sec</div>
                        </div>
                    </div>
                </div>
                <?php elseif($event->countdown['status'] === 'live'): ?>
                <div class="bg-red-600/20 rounded-lg p-3 mb-4">
                    <p class="text-red-400 text-sm font-medium text-center">
                        🔴 LIVE NOW - <?php echo e($event->countdown['message']); ?>

                    </p>
                </div>
                <?php endif; ?>

                <!-- Premium Badge -->
                <?php if($event->is_premium): ?>
                <div class="flex items-center mb-4">
                    <svg class="w-4 h-4 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-yellow-400 text-sm font-medium">Premium Event</span>
                </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <?php if($event->can_join): ?>
                        <a href="<?php echo e(route('dashboard.events.join', $event)); ?>" 
                           class="btn-primary flex-1 text-center">
                            Join Live
                        </a>
                    <?php elseif($event->is_user_registered): ?>
                        <?php if($event->countdown['status'] === 'upcoming'): ?>
                            <button class="btn-secondary flex-1" disabled>
                                RSVP'd ✓
                            </button>
                        <?php else: ?>
                            <button class="btn-secondary flex-1" disabled>
                                Event Ended
                            </button>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php if($event->countdown['status'] === 'upcoming' && $event->canUserAccess($user)): ?>
                            <button onclick="rsvpToEvent(<?php echo e($event->id); ?>)" 
                                    class="btn-primary flex-1" 
                                    id="rsvp-btn-<?php echo e($event->id); ?>">
                                RSVP Now
                            </button>
                        <?php elseif(!$event->canUserAccess($user)): ?>
                            <a href="<?php echo e(route('subscription.plans')); ?>" 
                               class="btn-secondary flex-1 text-center">
                                Upgrade to Access
                            </a>
                        <?php else: ?>
                            <button class="btn-secondary flex-1" disabled>
                                Event Ended
                            </button>
                        <?php endif; ?>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('dashboard.events.show', $event)); ?>" 
                       class="btn-secondary px-4 py-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- Pagination -->
    <div class="mt-8">
        <?php echo e($events->appends(request()->query())->links()); ?>

    </div>
    <?php else: ?>
    <!-- Empty State -->
    <div class="text-center py-12">
        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">
            <?php if($filter === 'upcoming'): ?>
                No Upcoming Events
            <?php elseif($filter === 'my_events'): ?>
                No Events RSVP'd
            <?php else: ?>
                No Past Events
            <?php endif; ?>
        </h3>
        <p class="text-gray-400 mb-6">
            <?php if($filter === 'upcoming'): ?>
                Check back soon for new live events and workshops!
            <?php elseif($filter === 'my_events'): ?>
                RSVP to upcoming events to see them here.
            <?php else: ?>
                Past events will appear here after they're completed.
            <?php endif; ?>
        </p>
        <?php if($filter !== 'upcoming'): ?>
        <a href="<?php echo e(route('dashboard.events.index')); ?>" class="btn-primary">
            View Upcoming Events
        </a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<script>
function rsvpToEvent(eventId) {
    const button = document.getElementById(`rsvp-btn-${eventId}`);
    button.disabled = true;
    button.textContent = 'RSVP\'ing...';

    fetch(`/dashboard/events/${eventId}/rsvp`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            button.textContent = 'RSVP\'d ✓';
            button.classList.remove('btn-primary');
            button.classList.add('btn-secondary');
        } else {
            showNotification(data.message, 'error');
            button.disabled = false;
            button.textContent = 'RSVP Now';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        button.disabled = false;
        button.textContent = 'RSVP Now';
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/events/index.blade.php ENDPATH**/ ?>