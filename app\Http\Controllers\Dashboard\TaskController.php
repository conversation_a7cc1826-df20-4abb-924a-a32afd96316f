<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Task;
use App\Models\UserTask;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TaskController extends Controller
{
    /**
     * Display daily tasks for the user
     */
    public function index()
    {
        $user = Auth::user();
        $today = Carbon::today();

        // Get all active tasks
        $allTasks = Task::active()->orderBy('sort_order')->get();

        // Get user's task completions for today
        $userTasks = UserTask::where('user_id', $user->id)
            ->whereDate('completed_at', $today)
            ->pluck('task_id')
            ->toArray();

        // Add completion status to tasks
        foreach ($allTasks as $task) {
            $task->is_completed_today = in_array($task->id, $userTasks);
        }

        // Calculate daily progress
        $completedTasks = $allTasks->where('is_completed_today', true)->count();
        $totalTasks = $allTasks->count();
        $dailyProgress = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;

        // Calculate daily XP earned
        $dailyXpEarned = UserTask::where('user_id', $user->id)
            ->whereDate('completed_at', $today)
            ->join('tasks', 'user_tasks.task_id', '=', 'tasks.id')
            ->sum('tasks.xp_reward');

        // Get user's streak
        $streak = $this->calculateUserStreak($user);

        // Check if user completed all tasks today
        $allTasksCompleted = $completedTasks === $totalTasks && $totalTasks > 0;

        return view('dashboard.tasks.index', compact(
            'allTasks', 'dailyProgress', 'completedTasks', 'totalTasks',
            'dailyXpEarned', 'streak', 'allTasksCompleted', 'user'
        ));
    }

    /**
     * Complete a specific task
     */
    public function complete(Request $request, Task $task)
    {
        $user = Auth::user();
        $today = Carbon::today();

        // Check if task is already completed today
        $existingCompletion = UserTask::where('user_id', $user->id)
            ->where('task_id', $task->id)
            ->whereDate('completed_at', $today)
            ->first();

        if ($existingCompletion) {
            return response()->json([
                'success' => false,
                'message' => 'Task already completed today!'
            ]);
        }

        // Create task completion record
        UserTask::create([
            'user_id' => $user->id,
            'task_id' => $task->id,
            'completed_at' => now(),
        ]);

        // Award XP to user
        $user->addXp($task->xp_reward);

        // Check if this completes all daily tasks
        $allTasks = Task::active()->count();
        $completedToday = UserTask::where('user_id', $user->id)
            ->whereDate('completed_at', $today)
            ->count();

        $allTasksCompleted = $completedToday === $allTasks;

        // Bonus XP for completing all daily tasks
        $bonusXp = 0;
        if ($allTasksCompleted) {
            $bonusXp = 50; // Bonus XP for completing all tasks
            $user->addXp($bonusXp);
        }

        return response()->json([
            'success' => true,
            'message' => 'Task completed! +' . $task->xp_reward . ' XP',
            'xp_earned' => $task->xp_reward,
            'bonus_xp' => $bonusXp,
            'all_tasks_completed' => $allTasksCompleted,
            'new_level' => $user->level,
            'total_xp' => $user->xp,
        ]);
    }

    /**
     * Calculate user's daily task streak
     */
    private function calculateUserStreak($user)
    {
        $streak = 0;
        $currentDate = Carbon::today();
        $totalTasks = Task::active()->count();

        if ($totalTasks === 0) return 0;

        // Check each day going backwards
        while (true) {
            $completedTasks = UserTask::where('user_id', $user->id)
                ->whereDate('completed_at', $currentDate)
                ->count();

            // If user completed all tasks on this day, increment streak
            if ($completedTasks === $totalTasks) {
                $streak++;
                $currentDate->subDay();
            } else {
                break;
            }

            // Prevent infinite loop (max 365 days)
            if ($streak >= 365) break;
        }

        return $streak;
    }
}
