<?php $__env->startSection('title', 'Manage Badges'); ?>
<?php $__env->startSection('page-title', 'Badges'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Manage Badges</h1>
            <p class="text-gray-400 mt-1">Create and manage achievement badges</p>
        </div>
        <a href="<?php echo e(route('admin.badges.create')); ?>" class="btn-primary">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Badge
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-primary-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Badges</p>
                    <p class="text-2xl font-bold text-white"><?php echo e($badges->total()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Active Badges</p>
                    <p class="text-2xl font-bold text-white"><?php echo e($badges->where('is_active', true)->count()); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Awarded</p>
                    <p class="text-2xl font-bold text-white"><?php echo e($badges->sum('users_count')); ?></p>
                </div>
            </div>
        </div>

        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Special Badges</p>
                    <p class="text-2xl font-bold text-white"><?php echo e($badges->where('type', 'special')->count()); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Badges Grid -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white">All Badges</h3>
        </div>

        <?php if($badges->count() > 0): ?>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <?php $__currentLoopData = $badges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $badge): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-gray-700 rounded-lg p-6 hover:bg-gray-600 transition-colors">
                            <!-- Badge Icon -->
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-16 h-16 rounded-full flex items-center justify-center text-2xl"
                                     style="background-color: <?php echo e($badge->color); ?>">
                                    <?php echo e($badge->icon); ?>

                                </div>
                            </div>

                            <!-- Badge Info -->
                            <div class="text-center mb-4">
                                <h3 class="text-lg font-semibold text-white mb-2"><?php echo e($badge->name); ?></h3>
                                <p class="text-sm text-gray-400 mb-3"><?php echo e(Str::limit($badge->description, 80)); ?></p>
                                
                                <!-- Badge Type -->
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    <?php echo e($badge->type === 'achievement' ? 'bg-blue-100 text-blue-800' : 
                                       ($badge->type === 'milestone' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800')); ?>">
                                    <?php echo e(ucfirst($badge->type)); ?>

                                </span>
                            </div>

                            <!-- Badge Stats -->
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-400">Awarded:</span>
                                    <span class="text-white font-medium"><?php echo e($badge->users_count ?? 0); ?> users</span>
                                </div>
                                <?php if($badge->xp_reward > 0): ?>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-400">XP Reward:</span>
                                        <span class="text-primary-400 font-medium"><?php echo e($badge->xp_reward); ?> XP</span>
                                    </div>
                                <?php endif; ?>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-400">Status:</span>
                                    <span class="font-medium <?php echo e($badge->is_active ? 'text-green-400' : 'text-red-400'); ?>">
                                        <?php echo e($badge->is_active ? 'Active' : 'Inactive'); ?>

                                    </span>
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="flex items-center justify-between pt-4 border-t border-gray-600">
                                <a href="<?php echo e(route('admin.badges.show', $badge)); ?>" 
                                   class="text-primary-400 hover:text-primary-300 text-sm font-medium">
                                    View Details
                                </a>
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.badges.edit', $badge)); ?>" 
                                       class="text-blue-400 hover:text-blue-300 text-sm">
                                        Edit
                                    </a>
                                    <form method="POST" action="<?php echo e(route('admin.badges.destroy', $badge)); ?>" class="inline">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" 
                                                class="text-red-400 hover:text-red-300 text-sm" 
                                                onclick="return confirm('Are you sure you want to delete this badge?')">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Pagination -->
            <?php if($badges->hasPages()): ?>
                <div class="px-6 py-4 border-t border-gray-700">
                    <?php echo e($badges->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <!-- Empty State -->
            <div class="p-12 text-center">
                <div class="text-gray-400">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-300">No badges</h3>
                    <p class="mt-1 text-sm text-gray-400">Get started by creating your first achievement badge.</p>
                    <div class="mt-6">
                        <a href="<?php echo e(route('admin.badges.create')); ?>" class="btn-primary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Create First Badge
                        </a>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/badges/index.blade.php ENDPATH**/ ?>