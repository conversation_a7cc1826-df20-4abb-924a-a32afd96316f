@extends('layouts.admin')

@section('title', 'Create Campus')
@section('page-title', 'Create Campus')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Create New Campus</h1>
            <p class="text-gray-400 mt-1">Add a new learning campus</p>
        </div>
        <a href="{{ route('admin.campuses.index') }}" class="btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Campuses
        </a>
    </div>

    <form action="{{ route('admin.campuses.store') }}" method="POST" enctype="multipart/form-data" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        @csrf
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Campus Name *</label>
                            <input type="text" name="name" id="name" class="w-full px-3 py-2 bg-gray-700 border @error('name') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('name') }}" required placeholder="Enter campus name">
                            @error('name')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-300 mb-2">Slug</label>
                            <input type="text" name="slug" id="slug" class="w-full px-3 py-2 bg-gray-700 border @error('slug') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('slug') }}" required placeholder="auto-generated">
                            <p class="mt-1 text-xs text-gray-400">URL-friendly version (auto-generated)</p>
                            @error('slug')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                        <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 bg-gray-700 border @error('description') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required placeholder="Describe what this campus is about">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-300 mb-2">Campus Color *</label>
                            <input type="color" name="color" id="color" class="w-full h-10 bg-gray-700 border @error('color') border-red-500 @else border-gray-600 @enderror rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" value="{{ old('color', '#3B82F6') }}" required>
                            <p class="mt-1 text-xs text-gray-400">Brand color for this campus</p>
                            @error('color')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                            <input type="number" name="sort_order" id="sort_order" class="w-full px-3 py-2 bg-gray-700 border @error('sort_order') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('sort_order', 0) }}" min="0" placeholder="0">
                            <p class="mt-1 text-xs text-gray-400">Display order (lower numbers first)</p>
                            @error('sort_order')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Media -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Media</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label for="image" class="block text-sm font-medium text-gray-300 mb-2">Campus Image</label>
                        <input type="file" name="image" id="image" class="w-full px-3 py-2 bg-gray-700 border @error('image') border-red-500 @else border-gray-600 @enderror rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" accept="image/*">
                        <p class="mt-1 text-xs text-gray-400">Upload a representative image for this campus</p>
                        @error('image')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="icon_url" class="block text-sm font-medium text-gray-300 mb-2">Icon URL</label>
                        <input type="url" name="icon_url" id="icon_url" class="w-full px-3 py-2 bg-gray-700 border @error('icon_url') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('icon_url') }}" placeholder="https://example.com/icon.png">
                        <p class="mt-1 text-xs text-gray-400">URL to campus icon (optional)</p>
                        @error('icon_url')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Additional Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label for="features" class="block text-sm font-medium text-gray-300 mb-2">Features (JSON)</label>
                        <textarea name="features" id="features" rows="4" class="w-full px-3 py-2 bg-gray-700 border @error('features') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder='{"networking": true, "mentorship": true, "events": true}'>{{ old('features') }}</textarea>
                        <p class="mt-1 text-xs text-gray-400">JSON object defining campus features</p>
                        @error('features')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="location" class="block text-sm font-medium text-gray-300 mb-2">Location</label>
                            <input type="text" name="location" id="location" class="w-full px-3 py-2 bg-gray-700 border @error('location') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('location') }}" placeholder="City, Country">
                            @error('location')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                            <select name="timezone" id="timezone" class="w-full px-3 py-2 bg-gray-700 border @error('timezone') border-red-500 @else border-gray-600 @enderror rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Select timezone</option>
                                <option value="UTC" {{ old('timezone') === 'UTC' ? 'selected' : '' }}>UTC</option>
                                <option value="America/New_York" {{ old('timezone') === 'America/New_York' ? 'selected' : '' }}>Eastern Time</option>
                                <option value="America/Chicago" {{ old('timezone') === 'America/Chicago' ? 'selected' : '' }}>Central Time</option>
                                <option value="America/Denver" {{ old('timezone') === 'America/Denver' ? 'selected' : '' }}>Mountain Time</option>
                                <option value="America/Los_Angeles" {{ old('timezone') === 'America/Los_Angeles' ? 'selected' : '' }}>Pacific Time</option>
                                <option value="Europe/London" {{ old('timezone') === 'Europe/London' ? 'selected' : '' }}>London</option>
                                <option value="Europe/Paris" {{ old('timezone') === 'Europe/Paris' ? 'selected' : '' }}>Paris</option>
                                <option value="Asia/Tokyo" {{ old('timezone') === 'Asia/Tokyo' ? 'selected' : '' }}>Tokyo</option>
                                <option value="Asia/Shanghai" {{ old('timezone') === 'Asia/Shanghai' ? 'selected' : '' }}>Shanghai</option>
                                <option value="Australia/Sydney" {{ old('timezone') === 'Australia/Sydney' ? 'selected' : '' }}>Sydney</option>
                            </select>
                            @error('timezone')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Settings</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_active" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Active</span>
                                <p class="text-xs text-gray-400">Campus is visible and accessible</p>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_featured" id="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_featured" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Featured Campus</span>
                                <p class="text-xs text-gray-400">Highlight this campus</p>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between">
                <a href="{{ route('admin.campuses.index') }}" class="btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Campus
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Campus Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Preview</h3>
                </div>
                <div class="p-6">
                    <div class="campus-preview text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-lg mb-4 campus-color" style="background-color: #3B82F6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h4 class="campus-name text-white font-medium mb-2">Campus Name</h4>
                        <p class="campus-description text-gray-300 text-sm mb-4">Campus description will appear here</p>
                        <div class="campus-location text-xs text-gray-400">Location will appear here</div>
                    </div>
                </div>
            </div>

            <!-- Guidelines -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Guidelines</h3>
                </div>
                <div class="p-6">
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Use clear, descriptive names
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Choose distinctive colors
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Upload high-quality images
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Set appropriate sort order
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const colorInput = document.getElementById('color');
    const locationInput = document.getElementById('location');

    nameInput.addEventListener('input', function() {
        document.querySelector('.campus-name').textContent = this.value || 'Campus Name';
    });

    descriptionInput.addEventListener('input', function() {
        document.querySelector('.campus-description').textContent = this.value || 'Campus description will appear here';
    });

    colorInput.addEventListener('input', function() {
        document.querySelector('.campus-color').style.backgroundColor = this.value;
    });

    locationInput.addEventListener('input', function() {
        document.querySelector('.campus-location').textContent = this.value || 'Location will appear here';
    });

    // Auto-generate slug from name
    nameInput.addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim('-');
        document.getElementById('slug').value = slug;
    });
});
</script>
@endpush
