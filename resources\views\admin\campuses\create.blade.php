@extends('layouts.admin')

@section('title', 'Create Campus')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Create New Campus</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.campuses.index') }}">Campuses</a></li>
                    <li class="breadcrumb-item active">Create</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.campuses.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Campuses
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Campus Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campus Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.campuses.store') }}" method="POST">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Campus Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                           id="slug" name="slug" value="{{ old('slug') }}" required>
                                    <small class="form-text text-muted">URL-friendly version (e.g., ecommerce, copywriting)</small>
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">Theme Color</label>
                                    <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', '#ef4444') }}" title="Choose campus theme color">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                                    <small class="form-text text-muted">Lower numbers appear first</small>
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="icon_url" class="form-label">Icon URL</label>
                            <input type="url" class="form-control @error('icon_url') is-invalid @enderror" 
                                   id="icon_url" name="icon_url" value="{{ old('icon_url') }}" 
                                   placeholder="https://example.com/icon.png">
                            <small class="form-text text-muted">URL to campus icon/logo image</small>
                            @error('icon_url')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" required>{{ old('description') }}</textarea>
                            <small class="form-text text-muted">Full description of the campus and what students will learn</small>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="teaser_description" class="form-label">Teaser Description</label>
                            <textarea class="form-control @error('teaser_description') is-invalid @enderror" 
                                      id="teaser_description" name="teaser_description" rows="2">{{ old('teaser_description') }}</textarea>
                            <small class="form-text text-muted">Short description for cards and previews</small>
                            @error('teaser_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Status Toggles -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            <strong>Active</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Campus is visible and accessible to users</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_premium" name="is_premium" value="1" 
                                               {{ old('is_premium') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_premium">
                                            <strong>Premium Campus</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Requires subscription to access</small>
                                </div>
                            </div>
                        </div>

                        <!-- Required Plans (only show if premium) -->
                        <div class="mb-3" id="required-plans-section" style="{{ old('is_premium') ? '' : 'display: none;' }}">
                            <label class="form-label">Required Subscription Plans</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="plan_prosper" name="required_plans[]" value="prosper"
                                               {{ in_array('prosper', old('required_plans', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="plan_prosper">
                                            Prosper ($49/month)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="plan_conquer" name="required_plans[]" value="conquer"
                                               {{ in_array('conquer', old('required_plans', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="plan_conquer">
                                            Conquer ($99/month)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="plan_champions" name="required_plans[]" value="champions"
                                               {{ in_array('champions', old('required_plans', [])) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="plan_champions">
                                            Champions ($199/month)
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <small class="form-text text-muted">Select which subscription plans can access this campus</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.campuses.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Campus
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Help -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campus Guidelines</h6>
                </div>
                <div class="card-body">
                    <h6>Naming Convention</h6>
                    <ul class="small text-muted mb-3">
                        <li>Use clear, descriptive names</li>
                        <li>Keep slugs short and URL-friendly</li>
                        <li>Examples: "E-commerce", "Copywriting", "Real Estate"</li>
                    </ul>

                    <h6>Content Guidelines</h6>
                    <ul class="small text-muted mb-3">
                        <li>Description should explain what students will learn</li>
                        <li>Teaser should be 1-2 sentences max</li>
                        <li>Use compelling, action-oriented language</li>
                    </ul>

                    <h6>Premium Settings</h6>
                    <ul class="small text-muted mb-3">
                        <li>Free campuses are accessible to all users</li>
                        <li>Premium campuses require active subscriptions</li>
                        <li>Select appropriate plans for campus access</li>
                    </ul>

                    <h6>Visual Design</h6>
                    <ul class="small text-muted">
                        <li>Choose colors that match campus theme</li>
                        <li>Use high-quality icons (64x64px minimum)</li>
                        <li>Maintain consistent branding</li>
                    </ul>
                </div>
            </div>

            <!-- Example Campuses -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Example Campuses</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>E-commerce</strong>
                        <p class="small text-muted mb-1">Learn to build and scale profitable online stores</p>
                        <span class="badge badge-success">Free</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong>Copywriting</strong>
                        <p class="small text-muted mb-1">Master the art of persuasive writing that sells</p>
                        <span class="badge badge-warning">Premium</span>
                    </div>
                    
                    <div>
                        <strong>Real Estate</strong>
                        <p class="small text-muted mb-1">Build wealth through property investment strategies</p>
                        <span class="badge badge-warning">Premium</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const slugInput = document.getElementById('slug');
    
    nameInput.addEventListener('input', function() {
        if (!slugInput.dataset.manual) {
            const slug = this.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-+|-+$/g, '');
            slugInput.value = slug;
        }
    });
    
    slugInput.addEventListener('input', function() {
        this.dataset.manual = 'true';
    });
    
    // Show/hide required plans based on premium toggle
    const premiumToggle = document.getElementById('is_premium');
    const requiredPlansSection = document.getElementById('required-plans-section');
    
    premiumToggle.addEventListener('change', function() {
        if (this.checked) {
            requiredPlansSection.style.display = 'block';
        } else {
            requiredPlansSection.style.display = 'none';
        }
    });
});
</script>
@endpush
