@extends('layouts.admin')

@section('title', 'Create Campus')
@section('page-title', 'Create Campus')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Create New Campus</h1>
            <p class="text-gray-400 mt-1">Add a new learning campus to the platform</p>
        </div>
        <a href="{{ route('admin.campuses.index') }}" class="btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Campuses
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <form action="{{ route('admin.campuses.store') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="bg-gray-800 rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h3 class="text-lg font-semibold text-white">Basic Information</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Campus Name *</label>
                                <input type="text" name="name" id="name" class="w-full px-3 py-2 bg-gray-700 border @error('name') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('name') }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="slug" class="block text-sm font-medium text-gray-300 mb-2">Slug *</label>
                                <input type="text" name="slug" id="slug" class="w-full px-3 py-2 bg-gray-700 border @error('slug') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('slug') }}" required>
                                <p class="mt-1 text-xs text-gray-400">URL-friendly version of the name</p>
                                @error('slug')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                            <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 bg-gray-700 border @error('description') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required>{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="teaser_description" class="block text-sm font-medium text-gray-300 mb-2">Teaser Description</label>
                            <textarea name="teaser_description" id="teaser_description" rows="2" class="w-full px-3 py-2 bg-gray-700 border @error('teaser_description') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="Short description for previews">{{ old('teaser_description') }}</textarea>
                            @error('teaser_description')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Visual Settings -->
                <div class="bg-gray-800 rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h3 class="text-lg font-semibold text-white">Visual Settings</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="icon_url" class="block text-sm font-medium text-gray-300 mb-2">Icon URL</label>
                                <input type="url" name="icon_url" id="icon_url" class="w-full px-3 py-2 bg-gray-700 border @error('icon_url') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('icon_url') }}" placeholder="https://example.com/icon.png">
                                @error('icon_url')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-300 mb-2">Theme Color</label>
                                <input type="color" name="color" id="color" class="w-full h-10 bg-gray-700 border @error('color') border-red-500 @else border-gray-600 @enderror rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('color', '#3B82F6') }}">
                                @error('color')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                            <input type="number" name="sort_order" id="sort_order" class="w-full px-3 py-2 bg-gray-700 border @error('sort_order') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('sort_order', 0) }}" min="0">
                            <p class="mt-1 text-xs text-gray-400">Lower numbers appear first</p>
                            @error('sort_order')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="bg-gray-800 rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h3 class="text-lg font-semibold text-white">Campus Settings</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="is_active" class="text-sm font-medium text-gray-300">Active Status</label>
                                <p class="text-xs text-gray-400">Whether this campus is visible to users</p>
                            </div>
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="is_premium" class="text-sm font-medium text-gray-300">Premium Campus</label>
                                <p class="text-xs text-gray-400">Requires premium subscription to access</p>
                            </div>
                            <input type="checkbox" name="is_premium" id="is_premium" value="1" {{ old('is_premium') ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Required Plans</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="required_plans[]" value="prosper" {{ in_array('prosper', old('required_plans', [])) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-300">Prosper</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="required_plans[]" value="conquer" {{ in_array('conquer', old('required_plans', [])) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-300">Conquer</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="required_plans[]" value="champions" {{ in_array('champions', old('required_plans', [])) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-300">Champions</span>
                                </label>
                            </div>
                            @error('required_plans')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('admin.campuses.index') }}" class="btn-secondary">Cancel</a>
                    <button type="submit" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Campus
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Guidelines -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Guidelines</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4 text-sm text-gray-300">
                        <div>
                            <h4 class="font-medium text-white mb-2">Campus Name</h4>
                            <p>Choose a clear, descriptive name that represents the learning focus area.</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-white mb-2">Slug</h4>
                            <p>URL-friendly identifier. Use lowercase letters, numbers, and hyphens only.</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-white mb-2">Description</h4>
                            <p>Provide a comprehensive overview of what students will learn in this campus.</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-white mb-2">Premium Settings</h4>
                            <p>Premium campuses require users to have specific subscription plans to access content.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Preview</h3>
                </div>
                <div class="p-6">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium text-white">Campus Name</h4>
                                <p class="text-xs text-gray-400">0 courses • 0 students</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300">Campus description will appear here...</p>
                        <div class="mt-3 flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate slug from name
document.getElementById('name').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('slug').value = slug;
});
</script>
@endsection
