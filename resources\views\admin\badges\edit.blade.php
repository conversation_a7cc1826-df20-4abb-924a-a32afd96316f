@extends('layouts.admin')

@section('title', 'Edit Badge - ' . $badge->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Edit Badge</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.badges.index') }}">Badges</a></li>
                    <li class="breadcrumb-item active">Edit {{ $badge->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.badges.show', $badge) }}" class="btn btn-info">
                <i class="fas fa-eye"></i> View Badge
            </a>
            <a href="{{ route('admin.badges.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Badges
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Badge Form -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.badges.update', $badge) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Badge Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $badge->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                           id="slug" name="slug" value="{{ old('slug', $badge->slug) }}" required>
                                    <small class="form-text text-muted">URL-friendly version</small>
                                    @error('slug')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3" required>{{ old('description', $badge->description) }}</textarea>
                            <small class="form-text text-muted">What this badge represents</small>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="type" class="form-label">Badge Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Select badge type</option>
                                        <option value="achievement" {{ old('type', $badge->type) === 'achievement' ? 'selected' : '' }}>Achievement</option>
                                        <option value="milestone" {{ old('type', $badge->type) === 'milestone' ? 'selected' : '' }}>Milestone</option>
                                        <option value="special" {{ old('type', $badge->type) === 'special' ? 'selected' : '' }}>Special</option>
                                        <option value="seasonal" {{ old('type', $badge->type) === 'seasonal' ? 'selected' : '' }}>Seasonal</option>
                                    </select>
                                    @error('type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">Badge Color</label>
                                    <input type="color" class="form-control form-control-color @error('color') is-invalid @enderror" 
                                           id="color" name="color" value="{{ old('color', $badge->color ?? '#6366f1') }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="icon_url" class="form-label">Icon URL</label>
                                    <input type="url" class="form-control @error('icon_url') is-invalid @enderror" 
                                           id="icon_url" name="icon_url" value="{{ old('icon_url', $badge->icon_url) }}" 
                                           placeholder="https://example.com/icon.png">
                                    <small class="form-text text-muted">URL to badge icon image</small>
                                    @error('icon_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="xp_reward" class="form-label">XP Reward</label>
                                    <input type="number" class="form-control @error('xp_reward') is-invalid @enderror" 
                                           id="xp_reward" name="xp_reward" value="{{ old('xp_reward', $badge->xp_reward) }}" min="0" max="1000">
                                    <small class="form-text text-muted">XP points awarded when earned</small>
                                    @error('xp_reward')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="requirements" class="form-label">Requirements (JSON)</label>
                            <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                      id="requirements" name="requirements" rows="4">{{ old('requirements', json_encode($badge->requirements ?? [], JSON_PRETTY_PRINT)) }}</textarea>
                            <small class="form-text text-muted">JSON object defining badge requirements</small>
                            @error('requirements')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', $badge->sort_order) }}" min="0">
                                    <small class="form-text text-muted">Display order (lower numbers first)</small>
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="is_rare" name="is_rare" value="1" 
                                               {{ old('is_rare', $badge->is_rare) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_rare">
                                            <strong>Rare Badge</strong>
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Mark as rare/special badge</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                       {{ old('is_active', $badge->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    <strong>Active</strong>
                                </label>
                            </div>
                            <small class="form-text text-muted">Badge can be earned by users</small>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ route('admin.badges.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Badge
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Badge Preview & Stats -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Preview</h6>
                </div>
                <div class="card-body text-center">
                    <div class="badge-preview mb-3">
                        <div class="badge-icon" style="width: 80px; height: 80px; background-color: {{ $badge->color ?? '#6366f1' }}; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 15px;">
                            @if($badge->icon_url)
                                <img src="{{ $badge->icon_url }}" alt="{{ $badge->name }}" style="width: 50px; height: 50px; border-radius: 50%;">
                            @else
                                <i class="fas fa-award text-white" style="font-size: 2rem;"></i>
                            @endif
                        </div>
                        <h5 class="badge-name">{{ $badge->name }}</h5>
                        <p class="badge-description text-muted">{{ $badge->description }}</p>
                        <div class="badge-meta">
                            <span class="badge badge-primary">{{ ucfirst($badge->type) }}</span>
                            <span class="badge badge-success">{{ $badge->xp_reward ?? 0 }} XP</span>
                            @if($badge->is_rare)
                                <span class="badge badge-warning">Rare</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Badge Statistics -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary">{{ $badge->users()->count() }}</h4>
                                <small class="text-muted">Total Earned</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ $badge->users()->wherePivot('created_at', '>=', now()->subDays(30))->count() }}</h4>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.badges.show', $badge) }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        @if($badge->users()->count() == 0)
                        <form action="{{ route('admin.badges.destroy', $badge) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this badge?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Badge
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const colorInput = document.getElementById('color');
    const badgeIcon = document.querySelector('.badge-icon');
    
    colorInput.addEventListener('input', function() {
        badgeIcon.style.backgroundColor = this.value;
    });

    document.getElementById('name').addEventListener('input', function() {
        document.querySelector('.badge-name').textContent = this.value || '{{ $badge->name }}';
    });

    document.getElementById('description').addEventListener('input', function() {
        document.querySelector('.badge-description').textContent = this.value || '{{ $badge->description }}';
    });

    document.getElementById('type').addEventListener('change', function() {
        document.querySelector('.badge-meta .badge-primary').textContent = this.value ? this.value.charAt(0).toUpperCase() + this.value.slice(1) : 'Achievement';
    });

    document.getElementById('xp_reward').addEventListener('input', function() {
        document.querySelector('.badge-meta .badge-success').textContent = (this.value || 0) + ' XP';
    });
});
</script>
@endpush
