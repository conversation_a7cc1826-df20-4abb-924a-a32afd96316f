<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SubscriptionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $plan = null): Response
    {
        $user = $request->user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has any active subscription
        if (!$user->subscribed()) {
            return redirect()->route('subscription.plans')
                ->with('error', 'You need an active subscription to access this feature.');
        }

        // Check for specific plan requirement
        if ($plan && !$user->subscribedToPlan($plan)) {
            return redirect()->route('subscription.plans')
                ->with('error', "You need a {$plan} subscription to access this feature.");
        }

        // Check if subscription is active and not cancelled
        if ($user->subscription() && $user->subscription()->cancelled()) {
            return redirect()->route('subscription.plans')
                ->with('warning', 'Your subscription has been cancelled. Please renew to continue accessing premium features.');
        }

        return $next($request);
    }
}
