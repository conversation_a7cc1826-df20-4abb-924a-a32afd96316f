@extends('layouts.admin')

@section('title', 'Lessons Management')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Lessons Management</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Lessons</li>
                </ol>
            </nav>
        </div>
        <a href="{{ route('admin.lessons.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Lesson
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.lessons.index') }}" class="row g-3">
                <div class="col-md-4">
                    <label for="course" class="form-label">Course</label>
                    <select name="course" id="course" class="form-select">
                        <option value="">All Courses</option>
                        @foreach($courses as $course)
                            <option value="{{ $course->id }}" {{ request('course') == $course->id ? 'selected' : '' }}>
                                {{ $course->campus->name }} - {{ $course->title }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                    <a href="{{ route('admin.lessons.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Lessons Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Lessons ({{ $lessons->total() }})
            </h6>
        </div>
        <div class="card-body">
            @if($lessons->count() > 0)
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Course</th>
                                <th>Order</th>
                                <th>Duration</th>
                                <th>XP</th>
                                <th>Status</th>
                                <th>Views</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($lessons as $lesson)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $lesson->title }}</strong>
                                        @if($lesson->is_preview)
                                            <span class="badge badge-info badge-sm ml-1">Preview</span>
                                        @endif
                                    </div>
                                    <small class="text-muted">{{ Str::limit($lesson->description, 60) }}</small>
                                </td>
                                <td>
                                    <div>
                                        <span class="badge" style="background-color: {{ $lesson->course->campus->color }}; color: white;">
                                            {{ $lesson->course->campus->name }}
                                        </span>
                                    </div>
                                    <small class="text-muted">{{ $lesson->course->title }}</small>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary">{{ $lesson->sort_order ?? 'N/A' }}</span>
                                </td>
                                <td class="text-center">
                                    @if($lesson->duration_seconds)
                                        {{ gmdate('i:s', $lesson->duration_seconds) }}
                                    @else
                                        <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-success">{{ $lesson->xp_reward ?? 0 }} XP</span>
                                </td>
                                <td>
                                    @if($lesson->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                </td>
                                <td class="text-center">
                                    <div>
                                        <strong>{{ $lesson->userProgress()->count() }}</strong>
                                    </div>
                                    <small class="text-muted">
                                        {{ $lesson->userProgress()->where('is_completed', true)->count() }} completed
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.lessons.show', $lesson) }}" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.lessons.edit', $lesson) }}" class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('admin.lessons.toggle-status', $lesson) }}" method="POST" class="d-inline">
                                            @csrf
                                            @method('PATCH')
                                            <button type="submit" class="btn btn-sm btn-outline-{{ $lesson->is_active ? 'secondary' : 'success' }}" 
                                                    title="{{ $lesson->is_active ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $lesson->is_active ? 'pause' : 'play' }}"></i>
                                            </button>
                                        </form>
                                        @if($lesson->userProgress()->count() == 0)
                                        <form action="{{ route('admin.lessons.destroy', $lesson) }}" method="POST" class="d-inline" 
                                              onsubmit="return confirm('Are you sure you want to delete this lesson?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $lessons->appends(request()->query())->links() }}
                </div>
            @else
                <div class="text-center py-4">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No lessons found</h5>
                    <p class="text-muted">Create your first lesson to get started.</p>
                    <a href="{{ route('admin.lessons.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Lesson
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const filters = ['course', 'status'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
});
</script>
@endpush
