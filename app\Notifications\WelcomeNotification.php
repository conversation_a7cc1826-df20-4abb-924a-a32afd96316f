<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class WelcomeNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('Welcome to The Real World! 🎉')
                    ->view('emails.welcome', ['user' => $notifiable]);
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Welcome to The Real World!',
            'message' => 'You\'ve successfully joined our exclusive community. Start your journey to financial freedom today.',
            'action_url' => route('dashboard.index'),
            'action_text' => 'Get Started',
            'type' => 'welcome',
            'icon' => '🎉',
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Welcome to The Real World!',
            'message' => 'You\'ve successfully joined our exclusive community.',
            'type' => 'welcome',
        ];
    }
}
