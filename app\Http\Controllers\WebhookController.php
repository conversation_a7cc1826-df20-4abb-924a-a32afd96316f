<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController as CashierWebhookController;
use App\Models\User;

class WebhookController extends CashierWebhookController
{
    /**
     * Handle customer subscription created.
     */
    public function handleCustomerSubscriptionCreated($payload)
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);

        if ($user) {
            // Update user subscription plan based on price ID
            $priceId = $payload['data']['object']['items']['data'][0]['price']['id'];
            $plan = $this->getPlanFromPriceId($priceId);

            $user->update([
                'subscription_plan' => $plan,
                'trial_ends_at' => now()->addDays(7), // 7-day trial
            ]);

            // Award welcome XP
            $user->addXp(50);
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription updated.
     */
    public function handleCustomerSubscriptionUpdated($payload)
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);

        if ($user) {
            $priceId = $payload['data']['object']['items']['data'][0]['price']['id'];
            $plan = $this->getPlanFromPriceId($priceId);
            $status = $payload['data']['object']['status'];

            $user->update([
                'subscription_plan' => $status === 'active' ? $plan : null,
                'is_active' => $status === 'active',
            ]);
        }

        return $this->successMethod();
    }

    /**
     * Handle customer subscription deleted.
     */
    public function handleCustomerSubscriptionDeleted($payload)
    {
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);

        if ($user) {
            $user->update([
                'subscription_plan' => null,
                'is_active' => true, // Keep account active but remove premium features
            ]);
        }

        return $this->successMethod();
    }

    /**
     * Get user by Stripe customer ID.
     */
    protected function getUserByStripeId($stripeId)
    {
        return User::where('stripe_id', $stripeId)->first();
    }

    /**
     * Get plan name from Stripe price ID.
     */
    protected function getPlanFromPriceId($priceId)
    {
        $planMapping = [
            'price_prosper_monthly' => 'prosper',
            'price_prosper_yearly' => 'prosper',
            'price_conquer_monthly' => 'conquer',
            'price_conquer_yearly' => 'conquer',
            'price_champions_monthly' => 'champions',
            'price_champions_yearly' => 'champions',
        ];

        return $planMapping[$priceId] ?? 'prosper';
    }
}
