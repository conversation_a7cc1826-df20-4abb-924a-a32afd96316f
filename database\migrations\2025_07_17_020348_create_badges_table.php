<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('badges', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->string('icon_url');
            $table->string('color', 7)->default('#ef4444'); // Hex color
            $table->enum('type', ['achievement', 'milestone', 'special', 'seasonal'])->default('achievement');
            $table->json('requirements')->nullable(); // JSON criteria for earning badge
            $table->integer('xp_reward')->default(0); // XP awarded when earned
            $table->boolean('is_active')->default(true);
            $table->boolean('is_rare')->default(false);
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->index(['is_active', 'type']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('badges');
    }
};
