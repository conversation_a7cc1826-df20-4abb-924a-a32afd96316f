<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\Course;
use Illuminate\Support\Facades\Auth;

class CampusController extends Controller
{
    /**
     * Display all campuses available to the user
     */
    public function index()
    {
        $user = Auth::user();

        // Get all active campuses
        $campuses = Campus::active()
            ->orderBy('sort_order')
            ->with(['courses' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }])
            ->get();

        // Filter campuses based on user access
        $accessibleCampuses = $campuses->filter(function($campus) use ($user) {
            return $campus->isAccessibleBy($user);
        });

        $restrictedCampuses = $campuses->filter(function($campus) use ($user) {
            return !$campus->isAccessibleBy($user);
        });

        return view('dashboard.campuses.index', compact('accessibleCampuses', 'restrictedCampuses', 'user'));
    }

    /**
     * Show a specific campus with its courses
     */
    public function show(Campus $campus)
    {
        $user = Auth::user();

        // Check if user can access this campus
        if (!$campus->isAccessibleBy($user)) {
            return redirect()->route('subscription.plans')
                ->with('error', 'You need to upgrade your plan to access this campus.');
        }

        // Get courses with progress
        $courses = $campus->courses()
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->with(['lessons' => function($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }])
            ->get();

        // Calculate progress for each course
        foreach ($courses as $course) {
            $totalLessons = $course->lessons->count();
            $completedLessons = $course->lessons->filter(function($lesson) use ($user) {
                return $lesson->isCompletedBy($user);
            })->count();

            $course->progress_percentage = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;
            $course->completed_lessons = $completedLessons;
            $course->total_lessons = $totalLessons;
        }

        return view('dashboard.campuses.show', compact('campus', 'courses', 'user'));
    }
}
