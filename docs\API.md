# The Real World - API Documentation

This document provides comprehensive documentation for The Real World API endpoints.

## Base URL
```
https://app.jointherealworld.com/api
```

## Authentication

All API endpoints require authentication using Laravel Sanctum tokens.

### Headers
```
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
```

## Rate Limiting

API requests are rate limited:
- **General API**: 60 requests per minute
- **Authentication**: 5 attempts per 15 minutes
- **Search**: 30 requests per minute

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Maximum requests allowed
- `X-RateLimit-Remaining`: Remaining requests in current window

## Error Responses

All errors follow a consistent format:

```json
{
    "message": "Error description",
    "errors": {
        "field": ["Validation error message"]
    },
    "code": "ERROR_CODE"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

## Authentication Endpoints

### Login
```http
POST /api/login
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>",
        "level": 5,
        "xp": 1250
    },
    "token": "1|abc123...",
    "expires_at": "2024-01-01T12:00:00Z"
}
```

### Register
```http
POST /api/register
```

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "subscription_plan": "prosper",
    "terms": true
}
```

### Logout
```http
POST /api/logout
```

## User Endpoints

### Get Current User
```http
GET /api/user
```

**Response:**
```json
{
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "level": 5,
    "xp": 1250,
    "subscription_plan": "prosper",
    "created_at": "2024-01-01T00:00:00Z"
}
```

### Get User Statistics
```http
GET /api/user/stats
```

**Response:**
```json
{
    "total_xp": 1250,
    "current_level": 5,
    "login_streak": 7,
    "courses_enrolled": 12,
    "courses_completed": 8,
    "events_attended": 15,
    "badges_earned": 23,
    "achievements_unlocked": 45
}
```

### Get Daily Tasks
```http
GET /api/user/daily-tasks
```

**Response:**
```json
[
    {
        "id": 1,
        "title": "Daily Login",
        "description": "Log in to your account",
        "xp_reward": 10,
        "progress": {
            "current": 1,
            "target": 1,
            "percentage": 100,
            "is_completed": true
        }
    }
]
```

### Complete Task
```http
POST /api/user/tasks/{taskId}/complete
```

**Response:**
```json
{
    "success": true,
    "xp_earned": 10,
    "task_completed": true
}
```

## Course Endpoints

### List Courses
```http
GET /api/courses
```

**Query Parameters:**
- `search` - Search term
- `campus_id` - Filter by campus
- `difficulty` - Filter by difficulty (beginner, intermediate, advanced)
- `category` - Filter by category
- `sort` - Sort by (newest, popular, rating)
- `per_page` - Results per page (default: 12)

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "title": "Cryptocurrency Fundamentals",
            "description": "Learn the basics of cryptocurrency",
            "difficulty_level": "beginner",
            "estimated_duration": 120,
            "rating": 4.8,
            "campus": {
                "id": 1,
                "name": "Crypto Campus"
            },
            "enrollment_count": 1250
        }
    ],
    "meta": {
        "current_page": 1,
        "total": 50,
        "per_page": 12
    }
}
```

### Enroll in Course
```http
POST /api/courses/{courseId}/enroll
```

**Response:**
```json
{
    "success": true,
    "enrollment": {
        "id": 1,
        "course_id": 1,
        "user_id": 1,
        "progress_percentage": 0,
        "enrolled_at": "2024-01-01T12:00:00Z"
    }
}
```

### Get Course Progress
```http
GET /api/courses/{courseId}/progress
```

**Response:**
```json
{
    "progress": 75,
    "completed_lessons": 8,
    "total_lessons": 12,
    "time_spent": "4h 30m",
    "last_accessed": "2024-01-01T12:00:00Z"
}
```

## Event Endpoints

### List Events
```http
GET /api/events
```

**Query Parameters:**
- `upcoming` - Show only upcoming events (true/false)
- `category` - Filter by category
- `date_from` - Start date filter (YYYY-MM-DD)
- `date_to` - End date filter (YYYY-MM-DD)

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "title": "Trading Masterclass",
            "description": "Advanced trading strategies",
            "start_time": "2024-01-15T18:00:00Z",
            "end_time": "2024-01-15T20:00:00Z",
            "category": "masterclass",
            "max_attendees": 100,
            "rsvp_count": 85,
            "user_rsvp": true
        }
    ]
}
```

### RSVP to Event
```http
POST /api/events/{eventId}/rsvp
```

**Response:**
```json
{
    "success": true,
    "message": "Successfully RSVP'd to event"
}
```

### Cancel RSVP
```http
DELETE /api/events/{eventId}/rsvp
```

**Response:**
```json
{
    "success": true,
    "message": "RSVP cancelled successfully"
}
```

## Notification Endpoints

### Get Notifications
```http
GET /api/notifications
```

**Query Parameters:**
- `unread_only` - Show only unread notifications (true/false)
- `limit` - Number of notifications to return (default: 20)

**Response:**
```json
{
    "data": [
        {
            "id": "uuid-123",
            "type": "achievement",
            "title": "Achievement Unlocked!",
            "message": "You've earned the Course Crusher achievement!",
            "icon": "🏆",
            "action_url": "/dashboard/badges",
            "action_text": "View Achievements",
            "read_at": null,
            "created_at": "2024-01-01T12:00:00Z"
        }
    ],
    "unread_count": 5
}
```

### Mark Notification as Read
```http
POST /api/notifications/{notificationId}/read
```

**Response:**
```json
{
    "success": true,
    "message": "Notification marked as read"
}
```

### Mark All Notifications as Read
```http
POST /api/notifications/mark-all-read
```

**Response:**
```json
{
    "success": true,
    "marked_count": 12
}
```

## Search Endpoints

### Quick Search
```http
GET /api/search/quick
```

**Query Parameters:**
- `q` - Search query (required)
- `type` - Search type (all, courses, events, users)

**Response:**
```json
{
    "courses": [
        {
            "id": 1,
            "title": "Cryptocurrency Fundamentals",
            "description": "Learn the basics..."
        }
    ],
    "events": [
        {
            "id": 1,
            "title": "Trading Masterclass",
            "start_time": "2024-01-15T18:00:00Z"
        }
    ],
    "users": [
        {
            "id": 1,
            "name": "John Doe",
            "level": 5
        }
    ]
}
```

## Leaderboard Endpoints

### Get Leaderboard
```http
GET /api/leaderboard/{type}
```

**Parameters:**
- `type` - Leaderboard type (xp, courses, badges)

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "John Doe",
            "avatar": "avatar-url",
            "xp": 5000,
            "level": 15,
            "rank": 1
        }
    ],
    "user_rank": 25,
    "user_stats": {
        "xp": 1250,
        "level": 5
    }
}
```

## Chat Endpoints

### Get Messages
```http
GET /api/chat/messages
```

**Query Parameters:**
- `limit` - Number of messages (default: 50)
- `before` - Get messages before timestamp

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "message": "Hello everyone!",
            "user": {
                "id": 1,
                "name": "John Doe",
                "level": 5
            },
            "created_at": "2024-01-01T12:00:00Z"
        }
    ]
}
```

### Send Message
```http
POST /api/chat/messages
```

**Request Body:**
```json
{
    "message": "Hello everyone!"
}
```

**Response:**
```json
{
    "id": 1,
    "message": "Hello everyone!",
    "user": {
        "id": 1,
        "name": "John Doe",
        "level": 5
    },
    "created_at": "2024-01-01T12:00:00Z"
}
```

## Dashboard Stats

### Get Dashboard Statistics
```http
GET /api/dashboard/stats
```

**Response:**
```json
{
    "user": {
        "name": "John Doe",
        "level": 5,
        "xp": 1250,
        "login_streak": 7
    },
    "progress": {
        "courses_enrolled": 12,
        "courses_completed": 8,
        "events_attended": 15,
        "badges_earned": 23
    },
    "recent_activity": {
        "recent_courses": [...],
        "upcoming_events": [...]
    }
}
```

## WebSocket Events

The application supports real-time features via WebSocket:

### Channels
- `user.{userId}` - User-specific notifications
- `chat` - Global chat messages
- `leaderboard` - Leaderboard updates

### Events
- `user.leveled-up` - User level increase
- `achievement.unlocked` - New achievement
- `message.sent` - New chat message
- `event.reminder` - Event reminder

## SDK Examples

### JavaScript
```javascript
// Initialize API client
const api = new RealWorldAPI('your-token');

// Get user stats
const stats = await api.user.getStats();

// Enroll in course
await api.courses.enroll(courseId);

// Send chat message
await api.chat.sendMessage('Hello!');
```

### PHP
```php
// Initialize API client
$api = new RealWorldAPI('your-token');

// Get notifications
$notifications = $api->notifications()->get();

// RSVP to event
$api->events()->rsvp($eventId);
```

## Webhooks

The application supports webhooks for external integrations:

### Stripe Webhooks
- `payment_intent.succeeded`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`

### Custom Webhooks
- `user.registered`
- `course.completed`
- `achievement.earned`

Configure webhook URLs in the admin panel.
