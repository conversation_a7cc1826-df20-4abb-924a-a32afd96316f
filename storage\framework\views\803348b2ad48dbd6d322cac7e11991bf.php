<?php $__env->startSection('title', 'Revenue Analytics'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-white">Revenue Analytics</h1>
                <p class="text-gray-400 mt-1">Track subscription revenue and financial metrics</p>
            </div>
            <a href="<?php echo e(route('admin.analytics.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Analytics
            </a>
        </div>
    </div>

    <!-- Revenue Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Monthly Revenue -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Monthly Revenue</p>
                    <p class="text-2xl font-bold text-white">$<?php echo e(number_format($stats['monthly_revenue'], 2)); ?></p>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 8h6m-5 0a3 3 0 110 6H9l3 3-3-3h1.5a2.5 2.5 0 100-5H9z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Revenue</p>
                    <p class="text-2xl font-bold text-white">$<?php echo e(number_format($stats['total_revenue'], 2)); ?></p>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Active Subscriptions</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['active_subscriptions'])); ?></p>
                </div>
            </div>
        </div>

        <!-- Average Revenue Per User -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">ARPU</p>
                    <p class="text-2xl font-bold text-white">$<?php echo e(number_format($stats['arpu'], 2)); ?></p>
                    <p class="text-xs text-gray-400">Average Revenue Per User</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Revenue by Month -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Revenue by Month</h3>
            </div>
            <div class="p-6">
                <?php if($revenueByMonth->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $revenueByMonth; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300"><?php echo e(\Carbon\Carbon::createFromFormat('Y-m', $month->month)->format('M Y')); ?></span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="bg-green-600 h-2 rounded-full" 
                                             style="width: <?php echo e($revenueByMonth->max('revenue') > 0 ? ($month->revenue / $revenueByMonth->max('revenue')) * 100 : 0); ?>%"></div>
                                    </div>
                                    <span class="text-white font-medium">$<?php echo e(number_format($month->revenue, 0)); ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-400 text-center py-8">No revenue data available</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Subscription Plans -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Revenue by Plan</h3>
            </div>
            <div class="p-6">
                <?php if($revenueByPlan->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $revenueByPlan; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-3 
                                        <?php echo e($plan->plan_name === 'prosper' ? 'bg-blue-500' : ''); ?>

                                        <?php echo e($plan->plan_name === 'conquer' ? 'bg-purple-500' : ''); ?>

                                        <?php echo e($plan->plan_name === 'champions' ? 'bg-yellow-500' : ''); ?>">
                                    </div>
                                    <span class="text-gray-300 capitalize"><?php echo e($plan->plan_name); ?></span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-24 bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="h-2 rounded-full
                                            <?php echo e($plan->plan_name === 'prosper' ? 'bg-blue-500' : ''); ?>

                                            <?php echo e($plan->plan_name === 'conquer' ? 'bg-purple-500' : ''); ?>

                                            <?php echo e($plan->plan_name === 'champions' ? 'bg-yellow-500' : ''); ?>" 
                                             style="width: <?php echo e($revenueByPlan->max('revenue') > 0 ? ($plan->revenue / $revenueByPlan->max('revenue')) * 100 : 0); ?>%"></div>
                                    </div>
                                    <span class="text-white font-medium">$<?php echo e(number_format($plan->revenue, 0)); ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-400 text-center py-8">No plan revenue data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Subscription Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Churn Rate -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Churn Rate</h3>
                <div class="p-2 bg-red-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2"><?php echo e(number_format($stats['churn_rate'], 1)); ?>%</p>
            <p class="text-sm text-gray-400">Monthly churn rate</p>
        </div>

        <!-- Lifetime Value -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Customer LTV</h3>
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2">$<?php echo e(number_format($stats['customer_ltv'], 0)); ?></p>
            <p class="text-sm text-gray-400">Average customer lifetime value</p>
        </div>

        <!-- Conversion Rate -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Conversion Rate</h3>
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2"><?php echo e(number_format($stats['conversion_rate'], 1)); ?>%</p>
            <p class="text-sm text-gray-400">Free to paid conversion</p>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/analytics/revenue.blade.php ENDPATH**/ ?>