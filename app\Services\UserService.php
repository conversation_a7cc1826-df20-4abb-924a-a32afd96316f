<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserTask;
use App\Models\DailyTask;
use App\Notifications\WelcomeNotification;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class UserService
{
    /**
     * Create a new user
     */
    public function createUser(array $data): User
    {
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'subscription_plan' => $data['subscription_plan'] ?? 'free',
            'xp' => 0,
            'level' => 1,
            'login_streak' => 0,
            'last_seen_at' => now(),
            'email_verified_at' => $data['email_verified_at'] ?? null,
        ]);

        // Send welcome notification
        $user->notify(new WelcomeNotification());

        // Award first login achievement
        app(AchievementService::class)->checkAchievements($user, 'first_login');

        // Assign daily tasks
        $this->assignDailyTasks($user);

        return $user;
    }

    /**
     * Update user profile
     */
    public function updateProfile(User $user, array $data): bool
    {
        $updateData = [];

        if (isset($data['name'])) {
            $updateData['name'] = $data['name'];
        }

        if (isset($data['bio'])) {
            $updateData['bio'] = $data['bio'];
        }

        if (isset($data['location'])) {
            $updateData['location'] = $data['location'];
        }

        if (isset($data['social_twitter'])) {
            $updateData['social_twitter'] = $data['social_twitter'];
        }

        if (isset($data['social_instagram'])) {
            $updateData['social_instagram'] = $data['social_instagram'];
        }

        if (isset($data['social_linkedin'])) {
            $updateData['social_linkedin'] = $data['social_linkedin'];
        }

        return $user->update($updateData);
    }

    /**
     * Update user password
     */
    public function updatePassword(User $user, string $newPassword): bool
    {
        return $user->update([
            'password' => Hash::make($newPassword),
        ]);
    }

    /**
     * Handle user login
     */
    public function handleLogin(User $user): void
    {
        $lastLogin = $user->last_seen_at;
        $now = now();

        // Update last login
        $user->update([
            'last_seen_at' => $now,
            'last_activity_at' => $now,
        ]);

        // Calculate login streak
        if ($lastLogin) {
            $daysSinceLastLogin = $lastLogin->diffInDays($now);
            
            if ($daysSinceLastLogin === 1) {
                // Consecutive day login
                $user->increment('login_streak');
            } elseif ($daysSinceLastLogin > 1) {
                // Streak broken
                $user->update(['login_streak' => 1]);
            }
            // Same day login doesn't change streak
        } else {
            // First login
            $user->update(['login_streak' => 1]);
        }

        // Check for streak achievements
        app(AchievementService::class)->checkAchievements($user, 'login_streak', [
            'streak' => $user->login_streak,
        ]);

        // Assign daily tasks if not already assigned today
        $this->assignDailyTasks($user);
    }

    /**
     * Assign daily tasks to user
     */
    public function assignDailyTasks(User $user): void
    {
        $today = now()->format('Y-m-d');

        // Check if user already has tasks for today
        $existingTasks = UserTask::where('user_id', $user->id)
            ->whereDate('completion_date', $today)
            ->count();

        if ($existingTasks > 0) {
            return; // Already has tasks for today
        }

        // Get random daily tasks appropriate for user level
        $dailyTasks = DailyTask::getRandomTasksForUser($user, 3);

        foreach ($dailyTasks as $task) {
            UserTask::create([
                'user_id' => $user->id,
                'task_id' => $task->id,
                'completion_date' => $today,
                'xp_earned' => $task->xp_reward,
                'completed_at' => now(),
            ]);
        }
    }

    /**
     * Get user statistics
     */
    public function getUserStats(User $user): array
    {
        return [
            'total_xp' => $user->xp,
            'current_level' => $user->level,
            'login_streak' => $user->login_streak ?? 0,
            'courses_enrolled' => $user->courseEnrollments()->count(),
            'courses_completed' => $user->courseEnrollments()->where('progress_percentage', 100)->count(),
            'events_attended' => $user->eventRsvps()->where('attended', true)->count(),
            'badges_earned' => $user->badges()->count(),
            'achievements_unlocked' => $user->achievements()->count(),
            'tasks_completed_today' => $user->completedTasks()
                ->whereDate('completed_at', today())
                ->count(),
            'total_tasks_completed' => $user->completedTasks()->count(),
            'chat_messages_sent' => $user->chatMessages()->count(),
            'member_since' => $user->created_at->format('M Y'),
            'last_active' => $user->last_activity_at?->diffForHumans(),
        ];
    }

    /**
     * Get user's daily tasks
     */
    public function getUserDailyTasks(User $user, ?Carbon $date = null): array
    {
        $date = $date ?? now();
        $dateString = $date->format('Y-m-d');

        return UserTask::where('user_id', $user->id)
            ->whereDate('assigned_date', $dateString)
            ->with('task')
            ->get()
            ->map(function ($userTask) {
                $progress = $userTask->task->getProgressForUser($userTask->user);
                return [
                    'user_task' => $userTask,
                    'task' => $userTask->task,
                    'progress' => $progress,
                ];
            })
            ->toArray();
    }

    /**
     * Complete a user task
     */
    public function completeTask(User $user, int $taskId, int $progressIncrement = 1): bool
    {
        $today = now()->format('Y-m-d');

        // Check if task is already completed today
        $existingTask = UserTask::where('user_id', $user->id)
            ->where('task_id', $taskId)
            ->whereDate('completion_date', $today)
            ->first();

        if ($existingTask) {
            return false; // Already completed today
        }

        // Get the task details
        $task = \App\Models\Task::find($taskId);
        if (!$task) {
            return false;
        }

        // Create completion record
        UserTask::create([
            'user_id' => $user->id,
            'task_id' => $taskId,
            'completed_at' => now(),
            'completion_date' => $today,
            'xp_earned' => $task->xp_reward,
        ]);

        // Award XP
        $user->addXp($task->xp_reward);

        return true;
    }

    /**
     * Get leaderboard data
     */
    public function getLeaderboard(string $type = 'xp', int $limit = 10): array
    {
        return app(AchievementService::class)->getLeaderboard($type, $limit);
    }

    /**
     * Update user subscription
     */
    public function updateSubscription(User $user, string $plan): bool
    {
        $oldPlan = $user->subscription_plan;
        
        $updated = $user->update(['subscription_plan' => $plan]);

        if ($updated && $plan !== 'free' && $oldPlan === 'free') {
            // Award achievement for upgrading
            app(AchievementService::class)->checkAchievements($user, 'subscription_upgrade', [
                'plan' => $plan,
                'previous_plan' => $oldPlan,
            ]);
        }

        return $updated;
    }

    /**
     * Deactivate user account
     */
    public function deactivateUser(User $user): bool
    {
        return $user->update([
            'is_active' => false,
            'deactivated_at' => now(),
        ]);
    }

    /**
     * Reactivate user account
     */
    public function reactivateUser(User $user): bool
    {
        return $user->update([
            'is_active' => true,
            'deactivated_at' => null,
        ]);
    }

    /**
     * Get user activity summary
     */
    public function getActivitySummary(User $user, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'logins' => $user->where('last_seen_at', '>=', $startDate)->count(),
            'courses_started' => $user->courseEnrollments()
                ->where('enrolled_at', '>=', $startDate)
                ->count(),
            'courses_completed' => $user->courseEnrollments()
                ->where('completed_at', '>=', $startDate)
                ->count(),
            'events_attended' => $user->eventRsvps()
                ->where('attended', true)
                ->where('updated_at', '>=', $startDate)
                ->count(),
            'tasks_completed' => $user->completedTasks()
                ->where('completed_at', '>=', $startDate)
                ->count(),
            'achievements_earned' => $user->achievements()
                ->where('earned_at', '>=', $startDate)
                ->count(),
            'xp_gained' => $user->achievements()
                ->where('earned_at', '>=', $startDate)
                ->sum('xp_reward'),
        ];
    }

    /**
     * Search users
     */
    public function searchUsers(string $query, array $filters = []): array
    {
        $userQuery = User::where('is_active', true);

        // Text search
        if (!empty($query)) {
            $userQuery->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('email', 'like', "%{$query}%");
            });
        }

        // Apply filters
        if (isset($filters['subscription_plan'])) {
            $userQuery->where('subscription_plan', $filters['subscription_plan']);
        }

        if (isset($filters['min_level'])) {
            $userQuery->where('level', '>=', $filters['min_level']);
        }

        if (isset($filters['min_xp'])) {
            $userQuery->where('xp', '>=', $filters['min_xp']);
        }

        // Sorting
        $sortBy = $filters['sort_by'] ?? 'xp';
        switch ($sortBy) {
            case 'name':
                $userQuery->orderBy('name', 'asc');
                break;
            case 'level':
                $userQuery->orderBy('level', 'desc');
                break;
            case 'recent':
                $userQuery->orderBy('created_at', 'desc');
                break;
            default: // xp
                $userQuery->orderBy('xp', 'desc');
                break;
        }

        return $userQuery->paginate($filters['per_page'] ?? 20)->toArray();
    }
}
