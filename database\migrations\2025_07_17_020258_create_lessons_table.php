<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lessons', function (Blueprint $table) {
            $table->id();
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('video_url')->nullable();
            $table->enum('video_type', ['youtube', 'vimeo', 'direct', 'embed'])->default('youtube');
            $table->string('resource_url')->nullable(); // Downloadable resources
            $table->text('content')->nullable(); // Text content/transcript
            $table->integer('duration_seconds')->nullable();
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_preview')->default(false); // Can be viewed without subscription
            $table->integer('xp_reward')->default(10); // XP awarded for completion
            $table->timestamps();

            $table->index(['course_id', 'sort_order']);
            $table->index(['is_active', 'is_preview']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lessons');
    }
};
