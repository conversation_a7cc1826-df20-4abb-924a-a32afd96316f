<?php $__env->startSection('title', $course->title . ' - The Real World'); ?>
<?php $__env->startSection('page-title', $course->title); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Course Header -->
    <div class="card mb-8">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center mb-4">
                    <a href="<?php echo e(route('dashboard.campuses.show', $campus)); ?>" 
                       class="text-primary-400 hover:text-primary-300 text-sm font-medium mr-2">
                        <?php echo e($campus->name); ?>

                    </a>
                    <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                    <span class="text-gray-400 text-sm"><?php echo e($course->title); ?></span>
                </div>
                
                <h1 class="text-3xl font-bold mb-4"><?php echo e($course->title); ?></h1>
                <p class="text-gray-300 mb-6 leading-relaxed"><?php echo e($course->description); ?></p>
                
                <!-- Course Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-primary-400"><?php echo e($totalLessons); ?></p>
                        <p class="text-sm text-gray-400">Lessons</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-400"><?php echo e($completedLessons); ?></p>
                        <p class="text-sm text-gray-400">Completed</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-400"><?php echo e(round($courseProgress)); ?>%</p>
                        <p class="text-sm text-gray-400">Progress</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-yellow-400"><?php echo e($course->duration_minutes); ?>m</p>
                        <p class="text-sm text-gray-400">Duration</p>
                    </div>
                </div>
            </div>
            
            <!-- Difficulty Badge -->
            <span class="bg-<?php echo e($course->difficulty === 'beginner' ? 'green' : ($course->difficulty === 'intermediate' ? 'yellow' : 'red')); ?>-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                <?php echo e(ucfirst($course->difficulty)); ?>

            </span>
        </div>

        <!-- Progress Bar -->
        <div class="mt-6">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-300">Course Progress</span>
                <span class="text-sm text-gray-400"><?php echo e($completedLessons); ?>/<?php echo e($totalLessons); ?> lessons</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-3">
                <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-500" 
                     style="width: <?php echo e($courseProgress); ?>%"></div>
            </div>
        </div>
    </div>

    <!-- Quick Action -->
    <?php if($nextLesson): ?>
    <div class="card mb-8 bg-gradient-to-r from-primary-600 to-primary-700">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-white mb-2">
                    <?php echo e($courseProgress > 0 ? 'Continue Learning' : 'Start Your Journey'); ?>

                </h3>
                <p class="text-primary-100">
                    Next: <?php echo e($nextLesson->title); ?>

                </p>
            </div>
            <a href="<?php echo e(route('dashboard.lessons.show', $nextLesson)); ?>" 
               class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-colors">
                <?php echo e($courseProgress > 0 ? 'Continue' : 'Start Now'); ?>

            </a>
        </div>
    </div>
    <?php endif; ?>

    <!-- Lessons List -->
    <div class="card">
        <h3 class="text-xl font-semibold mb-6">Course Lessons</h3>
        
        <div class="space-y-4">
            <?php $__currentLoopData = $lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="flex items-center p-4 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors <?php echo e($lesson->is_completed ? 'bg-green-900/20' : 'bg-gray-800/50'); ?>">
                <!-- Lesson Number -->
                <div class="w-10 h-10 rounded-full flex items-center justify-center mr-4 flex-shrink-0 <?php echo e($lesson->is_completed ? 'bg-green-600' : 'bg-gray-700'); ?>">
                    <?php if($lesson->is_completed): ?>
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    <?php else: ?>
                        <span class="text-white font-semibold"><?php echo e($index + 1); ?></span>
                    <?php endif; ?>
                </div>

                <!-- Lesson Info -->
                <div class="flex-1">
                    <div class="flex items-center justify-between">
                        <h4 class="font-semibold text-lg <?php echo e($lesson->is_completed ? 'text-green-400' : 'text-white'); ?>">
                            <?php echo e($lesson->title); ?>

                        </h4>
                        <div class="flex items-center space-x-4 text-sm text-gray-400">
                            <?php if($lesson->is_preview): ?>
                                <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs">Preview</span>
                            <?php endif; ?>
                            <span><?php echo e($lesson->getFormattedDuration()); ?></span>
                            <span><?php echo e($lesson->xp_reward); ?> XP</span>
                        </div>
                    </div>
                    
                    <?php if($lesson->description): ?>
                    <p class="text-gray-400 mt-1"><?php echo e($lesson->description); ?></p>
                    <?php endif; ?>

                    <!-- Progress Bar for Individual Lesson -->
                    <?php if($lesson->progress_percentage > 0 && !$lesson->is_completed): ?>
                    <div class="mt-2">
                        <div class="w-full bg-gray-700 rounded-full h-1">
                            <div class="bg-primary-600 h-1 rounded-full" style="width: <?php echo e($lesson->progress_percentage); ?>%"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1"><?php echo e(round($lesson->progress_percentage)); ?>% watched</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Action Button -->
                <div class="ml-4">
                    <?php if($lesson->is_preview || $campus->isAccessibleBy($user)): ?>
                        <a href="<?php echo e(route('dashboard.lessons.show', $lesson)); ?>" 
                           class="btn-<?php echo e($lesson->is_completed ? 'secondary' : 'primary'); ?> px-4 py-2">
                            <?php echo e($lesson->is_completed ? 'Review' : ($lesson->progress_percentage > 0 ? 'Continue' : 'Start')); ?>

                        </a>
                    <?php else: ?>
                        <span class="text-gray-500 text-sm">
                            <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                            Premium
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Back Button -->
    <div class="mt-8">
        <a href="<?php echo e(route('dashboard.campuses.show', $campus)); ?>" 
           class="inline-flex items-center text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to <?php echo e($campus->name); ?>

        </a>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/courses/show.blade.php ENDPATH**/ ?>