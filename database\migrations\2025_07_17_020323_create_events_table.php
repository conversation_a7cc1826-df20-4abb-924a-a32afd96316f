<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('campus_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('title');
            $table->text('description');
            $table->string('thumbnail_url')->nullable();
            $table->timestamp('start_datetime')->nullable();
            $table->timestamp('end_datetime')->nullable();
            $table->string('video_url')->nullable(); // Live stream URL
            $table->string('recording_url')->nullable(); // Recording after event
            $table->enum('status', ['scheduled', 'live', 'completed', 'cancelled'])->default('scheduled');
            $table->integer('max_attendees')->nullable();
            $table->boolean('is_premium')->default(false); // Requires subscription
            $table->json('required_plans')->nullable(); // JSON array of required subscription plans
            $table->timestamps();

            $table->index(['start_datetime', 'status']);
            $table->index(['campus_id', 'status']);
            $table->index('is_premium');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('events');
    }
};
