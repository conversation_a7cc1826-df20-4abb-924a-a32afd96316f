@extends('layouts.app')

@section('title', 'Page Not Found - The Real World')

@section('content')
<div class="min-h-screen flex items-center justify-center px-4">
    <div class="max-w-2xl mx-auto text-center">
        <!-- Error Animation -->
        <div class="mb-8">
            <div class="relative">
                <div class="text-9xl font-bold text-primary-600 opacity-20 select-none">404</div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="w-32 h-32 bg-gradient-to-br from-primary-600 to-primary-800 rounded-full flex items-center justify-center animate-pulse">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.935-6.072-2.456M15 17h5l-5 5v-5zM4 19h6v-2H4v2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message -->
        <div class="mb-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">Page Not Found</h1>
            <p class="text-xl text-gray-400 mb-6">
                The page you're looking for doesn't exist or has been moved.
            </p>
            <p class="text-gray-500">
                Don't worry, even the most successful entrepreneurs take wrong turns sometimes.
            </p>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <a href="{{ route('home') }}" 
               class="flex items-center justify-center p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                <svg class="w-6 h-6 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
                <div class="text-left">
                    <div class="font-semibold">Go Home</div>
                    <div class="text-sm text-gray-400">Back to homepage</div>
                </div>
            </a>

            @auth
                <a href="{{ route('dashboard.index') }}" 
                   class="flex items-center justify-center p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                    <svg class="w-6 h-6 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <div class="text-left">
                        <div class="font-semibold">Dashboard</div>
                        <div class="text-sm text-gray-400">Your learning hub</div>
                    </div>
                </a>
            @else
                <a href="{{ route('register') }}" 
                   class="flex items-center justify-center p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                    <svg class="w-6 h-6 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                    <div class="text-left">
                        <div class="font-semibold">Join Now</div>
                        <div class="text-sm text-gray-400">Start your journey</div>
                    </div>
                </a>
            @endauth

            <a href="{{ route('search') }}" 
               class="flex items-center justify-center p-4 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors">
                <svg class="w-6 h-6 mr-3 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <div class="text-left">
                    <div class="font-semibold">Search</div>
                    <div class="text-sm text-gray-400">Find what you need</div>
                </div>
            </a>
        </div>

        <!-- Popular Links -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold mb-4">Popular Pages</h3>
            <div class="flex flex-wrap justify-center gap-2">
                <a href="{{ route('campuses') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                    Campuses
                </a>
                <a href="{{ route('pricing') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                    Pricing
                </a>
                <a href="{{ route('testimonials') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                    Testimonials
                </a>
                <a href="{{ route('faq') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                    FAQ
                </a>
                @auth
                    <a href="{{ route('dashboard.courses.index') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                        Courses
                    </a>
                    <a href="{{ route('dashboard.events.index') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                        Events
                    </a>
                    <a href="{{ route('dashboard.leaderboard.index') }}" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-full text-sm transition-colors">
                        Leaderboard
                    </a>
                @endauth
            </div>
        </div>

        <!-- Contact Support -->
        <div class="p-6 bg-gray-800 rounded-lg">
            <h3 class="text-lg font-semibold mb-2">Still Need Help?</h3>
            <p class="text-gray-400 mb-4">
                If you believe this is an error or you need assistance, our support team is here to help.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="mailto:<EMAIL>" 
                   class="btn-secondary inline-flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    Contact Support
                </a>
                <button onclick="window.history.back()" 
                        class="btn-primary inline-flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Go Back
                </button>
            </div>
        </div>

        <!-- Motivational Quote -->
        <div class="mt-8 p-4 border-l-4 border-primary-600 bg-gray-800/50">
            <blockquote class="text-lg italic text-gray-300">
                "Success is not final, failure is not fatal: it is the courage to continue that counts."
            </blockquote>
            <cite class="block mt-2 text-sm text-gray-400">- Winston Churchill</cite>
        </div>
    </div>
</div>

<style>
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}
</style>

<script>
// Add some interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Add floating animation to the error icon
    const errorIcon = document.querySelector('.animate-pulse');
    if (errorIcon) {
        errorIcon.classList.add('animate-float');
    }
    
    // Track 404 errors for analytics (if you have analytics)
    if (typeof gtag !== 'undefined') {
        gtag('event', 'page_not_found', {
            'page_location': window.location.href,
            'page_referrer': document.referrer
        });
    }
});
</script>
@endsection
