<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Campus extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'icon_url',
        'description',
        'teaser_description',
        'color',
        'sort_order',
        'is_active',
        'is_premium',
        'required_plans',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_premium' => 'boolean',
        'required_plans' => 'array',
    ];

    /**
     * Relationships
     */
    public function courses(): HasMany
    {
        return $this->hasMany(Course::class);
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function chatRooms(): HasMany
    {
        return $this->hasMany(ChatRoom::class);
    }

    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    /**
     * Helper Methods
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function isAccessibleBy(User $user): bool
    {
        if (!$this->is_premium) {
            return true;
        }

        return $user->canAccessCampus($this);
    }
}
