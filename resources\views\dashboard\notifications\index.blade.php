@extends('layouts.dashboard')

@section('title', 'Notifications - The Real World')
@section('page-title', 'Notifications')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold mb-2">Notifications</h2>
            <p class="text-gray-400">Stay updated with your latest activities and announcements</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="markAllAsRead()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Mark All Read
            </button>
            <button onclick="clearAllNotifications()" class="btn-secondary text-red-400 hover:text-red-300">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Clear All
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <select id="typeFilter" class="input-field w-auto">
                <option value="">All Types</option>
                <option value="welcome">Welcome</option>
                <option value="subscription">Subscription</option>
                <option value="event_reminder">Event Reminders</option>
                <option value="course_completed">Course Completed</option>
                <option value="achievement">Achievements</option>
            </select>
            
            <label class="flex items-center">
                <input type="checkbox" id="unreadOnly" class="w-4 h-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500">
                <span class="ml-3 text-sm">Show unread only</span>
            </label>
            
            <button onclick="loadNotifications()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
    </div>

    <!-- Notifications List -->
    <div id="notificationsList" class="space-y-4">
        <!-- Notifications will be loaded here -->
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
        <p class="text-gray-400">Loading notifications...</p>
    </div>

    <!-- Empty State -->
    <div id="emptyState" class="text-center py-12 hidden">
        <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V11H1V7.414a2 2 0 01.586-1.414l.707-.707a5 5 0 013.535-1.465z"></path>
            </svg>
        </div>
        <h3 class="text-xl font-semibold mb-2">No notifications found</h3>
        <p class="text-gray-400">You're all caught up! Check back later for updates.</p>
    </div>

    <!-- Pagination -->
    <div id="pagination" class="mt-8 flex items-center justify-center space-x-2 hidden">
        <!-- Pagination will be loaded here -->
    </div>
</div>

<script>
let currentPage = 1;

// Load notifications on page load
document.addEventListener('DOMContentLoaded', function() {
    loadNotifications();
    
    // Add event listeners for filters
    document.getElementById('typeFilter').addEventListener('change', () => loadNotifications());
    document.getElementById('unreadOnly').addEventListener('change', () => loadNotifications());
});

// Load notifications function
async function loadNotifications(page = 1) {
    const typeFilter = document.getElementById('typeFilter');
    const unreadOnly = document.getElementById('unreadOnly');
    
    const params = new URLSearchParams({
        page: page,
        type: typeFilter.value,
        unread_only: unreadOnly.checked ? '1' : '0',
    });
    
    // Show loading state
    document.getElementById('loadingState').classList.remove('hidden');
    document.getElementById('notificationsList').innerHTML = '';
    document.getElementById('emptyState').classList.add('hidden');
    
    try {
        const response = await fetch(`/dashboard/notifications?${params}`, {
            headers: {
                'Accept': 'application/json',
            }
        });
        const data = await response.json();
        
        if (data.success) {
            renderNotifications(data.notifications);
            renderPagination(data.pagination);
            
            if (data.notifications.length === 0) {
                document.getElementById('emptyState').classList.remove('hidden');
            }
        }
    } catch (error) {
        console.error('Error loading notifications:', error);
    } finally {
        document.getElementById('loadingState').classList.add('hidden');
    }
}

// Render notifications
function renderNotifications(notifications) {
    const container = document.getElementById('notificationsList');
    
    container.innerHTML = notifications.map(notification => `
        <div class="notification-item card ${notification.read_at ? 'opacity-75' : 'border-l-4 border-primary-500'}" 
             data-id="${notification.id}">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-4 flex-1">
                    <div class="text-2xl">${notification.icon}</div>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-1">
                            <h4 class="font-semibold">${notification.title}</h4>
                            ${!notification.read_at ? '<span class="w-2 h-2 bg-primary-500 rounded-full"></span>' : ''}
                        </div>
                        <p class="text-gray-400 text-sm mb-2">${notification.message}</p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>${notification.created_at}</span>
                            ${notification.action_url ? `<a href="${notification.action_url}" class="text-primary-400 hover:text-primary-300">${notification.action_text || 'View'}</a>` : ''}
                        </div>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    ${!notification.read_at ? `<button onclick="markAsRead('${notification.id}')" class="text-gray-400 hover:text-white" title="Mark as read">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </button>` : ''}
                    <button onclick="deleteNotification('${notification.id}')" class="text-gray-400 hover:text-red-400" title="Delete">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Mark notification as read
async function markAsRead(notificationId) {
    try {
        const response = await fetch(`/dashboard/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update the notification item
            const item = document.querySelector(`[data-id="${notificationId}"]`);
            if (item) {
                item.classList.add('opacity-75');
                item.classList.remove('border-l-4', 'border-primary-500');
                
                // Remove unread indicator
                const unreadDot = item.querySelector('.bg-primary-500');
                if (unreadDot) unreadDot.remove();
                
                // Remove mark as read button
                const readButton = item.querySelector('button[onclick*="markAsRead"]');
                if (readButton) readButton.remove();
            }
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
    }
}

// Mark all notifications as read
async function markAllAsRead() {
    try {
        const response = await fetch('/dashboard/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            loadNotifications(); // Reload notifications
        }
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
    }
}

// Delete notification
async function deleteNotification(notificationId) {
    if (!confirm('Are you sure you want to delete this notification?')) {
        return;
    }
    
    try {
        const response = await fetch(`/dashboard/notifications/${notificationId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Remove the notification item
            const item = document.querySelector(`[data-id="${notificationId}"]`);
            if (item) {
                item.remove();
            }
        }
    } catch (error) {
        console.error('Error deleting notification:', error);
    }
}

// Clear all notifications
async function clearAllNotifications() {
    if (!confirm('Are you sure you want to clear all notifications? This action cannot be undone.')) {
        return;
    }
    
    try {
        const response = await fetch('/dashboard/notifications/clear', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            loadNotifications(); // Reload notifications
        }
    } catch (error) {
        console.error('Error clearing notifications:', error);
    }
}

// Render pagination
function renderPagination(pagination) {
    const container = document.getElementById('pagination');
    
    if (pagination.last_page <= 1) {
        container.classList.add('hidden');
        return;
    }
    
    container.classList.remove('hidden');
    
    let html = '';
    
    // Previous button
    if (pagination.current_page > 1) {
        html += `<button onclick="loadNotifications(${pagination.current_page - 1})" class="btn-secondary">Previous</button>`;
    }
    
    // Page numbers
    for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.last_page, pagination.current_page + 2); i++) {
        if (i === pagination.current_page) {
            html += `<button class="btn-primary">${i}</button>`;
        } else {
            html += `<button onclick="loadNotifications(${i})" class="btn-secondary">${i}</button>`;
        }
    }
    
    // Next button
    if (pagination.current_page < pagination.last_page) {
        html += `<button onclick="loadNotifications(${pagination.current_page + 1})" class="btn-secondary">Next</button>`;
    }
    
    container.innerHTML = html;
}
</script>
@endsection
