<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\DailyTask;

class DailyTaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tasks = [
            [
                'title' => 'Daily Login',
                'description' => 'Log in to your account',
                'type' => 'login',
                'target_value' => 1,
                'xp_reward' => 10,
                'icon' => '🎯',
                'difficulty_level' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Study Session',
                'description' => 'Complete 30 minutes of course content',
                'type' => 'course_progress',
                'target_value' => 30,
                'xp_reward' => 50,
                'icon' => '📚',
                'difficulty_level' => 2,
                'is_active' => true,
                'requirements' => json_encode(['duration_minutes' => 30]),
            ],
            [
                'title' => 'Community Engagement',
                'description' => 'Send 5 messages in the chat',
                'type' => 'chat_message',
                'target_value' => 5,
                'xp_reward' => 25,
                'icon' => '💬',
                'difficulty_level' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Complete a Lesson',
                'description' => 'Finish any lesson completely',
                'type' => 'lesson_complete',
                'target_value' => 1,
                'xp_reward' => 75,
                'icon' => '✅',
                'difficulty_level' => 2,
                'is_active' => true,
            ],
            [
                'title' => 'Event Participation',
                'description' => 'Attend a live event',
                'type' => 'event_attend',
                'target_value' => 1,
                'xp_reward' => 100,
                'icon' => '🎪',
                'difficulty_level' => 3,
                'is_active' => true,
            ],
            [
                'title' => 'Profile Update',
                'description' => 'Update your profile information',
                'type' => 'profile_update',
                'target_value' => 1,
                'xp_reward' => 20,
                'icon' => '👤',
                'difficulty_level' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Course Explorer',
                'description' => 'Browse and explore 3 different courses',
                'type' => 'course_browse',
                'target_value' => 3,
                'xp_reward' => 30,
                'icon' => '🔍',
                'difficulty_level' => 1,
                'is_active' => true,
            ],
            [
                'title' => 'Knowledge Seeker',
                'description' => 'Complete 3 lessons in a single day',
                'type' => 'lesson_complete',
                'target_value' => 3,
                'xp_reward' => 150,
                'icon' => '🧠',
                'difficulty_level' => 4,
                'is_active' => true,
            ],
            [
                'title' => 'Social Butterfly',
                'description' => 'Send 20 messages in the community chat',
                'type' => 'chat_message',
                'target_value' => 20,
                'xp_reward' => 80,
                'icon' => '🦋',
                'difficulty_level' => 3,
                'is_active' => true,
            ],
            [
                'title' => 'Early Bird',
                'description' => 'Log in before 8 AM',
                'type' => 'early_login',
                'target_value' => 1,
                'xp_reward' => 40,
                'icon' => '🌅',
                'difficulty_level' => 2,
                'is_active' => true,
                'requirements' => json_encode(['before_hour' => 8]),
            ],
        ];

        foreach ($tasks as $task) {
            DailyTask::create($task);
        }
    }
}
