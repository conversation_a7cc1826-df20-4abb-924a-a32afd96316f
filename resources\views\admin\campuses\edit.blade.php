@extends('layouts.admin')

@section('title', 'Edit Campus - ' . $campus->name)
@section('page-title', 'Edit Campus')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Edit Campus</h1>
            <p class="text-gray-400 mt-1">{{ $campus->name }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.campuses.show', $campus) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Campus
            </a>
            <a href="{{ route('admin.campuses.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Campuses
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <form action="{{ route('admin.campuses.update', $campus) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="bg-gray-800 rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h3 class="text-lg font-semibold text-white">Basic Information</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Campus Name *</label>
                                <input type="text" name="name" id="name" class="w-full px-3 py-2 bg-gray-700 border @error('name') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('name', $campus->name) }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="slug" class="block text-sm font-medium text-gray-300 mb-2">Slug *</label>
                                <input type="text" name="slug" id="slug" class="w-full px-3 py-2 bg-gray-700 border @error('slug') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('slug', $campus->slug) }}" required>
                                <p class="mt-1 text-xs text-gray-400">URL-friendly version of the name</p>
                                @error('slug')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                            <textarea name="description" id="description" rows="4" class="w-full px-3 py-2 bg-gray-700 border @error('description') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required>{{ old('description', $campus->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="teaser_description" class="block text-sm font-medium text-gray-300 mb-2">Teaser Description</label>
                            <textarea name="teaser_description" id="teaser_description" rows="2" class="w-full px-3 py-2 bg-gray-700 border @error('teaser_description') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="Short description for previews">{{ old('teaser_description', $campus->teaser_description) }}</textarea>
                            @error('teaser_description')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Visual Settings -->
                <div class="bg-gray-800 rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h3 class="text-lg font-semibold text-white">Visual Settings</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="icon_url" class="block text-sm font-medium text-gray-300 mb-2">Icon URL</label>
                                <input type="url" name="icon_url" id="icon_url" class="w-full px-3 py-2 bg-gray-700 border @error('icon_url') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('icon_url', $campus->icon_url) }}" placeholder="https://example.com/icon.png">
                                @error('icon_url')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="color" class="block text-sm font-medium text-gray-300 mb-2">Theme Color</label>
                                <input type="color" name="color" id="color" class="w-full h-10 bg-gray-700 border @error('color') border-red-500 @else border-gray-600 @enderror rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('color', $campus->color ?? '#3B82F6') }}">
                                @error('color')
                                    <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Sort Order</label>
                            <input type="number" name="sort_order" id="sort_order" class="w-full px-3 py-2 bg-gray-700 border @error('sort_order') border-red-500 @else border-gray-600 @enderror rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('sort_order', $campus->sort_order ?? 0) }}" min="0">
                            <p class="mt-1 text-xs text-gray-400">Lower numbers appear first</p>
                            @error('sort_order')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="bg-gray-800 rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h3 class="text-lg font-semibold text-white">Campus Settings</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="is_active" class="text-sm font-medium text-gray-300">Active Status</label>
                                <p class="text-xs text-gray-400">Whether this campus is visible to users</p>
                            </div>
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', $campus->is_active) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <label for="is_premium" class="text-sm font-medium text-gray-300">Premium Campus</label>
                                <p class="text-xs text-gray-400">Requires premium subscription to access</p>
                            </div>
                            <input type="checkbox" name="is_premium" id="is_premium" value="1" {{ old('is_premium', $campus->is_premium) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Required Plans</label>
                            <div class="space-y-2">
                                @php
                                    $requiredPlans = old('required_plans', $campus->required_plans ?? []);
                                @endphp
                                <label class="flex items-center">
                                    <input type="checkbox" name="required_plans[]" value="prosper" {{ in_array('prosper', $requiredPlans) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-300">Prosper</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="required_plans[]" value="conquer" {{ in_array('conquer', $requiredPlans) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-300">Conquer</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="required_plans[]" value="champions" {{ in_array('champions', $requiredPlans) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <span class="ml-2 text-sm text-gray-300">Champions</span>
                                </label>
                            </div>
                            @error('required_plans')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('admin.campuses.show', $campus) }}" class="btn-secondary">Cancel</a>
                    <button type="submit" class="btn-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Campus
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Campus Stats -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-primary-400">{{ $campus->courses_count ?? 0 }}</div>
                            <div class="text-sm text-gray-400">Courses</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400">{{ $campus->users_count ?? 0 }}</div>
                            <div class="text-sm text-gray-400">Students</div>
                        </div>
                    </div>
                    
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-300">Status</span>
                            @if($campus->is_active)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                            @endif
                        </div>
                        <div class="flex items-center justify-between text-sm mt-2">
                            <span class="text-gray-300">Type</span>
                            @if($campus->is_premium)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Premium</span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Free</span>
                            @endif
                        </div>
                        <div class="flex items-center justify-between text-sm mt-2">
                            <span class="text-gray-300">Created</span>
                            <span class="text-white">{{ $campus->created_at->format('M j, Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="{{ route('admin.campuses.show', $campus) }}" class="w-full btn-secondary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        View Campus Details
                    </a>
                    
                    <a href="{{ route('admin.courses.index', ['campus' => $campus->id]) }}" class="w-full btn-secondary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Manage Courses
                    </a>

                    @if($campus->courses_count == 0)
                    <form action="{{ route('admin.campuses.destroy', $campus) }}" method="POST" onsubmit="return confirm('Are you sure you want to delete this campus?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full btn-outline text-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Campus
                        </button>
                    </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
