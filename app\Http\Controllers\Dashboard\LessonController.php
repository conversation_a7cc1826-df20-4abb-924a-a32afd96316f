<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Lesson;
use App\Models\UserLessonProgress;
use Illuminate\Support\Facades\Auth;

class LessonController extends Controller
{
    /**
     * Show a specific lesson
     */
    public function show(Lesson $lesson)
    {
        $user = Auth::user();
        $course = $lesson->course;
        $campus = $course->campus;

        // Check if user can access this campus
        if (!$campus->isAccessibleBy($user)) {
            return redirect()->route('subscription.plans')
                ->with('error', 'You need to upgrade your plan to access this lesson.');
        }

        // Check if lesson is preview or user has access
        if (!$lesson->is_preview && !$campus->isAccessibleBy($user)) {
            return redirect()->route('subscription.plans')
                ->with('error', 'This lesson requires a subscription to access.');
        }

        // Get or create user progress
        $progress = UserLessonProgress::firstOrCreate(
            [
                'user_id' => $user->id,
                'lesson_id' => $lesson->id,
            ],
            [
                'started_at' => now(),
                'progress_percentage' => 0,
                'watch_time_seconds' => 0,
            ]
        );

        // Get all lessons in this course for navigation
        $courseLessons = $course->lessons()
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Add progress to each lesson for sidebar
        foreach ($courseLessons as $courseLesson) {
            $lessonProgress = UserLessonProgress::where('user_id', $user->id)
                ->where('lesson_id', $courseLesson->id)
                ->first();

            $courseLesson->is_completed = $lessonProgress ? $lessonProgress->is_completed : false;
            $courseLesson->progress_percentage = $lessonProgress ? $lessonProgress->progress_percentage : 0;
        }

        // Find previous and next lessons
        $currentIndex = $courseLessons->search(function($item) use ($lesson) {
            return $item->id === $lesson->id;
        });

        $previousLesson = $currentIndex > 0 ? $courseLessons[$currentIndex - 1] : null;
        $nextLesson = $currentIndex < $courseLessons->count() - 1 ? $courseLessons[$currentIndex + 1] : null;

        return view('dashboard.lessons.show', compact(
            'lesson', 'course', 'campus', 'progress', 'courseLessons',
            'previousLesson', 'nextLesson', 'user'
        ));
    }

    /**
     * Update lesson progress
     */
    public function updateProgress(Request $request, Lesson $lesson)
    {
        $user = Auth::user();

        $request->validate([
            'progress_percentage' => 'required|numeric|min:0|max:100',
            'watch_time_seconds' => 'required|integer|min:0',
        ]);

        $progress = UserLessonProgress::updateOrCreate(
            [
                'user_id' => $user->id,
                'lesson_id' => $lesson->id,
            ],
            [
                'progress_percentage' => $request->progress_percentage,
                'watch_time_seconds' => $request->watch_time_seconds,
                'is_completed' => $request->progress_percentage >= 90, // Mark as completed at 90%
                'completed_at' => $request->progress_percentage >= 90 ? now() : null,
            ]
        );

        // Award XP if lesson is completed for the first time
        if ($progress->is_completed && $progress->wasRecentlyCreated) {
            $user->addXp($lesson->xp_reward);
        }

        return response()->json([
            'success' => true,
            'progress' => $progress,
            'xp_earned' => $progress->is_completed && $progress->wasRecentlyCreated ? $lesson->xp_reward : 0,
        ]);
    }

    /**
     * Mark lesson as completed
     */
    public function complete(Lesson $lesson)
    {
        $user = Auth::user();

        $progress = UserLessonProgress::updateOrCreate(
            [
                'user_id' => $user->id,
                'lesson_id' => $lesson->id,
            ],
            [
                'progress_percentage' => 100,
                'is_completed' => true,
                'completed_at' => now(),
            ]
        );

        // Award XP if not already awarded
        if ($progress->wasRecentlyCreated || !$progress->getOriginal('is_completed')) {
            $user->addXp($lesson->xp_reward);
            $xpEarned = $lesson->xp_reward;
        } else {
            $xpEarned = 0;
        }

        return response()->json([
            'success' => true,
            'xp_earned' => $xpEarned,
            'message' => 'Lesson completed! You earned ' . $xpEarned . ' XP.',
        ]);
    }
}
