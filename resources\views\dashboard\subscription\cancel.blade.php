@extends('layouts.dashboard')

@section('title', 'Subscription Cancelled - The Real World')
@section('page-title', 'Subscription Cancelled')

@section('content')
<div class="p-6">
    <div class="max-w-2xl mx-auto">
        <!-- Cancel Message -->
        <div class="text-center mb-8">
            <div class="w-24 h-24 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold mb-4">Subscription Cancelled</h1>
            <p class="text-xl text-gray-300 mb-6">
                Your subscription process was cancelled. No charges have been made to your account.
            </p>
        </div>

        <!-- What Happened -->
        <div class="card mb-8">
            <h3 class="text-xl font-semibold mb-4">What Happened?</h3>
            <p class="text-gray-400 mb-6">
                You cancelled the subscription process before completing payment. This is completely normal and happens for various reasons:
            </p>
            
            <ul class="space-y-2 text-gray-400 mb-6">
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    You wanted to review the plan details again
                </li>
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    You need to check with your team or budget
                </li>
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    You experienced a technical issue
                </li>
                <li class="flex items-center">
                    <svg class="w-4 h-4 text-gray-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    You want to explore the free content first
                </li>
            </ul>
            
            <p class="text-gray-400">
                No worries! You can always come back and subscribe when you're ready.
            </p>
        </div>

        <!-- Free Content Available -->
        <div class="card mb-8">
            <h3 class="text-xl font-semibold mb-4">Continue with Free Access</h3>
            <p class="text-gray-400 mb-6">
                While you decide, you still have access to our free content and community features:
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center p-3 bg-gray-700 rounded-lg">
                    <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">Basic courses</span>
                </div>
                <div class="flex items-center p-3 bg-gray-700 rounded-lg">
                    <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">Community chat</span>
                </div>
                <div class="flex items-center p-3 bg-gray-700 rounded-lg">
                    <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">Public events</span>
                </div>
                <div class="flex items-center p-3 bg-gray-700 rounded-lg">
                    <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">Daily tasks</span>
                </div>
            </div>
        </div>

        <!-- Special Offer -->
        <div class="card border-primary-600 mb-8">
            <div class="bg-primary-600/10 rounded-lg p-6">
                <h3 class="text-xl font-semibold text-primary-400 mb-4">🎯 Limited Time Offer</h3>
                <p class="text-gray-300 mb-4">
                    Since you showed interest in joining The Real World, we'd like to offer you a special deal:
                </p>
                
                <div class="bg-gray-800 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-semibold text-primary-400">7-Day Free Trial</h4>
                            <p class="text-sm text-gray-400">Try any plan risk-free for 7 days</p>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-primary-400">FREE</div>
                            <div class="text-xs text-gray-400 line-through">$49-199/month</div>
                        </div>
                    </div>
                </div>
                
                <p class="text-sm text-gray-400 mb-4">
                    No commitment, cancel anytime during the trial period.
                </p>
                
                <a href="{{ route('subscription.plans') }}" class="btn-primary">
                    Start Free Trial
                </a>
            </div>
        </div>

        <!-- Need Help -->
        <div class="card">
            <h3 class="text-xl font-semibold mb-4">Need Help Deciding?</h3>
            <p class="text-gray-400 mb-6">
                Our team is here to help you choose the right plan for your goals. Get in touch if you have any questions.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="{{ route('dashboard.chat') }}" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-sm">Live Chat</h4>
                        <p class="text-xs text-gray-400">Ask the community</p>
                    </div>
                </a>
                
                <a href="mailto:<EMAIL>" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-sm">Email Support</h4>
                        <p class="text-xs text-gray-400">Get personal help</p>
                    </div>
                </a>
                
                <a href="tel:+1234567890" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-sm">Phone Support</h4>
                        <p class="text-xs text-gray-400">Speak with us</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex items-center justify-center space-x-4">
            <a href="{{ route('subscription.plans') }}" class="btn-primary">
                View Plans Again
            </a>
            <a href="{{ route('dashboard') }}" class="btn-secondary">
                Continue with Free
            </a>
        </div>
    </div>
</div>
@endsection
