<?php $__env->startSection('title', 'Success Stories - The Real World'); ?>
<?php $__env->startSection('description', 'Read real success stories from our students who are building wealth and achieving financial freedom with The Real World education.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold font-display mb-6">
            Real Students, <span class="text-gradient">Real Results</span>
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Don't just take our word for it. See what our students are achieving with The Real World education. 
            These are real people making real money with the skills they learned.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('register')); ?>" class="btn-primary text-lg px-8 py-4">
                Start Your Success Story
            </a>
            <a href="<?php echo e(route('campuses')); ?>" class="btn-secondary text-lg px-8 py-4">
                Explore Campuses
            </a>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section class="py-16 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-3xl font-bold text-primary-400 mb-2">$2.8M+</div>
                <div class="text-gray-400">Total Student Earnings</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-green-400 mb-2">1,247</div>
                <div class="text-gray-400">Success Stories</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-blue-400 mb-2">89%</div>
                <div class="text-gray-400">See Results in 30 Days</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-yellow-400 mb-2">24/7</div>
                <div class="text-gray-400">Community Support</div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Grid -->
<section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="card hover:transform hover:scale-105 transition-all duration-300">
                <!-- User Info -->
                <div class="flex items-center mb-6">
                    <?php if($testimonial->photo_url): ?>
                        <img src="<?php echo e($testimonial->photo_url); ?>" alt="<?php echo e($testimonial->name); ?>" class="w-16 h-16 rounded-full mr-4 object-cover">
                    <?php else: ?>
                        <div class="w-16 h-16 rounded-full bg-gradient-to-br from-primary-500 to-primary-700 flex items-center justify-center mr-4">
                            <span class="text-white font-bold text-xl"><?php echo e(substr($testimonial->name, 0, 1)); ?></span>
                        </div>
                    <?php endif; ?>
                    <div>
                        <h3 class="font-bold text-lg"><?php echo e($testimonial->name); ?></h3>
                        <p class="text-gray-400 text-sm"><?php echo e($testimonial->location); ?></p>
                        <?php if($testimonial->business_type): ?>
                            <span class="inline-block bg-primary-600 text-white text-xs px-2 py-1 rounded-full mt-1">
                                <?php echo e($testimonial->business_type); ?>

                            </span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Income Display -->
                <div class="text-center mb-6">
                    <div class="text-3xl font-bold text-green-400 mb-2"><?php echo e($testimonial->income_display); ?></div>
                    <div class="text-sm text-gray-400">Monthly Income</div>
                </div>

                <!-- Quote -->
                <blockquote class="text-gray-300 italic leading-relaxed mb-6">
                    "<?php echo e($testimonial->quote); ?>"
                </blockquote>

                <!-- Verification Badge -->
                <div class="flex items-center justify-center">
                    <div class="flex items-center text-sm text-green-400">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Verified Student
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">No Testimonials Yet</h3>
                <p class="text-gray-400">Be the first to share your success story!</p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if($testimonials->hasPages()): ?>
        <div class="mt-12">
            <?php echo e($testimonials->links()); ?>

        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Submit Testimonial Section -->
<section class="py-20 bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-6">Share Your Success Story</h2>
        <p class="text-xl text-gray-400 mb-8">
            Have you achieved success with The Real World? We'd love to hear about your journey and share it with others.
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">📈</span>
                </div>
                <h3 class="font-semibold mb-2">Share Your Results</h3>
                <p class="text-gray-400 text-sm">Tell us about your income growth and business success.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">💡</span>
                </div>
                <h3 class="font-semibold mb-2">Inspire Others</h3>
                <p class="text-gray-400 text-sm">Your story could motivate someone to start their journey.</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span class="text-2xl">🏆</span>
                </div>
                <h3 class="font-semibold mb-2">Get Featured</h3>
                <p class="text-gray-400 text-sm">Outstanding stories get featured on our homepage.</p>
            </div>
        </div>

        <?php if(auth()->guard()->check()): ?>
            <a href="<?php echo e(route('dashboard.profile.show')); ?>" class="btn-primary text-lg px-8 py-4">
                Submit Your Story
            </a>
        <?php else: ?>
            <div class="space-y-4">
                <p class="text-gray-400">Join The Real World to submit your success story</p>
                <a href="<?php echo e(route('register')); ?>" class="btn-primary text-lg px-8 py-4">
                    Join Now
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-primary-600 to-primary-800">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold font-display mb-6 text-white">
            Ready to Write Your Success Story?
        </h2>
        <p class="text-xl text-primary-100 mb-8">
            Join thousands of students who are already building wealth with The Real World. 
            Your success story could be next.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('register')); ?>" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-colors text-lg">
                Start Your Journey
            </a>
            <a href="<?php echo e(route('pricing')); ?>" class="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold py-4 px-8 rounded-lg transition-colors text-lg">
                View Pricing
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/home/<USER>/ ?>