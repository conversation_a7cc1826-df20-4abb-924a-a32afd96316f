@extends('layouts.admin')

@section('title', 'Edit Event - ' . $event->title)
@section('page-title', 'Edit Event')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Edit Event</h1>
            <p class="text-gray-400 mt-1">Update event information and settings</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.events.show', $event) }}" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Event
            </a>
            <a href="{{ route('admin.events.index') }}" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Events
            </a>
        </div>
    </div>

    <!-- Form -->
    <div class="bg-gray-800 rounded-lg p-6">
        <form method="POST" action="{{ route('admin.events.update', $event) }}" enctype="multipart/form-data">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Event Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Event Title</label>
                        <input type="text" 
                               name="title" 
                               id="title" 
                               value="{{ old('title', $event->title) }}"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                               placeholder="Enter event title"
                               required>
                        @error('title')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Category -->
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Event Category</label>
                        <select name="category" 
                                id="category" 
                                class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                required>
                            <option value="">Select category</option>
                            <option value="workshop" {{ old('category', $event->category) == 'workshop' ? 'selected' : '' }}>Workshop</option>
                            <option value="webinar" {{ old('category', $event->category) == 'webinar' ? 'selected' : '' }}>Webinar</option>
                            <option value="networking" {{ old('category', $event->category) == 'networking' ? 'selected' : '' }}>Networking</option>
                            <option value="masterclass" {{ old('category', $event->category) == 'masterclass' ? 'selected' : '' }}>Masterclass</option>
                        </select>
                        @error('category')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Date and Time -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="start_time" class="block text-sm font-medium text-gray-300 mb-2">Start Date & Time</label>
                            <input type="datetime-local" 
                                   name="start_time" 
                                   id="start_time" 
                                   value="{{ old('start_time', $event->start_time->format('Y-m-d\TH:i')) }}"
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   required>
                            @error('start_time')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="end_time" class="block text-sm font-medium text-gray-300 mb-2">End Date & Time</label>
                            <input type="datetime-local" 
                                   name="end_time" 
                                   id="end_time" 
                                   value="{{ old('end_time', $event->end_time->format('Y-m-d\TH:i')) }}"
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   required>
                            @error('end_time')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Max Attendees -->
                    <div>
                        <label for="max_attendees" class="block text-sm font-medium text-gray-300 mb-2">Maximum Attendees</label>
                        <input type="number" 
                               name="max_attendees" 
                               id="max_attendees" 
                               value="{{ old('max_attendees', $event->max_attendees) }}"
                               min="1"
                               class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                               placeholder="Leave empty for unlimited">
                        @error('max_attendees')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-400">Leave empty for unlimited attendees</p>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Event Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Event Description</label>
                        <textarea name="description" 
                                  id="description" 
                                  rows="6"
                                  class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                  placeholder="Describe what attendees will learn and experience..."
                                  required>{{ old('description', $event->description) }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Event Options -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-white">Event Options</h3>
                        
                        <!-- Premium Event -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_premium" 
                                   id="is_premium" 
                                   value="1"
                                   {{ old('is_premium', $event->is_premium) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_premium" class="ml-2 text-sm text-gray-300">
                                Premium Event (requires subscription)
                            </label>
                        </div>

                        <!-- Published -->
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   name="is_published" 
                                   id="is_published" 
                                   value="1"
                                   {{ old('is_published', $event->is_published) ? 'checked' : '' }}
                                   class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_published" class="ml-2 text-sm text-gray-300">
                                Published (visible to users)
                            </label>
                        </div>
                    </div>

                    <!-- Event Links -->
                    <div class="space-y-4">
                        <h3 class="text-lg font-medium text-white">Event Links</h3>
                        
                        <div>
                            <label for="meeting_url" class="block text-sm font-medium text-gray-300 mb-2">Meeting URL</label>
                            <input type="url" 
                                   name="meeting_url" 
                                   id="meeting_url" 
                                   value="{{ old('meeting_url', $event->meeting_url) }}"
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   placeholder="https://zoom.us/j/...">
                            @error('meeting_url')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="recording_url" class="block text-sm font-medium text-gray-300 mb-2">Recording URL (Optional)</label>
                            <input type="url" 
                                   name="recording_url" 
                                   id="recording_url" 
                                   value="{{ old('recording_url', $event->recording_url) }}"
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                   placeholder="https://...">
                            @error('recording_url')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-700">
                <a href="{{ route('admin.events.show', $event) }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Event
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Validate end time is after start time
document.getElementById('end_time').addEventListener('change', function() {
    const startTime = new Date(document.getElementById('start_time').value);
    const endTime = new Date(this.value);
    
    if (endTime <= startTime) {
        alert('End time must be after start time');
        this.value = '';
    }
});

// Auto-update end time when start time changes (if end time is empty)
document.getElementById('start_time').addEventListener('change', function() {
    const endTimeInput = document.getElementById('end_time');
    if (!endTimeInput.value) {
        const startTime = new Date(this.value);
        const endTime = new Date(startTime.getTime() + (2 * 60 * 60 * 1000)); // Add 2 hours
        endTimeInput.value = endTime.toISOString().slice(0, 16);
    }
});
</script>
@endsection
