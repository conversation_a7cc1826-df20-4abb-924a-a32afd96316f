<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ChatMessage;
use App\Models\ChatRoom;
use App\Models\User;
use Carbon\Carbon;

class ChatMessageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get chat rooms and users
        $generalRoom = ChatRoom::where('name', 'General Chat')->first();
        $successRoom = ChatRoom::where('name', 'Success Stories')->first();
        $helpRoom = ChatRoom::where('name', 'Help & Support')->first();

        $users = User::take(5)->get();

        if (!$generalRoom || $users->count() === 0) {
            return;
        }

        // Sample messages for General Chat
        $generalMessages = [
            "Welcome to The Real World! Excited to start this journey 🚀",
            "Just finished my first lesson on e-commerce. Mind blown! 🤯",
            "Anyone else working on their dropshipping store today?",
            "The community here is incredible. So much value!",
            "Just hit level 5! The XP system is so motivating 💪",
            "Morning everyone! Ready to make some money today? 💰",
            "The courses here are pure gold. <PERSON> knows what he's talking about",
            "Who's joining the live event tonight? Can't wait!",
            "Just made my first $100 online thanks to the strategies here! 🎉",
            "The Real World changed my life. No cap.",
        ];

        // Sample messages for Success Stories
        $successMessages = [
            "🎉 Just hit $10K/month with my e-commerce store! Started 3 months ago",
            "Landed my first high-paying copywriting client today! $5K project 💰",
            "My crypto portfolio is up 200% this month using the strategies from here",
            "Quit my 9-5 job today! My online business is now making more than my salary",
            "Just bought my dream car with profits from dropshipping! 🚗",
            "From broke college student to $50K/month in 6 months. This community is everything!",
        ];

        // Sample messages for Help & Support
        $helpMessages = [
            "Having trouble setting up my Shopify store. Any tips?",
            "What's the best way to find winning products for dropshipping?",
            "Can someone help me with Facebook ads? My ROAS is terrible",
            "How do I increase my conversion rate? Currently at 1.2%",
            "Need help with copywriting. My emails aren't converting",
            "What tools do you recommend for crypto trading?",
        ];

        // Create messages for General Chat
        if ($generalRoom) {
            foreach ($generalMessages as $index => $messageText) {
                ChatMessage::create([
                    'chat_room_id' => $generalRoom->id,
                    'user_id' => $users->random()->id,
                    'message' => $messageText,
                    'type' => 'text',
                    'created_at' => Carbon::now()->subHours(rand(1, 48))->subMinutes(rand(0, 59)),
                ]);
            }
        }

        // Create messages for Success Stories
        if ($successRoom) {
            foreach ($successMessages as $messageText) {
                ChatMessage::create([
                    'chat_room_id' => $successRoom->id,
                    'user_id' => $users->random()->id,
                    'message' => $messageText,
                    'type' => 'text',
                    'created_at' => Carbon::now()->subHours(rand(1, 72))->subMinutes(rand(0, 59)),
                ]);
            }
        }

        // Create messages for Help & Support
        if ($helpRoom) {
            foreach ($helpMessages as $messageText) {
                ChatMessage::create([
                    'chat_room_id' => $helpRoom->id,
                    'user_id' => $users->random()->id,
                    'message' => $messageText,
                    'type' => 'text',
                    'created_at' => Carbon::now()->subHours(rand(1, 24))->subMinutes(rand(0, 59)),
                ]);
            }
        }
    }
}
