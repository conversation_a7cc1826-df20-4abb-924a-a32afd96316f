<?php $__env->startSection('title', 'System Settings'); ?>
<?php $__env->startSection('page-title', 'Settings'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-white">System Settings</h1>
        <p class="text-gray-400 mt-1">Configure your application settings</p>
    </div>

    <!-- Settings Tabs -->
    <div class="bg-gray-800 rounded-lg overflow-hidden">
        <div class="border-b border-gray-700">
            <nav class="flex space-x-8 px-6" x-data="{ activeTab: 'general' }">
                <button @click="activeTab = 'general'" 
                        :class="activeTab === 'general' ? 'border-primary-500 text-primary-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                    General
                </button>
                <button @click="activeTab = 'email'" 
                        :class="activeTab === 'email' ? 'border-primary-500 text-primary-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                    Email
                </button>
                <button @click="activeTab = 'security'" 
                        :class="activeTab === 'security' ? 'border-primary-500 text-primary-400' : 'border-transparent text-gray-400 hover:text-gray-300'"
                        class="py-4 px-1 border-b-2 font-medium text-sm transition-colors">
                    Security
                </button>
            </nav>
        </div>

        <!-- General Settings -->
        <div x-show="activeTab === 'general'" class="p-6">
            <form method="POST" action="<?php echo e(route('admin.settings.general')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-white mb-4">General Configuration</h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- App Name -->
                            <div>
                                <label for="app_name" class="block text-sm font-medium text-gray-300 mb-2">Application Name</label>
                                <input type="text" 
                                       name="app_name" 
                                       id="app_name" 
                                       value="<?php echo e($settings['general']['app_name']); ?>"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <!-- App URL -->
                            <div>
                                <label for="app_url" class="block text-sm font-medium text-gray-300 mb-2">Application URL</label>
                                <input type="url" 
                                       name="app_url" 
                                       id="app_url" 
                                       value="<?php echo e($settings['general']['app_url']); ?>"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <!-- Timezone -->
                            <div>
                                <label for="timezone" class="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                                <select name="timezone" 
                                        id="timezone" 
                                        class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <option value="UTC" <?php echo e($settings['general']['timezone'] === 'UTC' ? 'selected' : ''); ?>>UTC</option>
                                    <option value="America/New_York" <?php echo e($settings['general']['timezone'] === 'America/New_York' ? 'selected' : ''); ?>>Eastern Time</option>
                                    <option value="America/Chicago" <?php echo e($settings['general']['timezone'] === 'America/Chicago' ? 'selected' : ''); ?>>Central Time</option>
                                    <option value="America/Denver" <?php echo e($settings['general']['timezone'] === 'America/Denver' ? 'selected' : ''); ?>>Mountain Time</option>
                                    <option value="America/Los_Angeles" <?php echo e($settings['general']['timezone'] === 'America/Los_Angeles' ? 'selected' : ''); ?>>Pacific Time</option>
                                    <option value="Europe/London" <?php echo e($settings['general']['timezone'] === 'Europe/London' ? 'selected' : ''); ?>>London</option>
                                    <option value="Europe/Paris" <?php echo e($settings['general']['timezone'] === 'Europe/Paris' ? 'selected' : ''); ?>>Paris</option>
                                    <option value="Asia/Tokyo" <?php echo e($settings['general']['timezone'] === 'Asia/Tokyo' ? 'selected' : ''); ?>>Tokyo</option>
                                </select>
                            </div>

                            <!-- Maintenance Mode -->
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Maintenance Mode</label>
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           name="maintenance_mode" 
                                           id="maintenance_mode" 
                                           value="1"
                                           <?php echo e($settings['general']['maintenance_mode'] ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <label for="maintenance_mode" class="ml-2 text-sm text-gray-300">
                                        Enable maintenance mode
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-400">When enabled, only administrators can access the site</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary">
                            Save General Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Email Settings -->
        <div x-show="activeTab === 'email'" class="p-6">
            <form method="POST" action="<?php echo e(route('admin.settings.email')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-white mb-4">Email Configuration</h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Mail Driver -->
                            <div>
                                <label for="mail_driver" class="block text-sm font-medium text-gray-300 mb-2">Mail Driver</label>
                                <select name="mail_driver" 
                                        id="mail_driver" 
                                        class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <option value="smtp" <?php echo e($settings['email']['mail_driver'] === 'smtp' ? 'selected' : ''); ?>>SMTP</option>
                                    <option value="mailgun" <?php echo e($settings['email']['mail_driver'] === 'mailgun' ? 'selected' : ''); ?>>Mailgun</option>
                                    <option value="ses" <?php echo e($settings['email']['mail_driver'] === 'ses' ? 'selected' : ''); ?>>Amazon SES</option>
                                    <option value="sendmail" <?php echo e($settings['email']['mail_driver'] === 'sendmail' ? 'selected' : ''); ?>>Sendmail</option>
                                </select>
                            </div>

                            <!-- Mail Host -->
                            <div>
                                <label for="mail_host" class="block text-sm font-medium text-gray-300 mb-2">Mail Host</label>
                                <input type="text" 
                                       name="mail_host" 
                                       id="mail_host" 
                                       value="<?php echo e($settings['email']['mail_host']); ?>"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <!-- Mail Port -->
                            <div>
                                <label for="mail_port" class="block text-sm font-medium text-gray-300 mb-2">Mail Port</label>
                                <input type="number" 
                                       name="mail_port" 
                                       id="mail_port" 
                                       value="<?php echo e($settings['email']['mail_port']); ?>"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <!-- From Address -->
                            <div>
                                <label for="mail_from_address" class="block text-sm font-medium text-gray-300 mb-2">From Address</label>
                                <input type="email" 
                                       name="mail_from_address" 
                                       id="mail_from_address" 
                                       value="<?php echo e($settings['email']['mail_from_address']); ?>"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>

                            <!-- From Name -->
                            <div class="lg:col-span-2">
                                <label for="mail_from_name" class="block text-sm font-medium text-gray-300 mb-2">From Name</label>
                                <input type="text" 
                                       name="mail_from_name" 
                                       id="mail_from_name" 
                                       value="<?php echo e($settings['email']['mail_from_name']); ?>"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary">
                            Save Email Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Security Settings -->
        <div x-show="activeTab === 'security'" class="p-6">
            <form method="POST" action="<?php echo e(route('admin.settings.security')); ?>">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>

                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-white mb-4">Security Configuration</h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Session Lifetime -->
                            <div>
                                <label for="session_lifetime" class="block text-sm font-medium text-gray-300 mb-2">Session Lifetime (minutes)</label>
                                <input type="number" 
                                       name="session_lifetime" 
                                       id="session_lifetime" 
                                       value="<?php echo e($settings['security']['session_lifetime']); ?>"
                                       min="1"
                                       max="10080"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <p class="mt-1 text-xs text-gray-400">How long users stay logged in (max 1 week)</p>
                            </div>

                            <!-- Password Timeout -->
                            <div>
                                <label for="password_timeout" class="block text-sm font-medium text-gray-300 mb-2">Password Timeout (minutes)</label>
                                <input type="number" 
                                       name="password_timeout" 
                                       id="password_timeout" 
                                       value="<?php echo e($settings['security']['password_timeout']); ?>"
                                       min="1"
                                       max="1440"
                                       class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <p class="mt-1 text-xs text-gray-400">How long before password re-confirmation (max 24 hours)</p>
                            </div>

                            <!-- Two Factor Authentication -->
                            <div class="lg:col-span-2">
                                <label class="block text-sm font-medium text-gray-300 mb-2">Two-Factor Authentication</label>
                                <div class="flex items-center">
                                    <input type="checkbox" 
                                           name="two_factor_enabled" 
                                           id="two_factor_enabled" 
                                           value="1"
                                           <?php echo e($settings['security']['two_factor_enabled'] ? 'checked' : ''); ?>

                                           class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                                    <label for="two_factor_enabled" class="ml-2 text-sm text-gray-300">
                                        Enable two-factor authentication for all users
                                    </label>
                                </div>
                                <p class="mt-1 text-xs text-gray-400">Requires users to use 2FA for enhanced security</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="btn-primary">
                            Save Security Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize Alpine.js data
document.addEventListener('alpine:init', () => {
    Alpine.data('settings', () => ({
        activeTab: 'general'
    }));
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/settings/index.blade.php ENDPATH**/ ?>