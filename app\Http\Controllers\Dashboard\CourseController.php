<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\UserLessonProgress;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request)
    {
        $query = Course::with(['campus', 'enrollments'])
            ->where('is_published', true);

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('description', 'like', "%{$searchTerm}%");
            });
        }

        // Campus filter
        if ($request->filled('campus')) {
            $query->where('campus_id', $request->get('campus'));
        }

        // Difficulty filter
        if ($request->filled('difficulty')) {
            $query->where('difficulty_level', $request->get('difficulty'));
        }

        // Sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'popular':
                $query->withCount('enrollments')->orderBy('enrollments_count', 'desc');
                break;
            case 'rating':
                $query->orderBy('rating', 'desc');
                break;
            case 'duration':
                $query->orderBy('estimated_duration', 'asc');
                break;
            default: // newest
                $query->orderBy('created_at', 'desc');
                break;
        }

        $courses = $query->paginate(12);
        $campuses = \App\Models\Campus::all();

        return view('dashboard.courses.index', compact('courses', 'campuses'));
    }

    /**
     * Show a specific course with its lessons
     */
    public function show(Course $course)
    {
        $user = Auth::user();
        $campus = $course->campus;

        // Check if user can access this campus
        if (!$campus->isAccessibleBy($user)) {
            return redirect()->route('subscription.plans')
                ->with('error', 'You need to upgrade your plan to access this course.');
        }

        // Get lessons with user progress
        $lessons = $course->lessons()
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Add progress information to each lesson
        foreach ($lessons as $lesson) {
            $progress = UserLessonProgress::where('user_id', $user->id)
                ->where('lesson_id', $lesson->id)
                ->first();

            $lesson->user_progress = $progress;
            $lesson->is_completed = $progress ? $progress->is_completed : false;
            $lesson->progress_percentage = $progress ? $progress->progress_percentage : 0;
            $lesson->watch_time = $progress ? $progress->watch_time_seconds : 0;
        }

        // Calculate course progress
        $totalLessons = $lessons->count();
        $completedLessons = $lessons->where('is_completed', true)->count();
        $courseProgress = $totalLessons > 0 ? ($completedLessons / $totalLessons) * 100 : 0;

        // Get next lesson to watch
        $nextLesson = $lessons->where('is_completed', false)->first();

        return view('dashboard.courses.show', compact('course', 'campus', 'lessons', 'courseProgress', 'completedLessons', 'totalLessons', 'nextLesson', 'user'));
    }

    /**
     * Mark course as started
     */
    public function start(Course $course)
    {
        $user = Auth::user();
        $campus = $course->campus;

        // Check access
        if (!$campus->isAccessibleBy($user)) {
            return response()->json(['error' => 'Access denied'], 403);
        }

        // Get first lesson
        $firstLesson = $course->lessons()
            ->where('is_active', true)
            ->orderBy('sort_order')
            ->first();

        if (!$firstLesson) {
            return response()->json(['error' => 'No lessons available'], 404);
        }

        // Create or update progress for first lesson
        UserLessonProgress::updateOrCreate(
            [
                'user_id' => $user->id,
                'lesson_id' => $firstLesson->id,
            ],
            [
                'started_at' => now(),
            ]
        );

        return redirect()->route('dashboard.lessons.show', $firstLesson);
    }
}
