# The Real World - Deployment Guide

This guide covers deploying The Real World Laravel application to production environments.

## 🚀 Production Requirements

### Server Requirements
- **PHP**: 8.1 or higher with extensions:
  - BCMath
  - Ctype
  - Fileinfo
  - JSON
  - Mbstring
  - OpenSSL
  - PDO
  - Tokenizer
  - XML
  - GD or Imagick
  - Redis
- **Web Server**: Nginx or Apache
- **Database**: MySQL 8.0+ or MariaDB 10.3+
- **Cache**: Redis 6.0+
- **SSL Certificate**: Required for production
- **Memory**: Minimum 2GB RAM
- **Storage**: Minimum 20GB SSD

### Recommended Server Specifications
- **CPU**: 2+ cores
- **RAM**: 4GB+
- **Storage**: 50GB+ SSD
- **Bandwidth**: 100Mbps+

## 🔧 Server Setup

### 1. Ubuntu 22.04 LTS Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.1 and extensions
sudo apt install -y php8.1-fpm php8.1-mysql php8.1-redis php8.1-xml php8.1-gd php8.1-mbstring php8.1-curl php8.1-zip php8.1-bcmath php8.1-intl

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install MySQL
sudo apt install -y mysql-server
sudo mysql_secure_installation

# Install Redis
sudo apt install -y redis-server
sudo systemctl enable redis-server

# Install Nginx
sudo apt install -y nginx
sudo systemctl enable nginx

# Install Supervisor (for queue workers)
sudo apt install -y supervisor
```

### 2. MySQL Configuration

```sql
-- Create database and user
CREATE DATABASE real_world_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'realworld'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON real_world_production.* TO 'realworld'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Nginx Configuration

Create `/etc/nginx/sites-available/realworld`:

```nginx
server {
    listen 80;
    server_name app.jointherealworld.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name app.jointherealworld.com;
    root /var/www/realworld/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Increase upload size
    client_max_body_size 100M;
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/realworld /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 📦 Application Deployment

### 1. Clone and Setup Application

```bash
# Create application directory
sudo mkdir -p /var/www/realworld
sudo chown $USER:$USER /var/www/realworld

# Clone repository
cd /var/www/realworld
git clone <repository-url> .

# Install dependencies
composer install --no-dev --optimize-autoloader
npm ci --production

# Set permissions
sudo chown -R www-data:www-data /var/www/realworld
sudo chmod -R 755 /var/www/realworld
sudo chmod -R 775 /var/www/realworld/storage
sudo chmod -R 775 /var/www/realworld/bootstrap/cache
```

### 2. Environment Configuration

Create production `.env` file:

```env
APP_NAME="The Real World"
APP_ENV=production
APP_KEY=base64:your-generated-key
APP_DEBUG=false
APP_URL=https://app.jointherealworld.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=real_world_production
DB_USERNAME=realworld
DB_PASSWORD=secure_password

BROADCAST_DRIVER=pusher
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-smtp-username
MAIL_PASSWORD=your-smtp-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Stripe Configuration
STRIPE_KEY=pk_live_your_publishable_key
STRIPE_SECRET=sk_live_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Pusher Configuration
PUSHER_APP_ID=your_pusher_app_id
PUSHER_APP_KEY=your_pusher_key
PUSHER_APP_SECRET=your_pusher_secret
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Security Settings
SECURITY_AUDIT_ENABLED=true
CHECK_PASSWORD_BREACHES=true
ENABLE_IP_BLOCKING=true
TWO_FACTOR_ENABLED=true

# Performance Settings
CACHE_TTL=3600
PERFORMANCE_MONITORING=true
```

### 3. Application Optimization

```bash
# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Seed database (only on first deployment)
php artisan db:seed --force

# Create storage link
php artisan storage:link

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Optimize autoloader
composer dump-autoload --optimize

# Build assets
npm run build
```

### 4. Queue Worker Setup

Create supervisor configuration `/etc/supervisor/conf.d/realworld-worker.conf`:

```ini
[program:realworld-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/realworld/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/www/realworld/storage/logs/worker.log
stopwaitsecs=3600
```

Start supervisor:
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start realworld-worker:*
```

### 5. Cron Jobs

Add to crontab (`sudo crontab -e`):

```bash
# Laravel Scheduler
* * * * * cd /var/www/realworld && php artisan schedule:run >> /dev/null 2>&1

# Log rotation
0 0 * * * cd /var/www/realworld && find storage/logs -name "*.log" -mtime +30 -delete

# Cache warming
0 */6 * * * cd /var/www/realworld && php artisan cache:warm
```

## 🔒 Security Hardening

### 1. File Permissions

```bash
# Set proper ownership
sudo chown -R www-data:www-data /var/www/realworld

# Set directory permissions
sudo find /var/www/realworld -type d -exec chmod 755 {} \;

# Set file permissions
sudo find /var/www/realworld -type f -exec chmod 644 {} \;

# Set executable permissions
sudo chmod +x /var/www/realworld/artisan

# Secure sensitive directories
sudo chmod -R 775 /var/www/realworld/storage
sudo chmod -R 775 /var/www/realworld/bootstrap/cache
```

### 2. Firewall Configuration

```bash
# Install UFW
sudo apt install -y ufw

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH
sudo ufw allow ssh

# Allow HTTP and HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Enable firewall
sudo ufw enable
```

### 3. Fail2Ban Setup

```bash
# Install Fail2Ban
sudo apt install -y fail2ban

# Create jail configuration
sudo tee /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
EOF

# Restart Fail2Ban
sudo systemctl restart fail2ban
```

## 📊 Monitoring Setup

### 1. Log Management

Configure log rotation in `/etc/logrotate.d/realworld`:

```
/var/www/realworld/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        sudo systemctl reload php8.1-fpm
    endscript
}
```

### 2. Health Checks

Create health check script `/var/www/realworld/health-check.sh`:

```bash
#!/bin/bash

# Check application health
curl -f http://localhost/health || exit 1

# Check database connection
php /var/www/realworld/artisan tinker --execute="DB::connection()->getPdo();" || exit 1

# Check Redis connection
redis-cli ping || exit 1

# Check queue workers
supervisorctl status realworld-worker:* | grep RUNNING || exit 1

echo "All health checks passed"
```

### 3. Performance Monitoring

Install and configure monitoring tools:

```bash
# Install htop for system monitoring
sudo apt install -y htop

# Install MySQL monitoring
sudo apt install -y mytop

# Configure application monitoring
php artisan make:command MonitorPerformance
```

## 🔄 Deployment Automation

### 1. Deployment Script

Create `deploy.sh`:

```bash
#!/bin/bash

set -e

echo "Starting deployment..."

# Pull latest code
git pull origin main

# Install/update dependencies
composer install --no-dev --optimize-autoloader
npm ci --production

# Run migrations
php artisan migrate --force

# Clear and rebuild caches
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

php artisan config:cache
php artisan route:cache
php artisan view:cache

# Build assets
npm run build

# Restart services
sudo supervisorctl restart realworld-worker:*
sudo systemctl reload php8.1-fpm
sudo systemctl reload nginx

echo "Deployment completed successfully!"
```

### 2. Zero-Downtime Deployment

For zero-downtime deployments, use a blue-green deployment strategy:

```bash
#!/bin/bash

# Create new release directory
RELEASE_DIR="/var/www/releases/$(date +%Y%m%d%H%M%S)"
mkdir -p $RELEASE_DIR

# Deploy to release directory
cd $RELEASE_DIR
git clone <repository-url> .
composer install --no-dev --optimize-autoloader
npm ci --production
npm run build

# Update symlink atomically
ln -nfs $RELEASE_DIR /var/www/realworld-new
mv /var/www/realworld-new /var/www/realworld

# Cleanup old releases (keep last 5)
ls -t /var/www/releases | tail -n +6 | xargs -r rm -rf
```

## 🚨 Backup Strategy

### 1. Database Backup

Create backup script `/usr/local/bin/backup-db.sh`:

```bash
#!/bin/bash

BACKUP_DIR="/var/backups/realworld"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="real_world_production"

mkdir -p $BACKUP_DIR

# Create database backup
mysqldump --single-transaction --routines --triggers $DB_NAME | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# Remove backups older than 30 days
find $BACKUP_DIR -name "db_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_$DATE.sql.gz"
```

### 2. File Backup

```bash
#!/bin/bash

BACKUP_DIR="/var/backups/realworld"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup storage directory
tar -czf $BACKUP_DIR/storage_$DATE.tar.gz -C /var/www/realworld storage

# Backup uploads
tar -czf $BACKUP_DIR/uploads_$DATE.tar.gz -C /var/www/realworld/public uploads

# Remove old backups
find $BACKUP_DIR -name "storage_*.tar.gz" -mtime +7 -delete
find $BACKUP_DIR -name "uploads_*.tar.gz" -mtime +7 -delete
```

### 3. Automated Backups

Add to crontab:

```bash
# Database backup (daily at 2 AM)
0 2 * * * /usr/local/bin/backup-db.sh

# File backup (daily at 3 AM)
0 3 * * * /usr/local/bin/backup-files.sh
```

## 🔍 Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data /var/www/realworld
   sudo chmod -R 775 storage bootstrap/cache
   ```

2. **Queue Workers Not Processing**
   ```bash
   sudo supervisorctl restart realworld-worker:*
   php artisan queue:restart
   ```

3. **High Memory Usage**
   ```bash
   php artisan config:cache
   php artisan route:cache
   php artisan view:cache
   ```

4. **Database Connection Issues**
   - Check MySQL service status
   - Verify database credentials
   - Check firewall rules

### Log Locations

- **Application Logs**: `/var/www/realworld/storage/logs/`
- **Nginx Logs**: `/var/log/nginx/`
- **PHP-FPM Logs**: `/var/log/php8.1-fpm.log`
- **MySQL Logs**: `/var/log/mysql/`
- **Supervisor Logs**: `/var/log/supervisor/`

## 📈 Performance Optimization

### 1. PHP-FPM Tuning

Edit `/etc/php/8.1/fpm/pool.d/www.conf`:

```ini
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

### 2. MySQL Optimization

Add to `/etc/mysql/mysql.conf.d/mysqld.cnf`:

```ini
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
query_cache_type = 1
query_cache_size = 64M
max_connections = 200
```

### 3. Redis Configuration

Edit `/etc/redis/redis.conf`:

```
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

This deployment guide provides a comprehensive setup for production deployment of The Real World application with security, monitoring, and performance optimizations.
