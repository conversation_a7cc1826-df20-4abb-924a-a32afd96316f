# Optional Docker Compose file for Mailpit
# Run with: docker-compose -f docker-compose.mailpit.yml up -d
version: '3.8'

services:
  mailpit:
    image: axllent/mailpit:latest
    container_name: mailpit
    ports:
      - "8025:8025"  # Web interface
      - "1025:1025"  # SMTP server
    environment:
      MP_SMTP_AUTH_ACCEPT_ANY: 1
      MP_SMTP_AUTH_ALLOW_INSECURE: 1
    networks:
      - mailpit-network

networks:
  mailpit-network:
    driver: bridge
