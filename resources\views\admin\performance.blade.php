@extends('layouts.admin')

@section('title', 'Performance')

@section('content')
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gradient">Performance Dashboard</h1>
    <p class="text-gray-400 mt-2">Monitor system performance and optimization metrics</p>
</div>

<!-- Performance Metrics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- Cache Performance -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400">Cache Hit Rate</p>
                <p class="text-2xl font-bold text-white">{{ $performance['cache_stats']['hit_rate'] }}%</p>
                <p class="text-xs text-gray-500">{{ number_format($performance['cache_stats']['total_keys']) }} keys</p>
            </div>
        </div>
    </div>

    <!-- Memory Usage -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400">Memory Usage</p>
                <p class="text-2xl font-bold text-white">{{ $performance['memory_usage']['current'] }}%</p>
                <p class="text-xs text-gray-500">Peak: {{ $performance['memory_usage']['peak'] }}%</p>
            </div>
        </div>
    </div>

    <!-- Response Time -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-600 rounded-lg flex items-center justify-center mr-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-400">Avg Response Time</p>
                <p class="text-2xl font-bold text-white">{{ $performance['response_times']['average'] }}ms</p>
                <p class="text-xs text-gray-500">Max: {{ $performance['response_times']['max'] }}ms</p>
            </div>
        </div>
    </div>
</div>

<!-- Performance Charts -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Response Time Chart -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4">Response Time Trends</h3>
        <div class="h-64 flex items-center justify-center bg-gray-700 rounded-lg">
            <div class="text-center">
                <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p class="text-gray-400">Performance chart will be implemented here</p>
                <p class="text-sm text-gray-500 mt-2">Real-time response time monitoring</p>
            </div>
        </div>
    </div>

    <!-- Memory Usage Chart -->
    <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
        <h3 class="text-lg font-semibold mb-4">Memory Usage Over Time</h3>
        <div class="h-64 flex items-center justify-center bg-gray-700 rounded-lg">
            <div class="text-center">
                <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                <p class="text-gray-400">Memory usage chart will be implemented here</p>
                <p class="text-sm text-gray-500 mt-2">Track memory consumption patterns</p>
            </div>
        </div>
    </div>
</div>

<!-- Performance Recommendations -->
<div class="bg-gray-800 rounded-lg p-6 border border-gray-700 mb-8">
    <h3 class="text-lg font-semibold mb-4">Performance Recommendations</h3>
    <div class="space-y-4">
        <div class="flex items-start p-4 bg-green-900/20 border border-green-700 rounded-lg">
            <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
            </svg>
            <div>
                <h4 class="font-medium text-green-400">Good Cache Performance</h4>
                <p class="text-sm text-gray-300 mt-1">Your cache hit rate is excellent at {{ $performance['cache_stats']['hit_rate'] }}%. Keep up the good work!</p>
            </div>
        </div>

        @if($performance['memory_usage']['current'] > 80)
        <div class="flex items-start p-4 bg-red-900/20 border border-red-700 rounded-lg">
            <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
                <h4 class="font-medium text-red-400">High Memory Usage</h4>
                <p class="text-sm text-gray-300 mt-1">Memory usage is at {{ $performance['memory_usage']['current'] }}%. Consider optimizing memory-intensive operations.</p>
            </div>
        </div>
        @elseif($performance['memory_usage']['current'] > 60)
        <div class="flex items-start p-4 bg-yellow-900/20 border border-yellow-700 rounded-lg">
            <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
                <h4 class="font-medium text-yellow-400">Moderate Memory Usage</h4>
                <p class="text-sm text-gray-300 mt-1">Memory usage is at {{ $performance['memory_usage']['current'] }}%. Monitor for potential optimization opportunities.</p>
            </div>
        </div>
        @endif

        @if($performance['response_times']['average'] > 200)
        <div class="flex items-start p-4 bg-yellow-900/20 border border-yellow-700 rounded-lg">
            <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h4 class="font-medium text-yellow-400">Response Time Optimization</h4>
                <p class="text-sm text-gray-300 mt-1">Average response time is {{ $performance['response_times']['average'] }}ms. Consider optimizing database queries and caching strategies.</p>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Quick Actions -->
<div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
    <h3 class="text-lg font-semibold mb-4">Performance Actions</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg text-center transition-colors">
            <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            <span class="font-medium">Clear Cache</span>
            <p class="text-xs text-gray-300 mt-1">Clear all application cache</p>
        </button>

        <button class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors">
            <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            <span class="font-medium">Run Analysis</span>
            <p class="text-xs text-gray-300 mt-1">Analyze performance metrics</p>
        </button>

        <button class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition-colors">
            <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="font-medium">Optimize</span>
            <p class="text-xs text-gray-300 mt-1">Run optimization tasks</p>
        </button>
    </div>
</div>
@endsection
