<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\CacheService;
use App\Services\DatabaseOptimizationService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Http\JsonResponse;

class PerformanceController extends Controller
{
    protected $cacheService;
    protected $dbOptimizationService;

    public function __construct(CacheService $cacheService, DatabaseOptimizationService $dbOptimizationService)
    {
        $this->middleware(['auth', 'role:admin']);
        $this->cacheService = $cacheService;
        $this->dbOptimizationService = $dbOptimizationService;
    }

    /**
     * Display performance dashboard
     */
    public function index()
    {
        $cacheStats = $this->cacheService->getCacheStatistics();
        $platformStats = $this->cacheService->getPlatformStatistics();
        $dbAnalysis = $this->dbOptimizationService->analyzePerformance();

        return view('admin.performance.index', compact('cacheStats', 'platformStats', 'dbAnalysis'));
    }

    /**
     * Get cache statistics
     */
    public function cacheStats(): JsonResponse
    {
        $stats = $this->cacheService->getCacheStatistics();

        return response()->json([
            'success' => true,
            'stats' => $stats,
        ]);
    }

    /**
     * Clear specific cache
     */
    public function clearCache(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:all,config,route,view,application',
        ]);

        $type = $request->get('type');
        $result = [];

        try {
            switch ($type) {
                case 'all':
                    Artisan::call('cache:clear');
                    Artisan::call('config:clear');
                    Artisan::call('route:clear');
                    Artisan::call('view:clear');
                    $this->cacheService->clearAllCaches();
                    $result['message'] = 'All caches cleared successfully';
                    break;

                case 'config':
                    Artisan::call('config:clear');
                    $result['message'] = 'Configuration cache cleared';
                    break;

                case 'route':
                    Artisan::call('route:clear');
                    $result['message'] = 'Route cache cleared';
                    break;

                case 'view':
                    Artisan::call('view:clear');
                    $result['message'] = 'View cache cleared';
                    break;

                case 'application':
                    $this->cacheService->clearAllCaches();
                    $result['message'] = 'Application cache cleared';
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Warm up caches
     */
    public function warmupCache(): JsonResponse
    {
        try {
            $this->cacheService->warmupCaches();

            return response()->json([
                'success' => true,
                'message' => 'Caches warmed up successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to warm up caches: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Optimize application performance
     */
    public function optimize(): JsonResponse
    {
        try {
            Artisan::call('optimize:performance');
            $output = Artisan::output();

            return response()->json([
                'success' => true,
                'message' => 'Performance optimization completed',
                'output' => $output,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Optimization failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get database analysis
     */
    public function databaseAnalysis(): JsonResponse
    {
        try {
            $analysis = $this->dbOptimizationService->analyzePerformance();

            return response()->json([
                'success' => true,
                'analysis' => $analysis,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Database analysis failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Optimize database tables
     */
    public function optimizeDatabase(): JsonResponse
    {
        try {
            $results = $this->dbOptimizationService->optimizeTables();
            $analyzeResults = $this->dbOptimizationService->analyzeTableStatistics();

            return response()->json([
                'success' => true,
                'message' => 'Database optimization completed',
                'optimize_results' => $results,
                'analyze_results' => $analyzeResults,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Database optimization failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get system information
     */
    public function systemInfo(): JsonResponse
    {
        $info = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status(),
            'cache_driver' => config('cache.default'),
            'session_driver' => config('session.driver'),
            'queue_driver' => config('queue.default'),
            'database_driver' => config('database.default'),
        ];

        return response()->json([
            'success' => true,
            'info' => $info,
        ]);
    }

    /**
     * Get performance recommendations
     */
    public function recommendations(): JsonResponse
    {
        $recommendations = [
            'caching' => [
                'Use Redis for cache and sessions in production',
                'Enable OPcache for PHP optimization',
                'Implement CDN for static assets',
                'Use browser caching headers',
            ],
            'database' => [
                'Add indexes on frequently queried columns',
                'Use database connection pooling',
                'Enable query caching',
                'Regular database maintenance',
            ],
            'application' => [
                'Use queue workers for background jobs',
                'Optimize images and assets',
                'Enable gzip compression',
                'Use lazy loading for relationships',
            ],
            'monitoring' => [
                'Set up application performance monitoring',
                'Monitor database slow queries',
                'Track cache hit rates',
                'Monitor server resources',
            ],
        ];

        return response()->json([
            'success' => true,
            'recommendations' => $recommendations,
        ]);
    }
}
