<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventRsvp extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'rsvp_status',
        'rsvp_at',
        'attended_at',
        'notes',
    ];

    protected $casts = [
        'rsvp_at' => 'datetime',
        'attended_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Scopes
     */
    public function scopeAttending($query)
    {
        return $query->where('rsvp_status', 'attending');
    }

    public function scopeAttended($query)
    {
        return $query->where('rsvp_status', 'attended');
    }

    /**
     * Helper Methods
     */
    public function isAttending(): bool
    {
        return $this->rsvp_status === 'attending';
    }

    public function hasAttended(): bool
    {
        return $this->rsvp_status === 'attended';
    }
}
