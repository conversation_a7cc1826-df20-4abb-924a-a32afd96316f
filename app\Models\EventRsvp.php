<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventRsvp extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'event_id',
        'status',
        'attended',
        'rsvp_at',
    ];

    protected $casts = [
        'rsvp_at' => 'datetime',
        'attended' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Scopes
     */
    public function scopeAttending($query)
    {
        return $query->where('status', 'attending');
    }

    public function scopeAttended($query)
    {
        return $query->where('attended', true);
    }

    /**
     * Helper Methods
     */
    public function isAttending(): bool
    {
        return $this->status === 'attending';
    }

    public function hasAttended(): bool
    {
        return $this->attended === true;
    }
}
