@extends('layouts.dashboard')

@section('title', 'My Analytics')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-white mb-2">My Analytics</h1>
        <p class="text-gray-400">Track your learning progress and achievements</p>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total XP -->
        <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg p-6 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-primary-100 text-sm font-medium">Total XP</p>
                    <p class="text-3xl font-bold">{{ number_format(auth()->user()->xp ?? 0) }}</p>
                </div>
                <div class="p-3 bg-primary-500 rounded-full">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-primary-100 text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                    </svg>
                    Level {{ auth()->user()->level ?? 1 }}
                </div>
            </div>
        </div>

        <!-- Courses Completed -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm font-medium">Courses Completed</p>
                    <p class="text-3xl font-bold text-white">{{ auth()->user()->completedCourses()->count() }}</p>
                </div>
                <div class="p-3 bg-green-600 rounded-full">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-green-400 text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                    </svg>
                    {{ auth()->user()->enrollments()->count() }} total enrolled
                </div>
            </div>
        </div>

        <!-- Study Streak -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm font-medium">Study Streak</p>
                    <p class="text-3xl font-bold text-white">{{ auth()->user()->study_streak ?? 0 }}</p>
                </div>
                <div class="p-3 bg-orange-600 rounded-full">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.879 16.121A3 3 0 1012.015 11L11 14H9c0 .768.293 1.536.879 2.121z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-orange-400 text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    days in a row
                </div>
            </div>
        </div>

        <!-- Badges Earned -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-400 text-sm font-medium">Badges Earned</p>
                    <p class="text-3xl font-bold text-white">{{ auth()->user()->badges()->count() }}</p>
                </div>
                <div class="p-3 bg-purple-600 rounded-full">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-purple-400 text-sm">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                    achievements unlocked
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Learning Progress -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Learning Progress</h3>
            <div class="space-y-4">
                @foreach(auth()->user()->enrollments()->with('course')->take(5)->get() as $enrollment)
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h4 class="text-white font-medium">{{ $enrollment->course->title }}</h4>
                            <div class="w-full bg-gray-700 rounded-full h-2 mt-2">
                                <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                                     style="width: {{ $enrollment->progress_percentage }}%"></div>
                            </div>
                        </div>
                        <span class="text-primary-400 font-medium ml-4">{{ $enrollment->progress_percentage }}%</span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Weekly Activity -->
        <div class="bg-gray-800 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Weekly Activity</h3>
            <div class="space-y-3">
                @for($i = 6; $i >= 0; $i--)
                    @php
                        $date = now()->subDays($i);
                        $activity = rand(0, 100); // Mock data - replace with real activity
                    @endphp
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400 text-sm">{{ $date->format('M j') }}</span>
                        <div class="flex items-center space-x-2">
                            <div class="w-32 bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ $activity }}%"></div>
                            </div>
                            <span class="text-white text-sm w-8">{{ $activity }}%</span>
                        </div>
                    </div>
                @endfor
            </div>
        </div>
    </div>

    <!-- Recent Achievements -->
    <div class="bg-gray-800 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-white mb-4">Recent Achievements</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @forelse(auth()->user()->badges()->latest()->take(6)->get() as $badge)
                <div class="bg-gray-700 rounded-lg p-4 flex items-center space-x-3">
                    <div class="w-12 h-12 rounded-full flex items-center justify-center text-xl"
                         style="background-color: {{ $badge->color }}">
                        {{ $badge->icon }}
                    </div>
                    <div class="flex-1">
                        <h4 class="text-white font-medium">{{ $badge->name }}</h4>
                        <p class="text-gray-400 text-sm">{{ $badge->pivot->earned_at->diffForHumans() }}</p>
                    </div>
                </div>
            @empty
                <div class="col-span-full text-center py-8">
                    <div class="text-gray-400">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-300">No achievements yet</h3>
                        <p class="mt-1 text-sm text-gray-400">Complete courses and activities to earn your first badge!</p>
                    </div>
                </div>
            @endforelse
        </div>
    </div>

    <!-- Goals and Targets -->
    <div class="bg-gray-800 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Goals & Targets</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Weekly Goal -->
            <div class="text-center">
                <div class="relative w-24 h-24 mx-auto mb-4">
                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-700" stroke="currentColor" stroke-width="3" fill="none"
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="text-primary-600" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                              stroke-dasharray="75, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-xl font-bold text-white">75%</span>
                    </div>
                </div>
                <h4 class="text-white font-medium">Weekly Goal</h4>
                <p class="text-gray-400 text-sm">5 hours of study</p>
            </div>

            <!-- Monthly Goal -->
            <div class="text-center">
                <div class="relative w-24 h-24 mx-auto mb-4">
                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-700" stroke="currentColor" stroke-width="3" fill="none"
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="text-green-500" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                              stroke-dasharray="60, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-xl font-bold text-white">60%</span>
                    </div>
                </div>
                <h4 class="text-white font-medium">Monthly Goal</h4>
                <p class="text-gray-400 text-sm">Complete 3 courses</p>
            </div>

            <!-- Yearly Goal -->
            <div class="text-center">
                <div class="relative w-24 h-24 mx-auto mb-4">
                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-700" stroke="currentColor" stroke-width="3" fill="none"
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        <path class="text-purple-500" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                              stroke-dasharray="40, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-xl font-bold text-white">40%</span>
                    </div>
                </div>
                <h4 class="text-white font-medium">Yearly Goal</h4>
                <p class="text-gray-400 text-sm">Reach Level 10</p>
            </div>
        </div>
    </div>
</div>
@endsection
