<?php $__env->startSection('title', 'Community Chat - The Real World'); ?>
<?php $__env->startSection('page-title', 'Community Chat'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 h-full">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
        <!-- Chat Rooms Sidebar -->
        <div class="lg:col-span-1">
            <div class="card h-full">
                <h3 class="text-lg font-semibold mb-4">Chat Rooms</h3>
                
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    <?php $__currentLoopData = $availableRooms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <a href="<?php echo e(route('dashboard.chat', ['room' => $room->id])); ?>" 
                       class="block p-3 rounded-lg transition-colors <?php echo e($selectedRoom && $selectedRoom->id === $room->id ? 'bg-primary-600' : 'hover:bg-gray-700'); ?>">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3"><?php echo e($room->getRoomIcon()); ?></span>
                            <div class="flex-1 min-w-0">
                                <h4 class="font-medium truncate <?php echo e($selectedRoom && $selectedRoom->id === $room->id ? 'text-white' : 'text-gray-200'); ?>">
                                    <?php echo e($room->name); ?>

                                </h4>
                                <p class="text-xs text-gray-400 truncate"><?php echo e($room->description); ?></p>
                                
                                <!-- Room Stats -->
                                <div class="flex items-center mt-1 text-xs text-gray-500">
                                    <span class="mr-3"><?php echo e($room->getActiveUsersCount()); ?> online</span>
                                    <?php if($room->campus): ?>
                                        <span class="bg-gray-600 px-2 py-0.5 rounded text-xs"><?php echo e($room->campus->name); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <?php if($availableRooms->count() === 0): ?>
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">No Chat Rooms</h3>
                    <p class="text-gray-400">No chat rooms are available at the moment.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Chat Area -->
        <div class="lg:col-span-3">
            <?php if($selectedRoom): ?>
            <div class="card h-full flex flex-col">
                <!-- Chat Header -->
                <div class="flex items-center justify-between p-4 border-b border-gray-700">
                    <div class="flex items-center">
                        <span class="text-2xl mr-3"><?php echo e($selectedRoom->getRoomIcon()); ?></span>
                        <div>
                            <h3 class="text-lg font-semibold"><?php echo e($selectedRoom->name); ?></h3>
                            <p class="text-sm text-gray-400"><?php echo e($selectedRoom->description); ?></p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4 text-sm text-gray-400">
                        <span><?php echo e($selectedRoom->getActiveUsersCount()); ?> online</span>
                        <?php if($selectedRoom->is_moderated): ?>
                            <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs">Moderated</span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Messages Area -->
                <div class="flex-1 overflow-y-auto p-4 space-y-4" id="messages-container" style="max-height: 400px;">
                    <?php if($messages->count() > 0): ?>
                        <?php $__currentLoopData = $messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo $__env->make('dashboard.chat.partials.message', ['message' => $message, 'user' => $user], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-semibold mb-2">No Messages Yet</h3>
                            <p class="text-gray-400">Be the first to start the conversation!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Message Input -->
                <div class="p-4 border-t border-gray-700">
                    <form id="message-form" class="flex space-x-3">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="room_id" value="<?php echo e($selectedRoom->id); ?>">
                        <div class="flex-1">
                            <input type="text" 
                                   name="message" 
                                   id="message-input"
                                   placeholder="Type your message..." 
                                   class="input-field w-full"
                                   maxlength="1000"
                                   required>
                        </div>
                        <button type="submit" 
                                class="btn-primary px-6 py-2"
                                id="send-button">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </form>
                    
                    <!-- Typing Indicator -->
                    <div id="typing-indicator" class="mt-2 text-sm text-gray-500 hidden">
                        <span class="italic">Someone is typing...</span>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <!-- No Room Selected -->
            <div class="card h-full flex items-center justify-center">
                <div class="text-center">
                    <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Welcome to Community Chat</h3>
                    <p class="text-gray-400 mb-6">Select a chat room from the sidebar to start chatting with the community.</p>
                    <?php if($availableRooms->count() > 0): ?>
                        <a href="<?php echo e(route('dashboard.chat', ['room' => $availableRooms->first()->id])); ?>" 
                           class="btn-primary">
                            Join <?php echo e($availableRooms->first()->name); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php if($selectedRoom): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');
    const messagesContainer = document.getElementById('messages-container');

    // Auto-scroll to bottom
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Initial scroll to bottom
    scrollToBottom();

    // Handle form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const message = messageInput.value.trim();
        if (!message) return;

        // Disable form
        sendButton.disabled = true;
        messageInput.disabled = true;

        // Send message
        fetch('<?php echo e(route("dashboard.chat.send")); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                room_id: <?php echo e($selectedRoom->id); ?>,
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add message to chat
                messagesContainer.insertAdjacentHTML('beforeend', data.html);
                scrollToBottom();
                
                // Clear input
                messageInput.value = '';
            } else {
                showNotification(data.message || 'Failed to send message', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred while sending the message', 'error');
        })
        .finally(() => {
            // Re-enable form
            sendButton.disabled = false;
            messageInput.disabled = false;
            messageInput.focus();
        });
    });

    // Auto-focus message input
    messageInput.focus();

    // Handle Enter key
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            messageForm.dispatchEvent(new Event('submit'));
        }
    });
});

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/dashboard/chat/index.blade.php ENDPATH**/ ?>