<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\Searchable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Course extends Model
{
    use HasFactory, Searchable;

    protected $fillable = [
        'campus_id',
        'title',
        'description',
        'thumbnail_url',
        'duration_minutes',
        'difficulty',
        'sort_order',
        'is_active',
        'is_featured',
        'prerequisites',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'prerequisites' => 'array',
    ];

    /**
     * Searchable fields configuration
     */
    protected $searchable = [
        'title',
        'description',
        'content',
        'campus.name',
    ];

    /**
     * Search field weights for relevance scoring
     */
    protected $searchWeights = [
        'title' => 10,
        'description' => 5,
        'content' => 1,
        'campus.name' => 3,
    ];

    /**
     * Relationships
     */
    public function campus(): BelongsTo
    {
        return $this->belongsTo(Campus::class);
    }

    public function lessons(): HasMany
    {
        return $this->hasMany(Lesson::class);
    }

    /**
     * Get the enrollments for the course
     */
    public function enrollments()
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get enrolled users
     */
    public function enrolledUsers()
    {
        return $this->belongsToMany(User::class, 'course_enrollments')
            ->withPivot(['progress_percentage', 'enrolled_at', 'completed_at', 'last_accessed_at'])
            ->withTimestamps();
    }

    /**
     * Check if user is enrolled in this course
     */
    public function isUserEnrolled(User $user): bool
    {
        return $this->enrollments()->where('user_id', $user->id)->exists();
    }

    /**
     * Get user's enrollment for this course
     */
    public function getUserEnrollment(User $user): ?CourseEnrollment
    {
        return $this->enrollments()->where('user_id', $user->id)->first();
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Helper Methods
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    public function getTotalDuration()
    {
        return $this->lessons()->sum('duration_seconds');
    }

    public function getProgressForUser(User $user)
    {
        $totalLessons = $this->lessons()->where('is_active', true)->count();
        if ($totalLessons === 0) return 0;

        $completedLessons = $this->lessons()
            ->where('is_active', true)
            ->whereHas('userProgress', function($query) use ($user) {
                $query->where('user_id', $user->id)->where('is_completed', true);
            })
            ->count();

        return ($completedLessons / $totalLessons) * 100;
    }

    public function isCompletedBy(User $user): bool
    {
        return $this->getProgressForUser($user) >= 100;
    }

    /**
     * Get search URL for the course
     */
    protected function getSearchUrl(): string
    {
        return route('dashboard.courses.show', $this);
    }

    /**
     * Get search image for the course
     */
    protected function getSearchImage(): ?string
    {
        return $this->getFirstMediaUrl('thumbnail');
    }

    /**
     * Get search meta information for the course
     */
    protected function getSearchMeta(): string
    {
        $meta = [];

        if ($this->campus) {
            $meta[] = $this->campus->name;
        }

        if ($this->difficulty_level) {
            $meta[] = ucfirst($this->difficulty_level);
        }

        if ($this->estimated_duration) {
            $meta[] = $this->estimated_duration . ' min';
        }

        return implode(' • ', $meta);
    }
}
