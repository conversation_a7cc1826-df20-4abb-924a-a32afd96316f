@extends('layouts.dashboard')

@section('title', $lesson->title . ' - The Real World')
@section('page-title', $lesson->title)

@section('content')
<div class="p-6">
    <!-- Breadcrumb -->
    <div class="mb-6">
        <nav class="flex items-center space-x-2 text-sm">
            <a href="{{ route('dashboard.campuses.show', $campus) }}" class="text-primary-400 hover:text-primary-300">
                {{ $campus->name }}
            </a>
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <a href="{{ route('dashboard.courses.show', $course) }}" class="text-primary-400 hover:text-primary-300">
                {{ $course->title }}
            </a>
            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-gray-400">{{ $lesson->title }}</span>
        </nav>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Video Player -->
            <div class="card mb-6">
                @if($lesson->video_url)
                <div class="aspect-video bg-gray-900 rounded-lg overflow-hidden mb-4">
                    @if($lesson->video_type === 'youtube')
                        <iframe 
                            src="{{ $lesson->getVideoEmbedUrl() }}" 
                            class="w-full h-full"
                            frameborder="0" 
                            allowfullscreen>
                        </iframe>
                    @else
                        <div class="w-full h-full flex items-center justify-center">
                            <div class="text-center">
                                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <p class="text-gray-400">Video player will be available soon</p>
                                <a href="{{ $lesson->video_url }}" target="_blank" class="text-primary-400 hover:text-primary-300 text-sm">
                                    Watch on External Platform
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
                @else
                <div class="aspect-video bg-gray-900 rounded-lg overflow-hidden mb-4 flex items-center justify-center">
                    <div class="text-center">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <p class="text-gray-400">No video available for this lesson</p>
                    </div>
                </div>
                @endif

                <!-- Lesson Info -->
                <div>
                    <div class="flex items-center justify-between mb-4">
                        <h1 class="text-2xl font-bold">{{ $lesson->title }}</h1>
                        <div class="flex items-center space-x-4">
                            @if($lesson->is_preview)
                                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">Preview</span>
                            @endif
                            <span class="text-gray-400 text-sm">{{ $lesson->getFormattedDuration() }}</span>
                            <span class="text-yellow-400 text-sm font-medium">{{ $lesson->xp_reward }} XP</span>
                        </div>
                    </div>

                    @if($lesson->description)
                    <p class="text-gray-300 mb-6 leading-relaxed">{{ $lesson->description }}</p>
                    @endif

                    <!-- Progress Bar -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-300">Your Progress</span>
                            <span class="text-sm text-gray-400">{{ round($progress->progress_percentage ?? 0) }}%</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                                 style="width: {{ $progress->progress_percentage ?? 0 }}%"></div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-4">
                        @if(!$progress->is_completed)
                        <button onclick="markAsCompleted()" class="btn-primary">
                            Mark as Completed
                        </button>
                        @else
                        <div class="flex items-center text-green-400">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Completed
                        </div>
                        @endif

                        @if($lesson->resource_url)
                        <a href="{{ $lesson->resource_url }}" target="_blank" class="btn-secondary">
                            Download Resources
                        </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Lesson Content -->
            @if($lesson->content)
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Lesson Notes</h3>
                <div class="prose prose-invert max-w-none">
                    {!! nl2br(e($lesson->content)) !!}
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Course Navigation -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Course Lessons</h3>
                <div class="space-y-2 max-h-96 overflow-y-auto">
                    @foreach($courseLessons as $index => $courseLesson)
                    <div class="flex items-center p-3 rounded-lg {{ $courseLesson->id === $lesson->id ? 'bg-primary-600' : 'hover:bg-gray-700' }} transition-colors">
                        <div class="w-6 h-6 rounded-full flex items-center justify-center mr-3 flex-shrink-0 {{ $courseLesson->is_completed ? 'bg-green-600' : 'bg-gray-600' }}">
                            @if($courseLesson->is_completed)
                                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <span class="text-white text-xs">{{ $index + 1 }}</span>
                            @endif
                        </div>
                        <div class="flex-1 min-w-0">
                            <a href="{{ route('dashboard.lessons.show', $courseLesson) }}" 
                               class="block text-sm font-medium truncate {{ $courseLesson->id === $lesson->id ? 'text-white' : 'text-gray-300 hover:text-white' }}">
                                {{ $courseLesson->title }}
                            </a>
                            <p class="text-xs text-gray-400">{{ $courseLesson->getFormattedDuration() }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="mt-6 space-y-3">
                @if($previousLesson)
                <a href="{{ route('dashboard.lessons.show', $previousLesson) }}" 
                   class="btn-secondary w-full text-center">
                    ← Previous Lesson
                </a>
                @endif

                @if($nextLesson)
                <a href="{{ route('dashboard.lessons.show', $nextLesson) }}" 
                   class="btn-primary w-full text-center">
                    Next Lesson →
                </a>
                @endif

                <a href="{{ route('dashboard.courses.show', $course) }}" 
                   class="btn-secondary w-full text-center">
                    Back to Course
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function markAsCompleted() {
    fetch(`{{ route('dashboard.lessons.complete', $lesson) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>
@endsection
