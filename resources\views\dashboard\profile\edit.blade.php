@extends('layouts.dashboard')

@section('title', 'Edit Profile - The Real World')
@section('page-title', 'Edit Profile')

@section('content')
<div class="p-6">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ route('dashboard.profile.show') }}" 
           class="inline-flex items-center text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Profile
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Form -->
        <div class="lg:col-span-2">
            <div class="card">
                <h3 class="text-xl font-semibold mb-6">Profile Information</h3>

                <form action="{{ route('dashboard.profile.update') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')

                    <!-- Avatar Upload -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-300 mb-3">Profile Picture</label>
                        <div class="flex items-center space-x-6">
                            <img src="{{ $user->getAvatarUrl() }}" 
                                 alt="{{ $user->name }}" 
                                 class="w-20 h-20 rounded-full"
                                 id="avatar-preview">
                            
                            <div class="flex-1">
                                <input type="file" 
                                       name="avatar" 
                                       id="avatar-input"
                                       accept="image/*"
                                       class="hidden">
                                
                                <div class="flex space-x-3">
                                    <button type="button" 
                                            onclick="document.getElementById('avatar-input').click()"
                                            class="btn-secondary">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        Change Photo
                                    </button>
                                    
                                    @if($user->avatar_url)
                                    <form action="{{ route('dashboard.profile.delete-avatar') }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn-secondary text-red-400 hover:text-red-300">
                                            Remove
                                        </button>
                                    </form>
                                    @endif
                                </div>
                                
                                <p class="text-xs text-gray-400 mt-2">JPG, PNG or GIF. Max size 2MB.</p>
                            </div>
                        </div>
                        @error('avatar')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Basic Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                            <input type="text" 
                                   name="name" 
                                   id="name"
                                   value="{{ old('name', $user->name) }}"
                                   class="input-field"
                                   required>
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                            <input type="email" 
                                   name="email" 
                                   id="email"
                                   value="{{ old('email', $user->email) }}"
                                   class="input-field"
                                   required>
                            @error('email')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Bio -->
                    <div class="mb-6">
                        <label for="bio" class="block text-sm font-medium text-gray-300 mb-2">Bio</label>
                        <textarea name="bio" 
                                  id="bio"
                                  rows="4"
                                  placeholder="Tell us about yourself..."
                                  class="input-field">{{ old('bio', $user->bio) }}</textarea>
                        <p class="text-xs text-gray-400 mt-1">Maximum 500 characters</p>
                        @error('bio')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Location -->
                    <div class="mb-6">
                        <label for="location" class="block text-sm font-medium text-gray-300 mb-2">Location</label>
                        <input type="text" 
                               name="location" 
                               id="location"
                               value="{{ old('location', $user->location) }}"
                               placeholder="City, Country"
                               class="input-field">
                        @error('location')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Social Links -->
                    <div class="mb-6">
                        <h4 class="text-lg font-medium mb-4">Social Links</h4>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="website" class="block text-sm font-medium text-gray-300 mb-2">Website</label>
                                <input type="url" 
                                       name="website" 
                                       id="website"
                                       value="{{ old('website', $user->website) }}"
                                       placeholder="https://yourwebsite.com"
                                       class="input-field">
                                @error('website')
                                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="twitter" class="block text-sm font-medium text-gray-300 mb-2">Twitter Username</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-400">@</span>
                                    </div>
                                    <input type="text" 
                                           name="twitter" 
                                           id="twitter"
                                           value="{{ old('twitter', $user->twitter) }}"
                                           placeholder="username"
                                           class="input-field pl-8">
                                </div>
                                @error('twitter')
                                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="linkedin" class="block text-sm font-medium text-gray-300 mb-2">LinkedIn Username</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span class="text-gray-400 text-sm">linkedin.com/in/</span>
                                    </div>
                                    <input type="text" 
                                           name="linkedin" 
                                           id="linkedin"
                                           value="{{ old('linkedin', $user->linkedin) }}"
                                           placeholder="username"
                                           class="input-field pl-32">
                                </div>
                                @error('linkedin')
                                    <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex items-center justify-end space-x-4">
                        <a href="{{ route('dashboard.profile.show') }}" class="btn-secondary">Cancel</a>
                        <button type="submit" class="btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Change Password -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Change Password</h3>
                
                <form action="{{ route('dashboard.profile.update-password') }}" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="space-y-4">
                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
                            <input type="password" 
                                   name="current_password" 
                                   id="current_password"
                                   class="input-field"
                                   required>
                            @error('current_password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">New Password</label>
                            <input type="password" 
                                   name="password" 
                                   id="password"
                                   class="input-field"
                                   required>
                            @error('password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
                            <input type="password" 
                                   name="password_confirmation" 
                                   id="password_confirmation"
                                   class="input-field"
                                   required>
                        </div>

                        <button type="submit" class="btn-primary w-full">
                            Update Password
                        </button>
                    </div>
                </form>
            </div>

            <!-- Account Info -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Account Information</h3>
                
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Member Since:</span>
                        <span>{{ $user->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Account Status:</span>
                        <span class="text-green-400">{{ $user->is_active ? 'Active' : 'Inactive' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Email Verified:</span>
                        <span class="text-green-400">{{ $user->email_verified_at ? 'Yes' : 'No' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Subscription:</span>
                        <span class="capitalize">{{ $user->subscription_plan ?? 'Free' }}</span>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t border-gray-700">
                    <a href="{{ route('dashboard.settings.index') }}" class="btn-secondary w-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        Account Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const avatarInput = document.getElementById('avatar-input');
    const avatarPreview = document.getElementById('avatar-preview');

    avatarInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                avatarPreview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endsection
