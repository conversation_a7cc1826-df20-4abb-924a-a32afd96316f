@extends('layouts.app')

@section('title', 'Welcome to The Real World!')
@section('description', 'Your subscription is now active. Welcome to The Real World community!')

@section('content')
<section class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Success Icon -->
        <div class="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>

        <!-- Success Message -->
        <h1 class="text-4xl md:text-5xl font-bold font-display mb-6">
            🎉 Welcome to <span class="text-gradient">The Real World!</span>
        </h1>
        
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Congratulations! Your subscription is now active and you have full access to all campuses, courses, and the community. 
            Your journey to financial freedom starts now.
        </p>

        <!-- Subscription Details -->
        <div class="bg-gray-800 rounded-lg p-8 mb-8 max-w-2xl mx-auto">
            <h2 class="text-2xl font-bold mb-6">Your Subscription Details</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                <div>
                    <h3 class="font-semibold text-gray-400 mb-2">Plan</h3>
                    <p class="text-lg font-bold text-primary-400 capitalize">{{ $plan ?? 'Prosper' }}</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-400 mb-2">Status</h3>
                    <p class="text-lg font-bold text-green-400">Active</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-400 mb-2">Trial Period</h3>
                    <p class="text-lg">7 days free</p>
                </div>
                
                <div>
                    <h3 class="font-semibold text-gray-400 mb-2">Next Billing</h3>
                    <p class="text-lg">{{ now()->addDays(7)->format('M j, Y') }}</p>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-gray-800 rounded-lg p-8 mb-8 max-w-2xl mx-auto">
            <h2 class="text-2xl font-bold mb-6">What's Next?</h2>
            
            <div class="space-y-4 text-left">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <span class="text-white font-bold text-sm">1</span>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Complete Your Profile</h3>
                        <p class="text-gray-400">Add your bio, location, and goals to connect with the community.</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <span class="text-white font-bold text-sm">2</span>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Choose Your First Campus</h3>
                        <p class="text-gray-400">Pick a business model that interests you and start learning.</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <span class="text-white font-bold text-sm">3</span>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Join the Community</h3>
                        <p class="text-gray-400">Connect with other students and mentors in the chat rooms.</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <span class="text-white font-bold text-sm">4</span>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Complete Daily Tasks</h3>
                        <p class="text-gray-400">Earn XP and level up by completing daily challenges.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <a href="{{ route('dashboard.index') }}" class="btn-primary text-lg px-8 py-4">
                Go to Dashboard
            </a>
            <a href="{{ route('campuses') }}" class="btn-secondary text-lg px-8 py-4">
                Explore Campuses
            </a>
        </div>

        <!-- Support Info -->
        <div class="text-center">
            <p class="text-gray-400 mb-4">
                Need help getting started? Our support team is here for you.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('faq') }}" class="text-primary-400 hover:text-primary-300">
                    View FAQ
                </a>
                <span class="hidden sm:inline text-gray-600">•</span>
                <a href="mailto:<EMAIL>" class="text-primary-400 hover:text-primary-300">
                    Contact Support
                </a>
                <span class="hidden sm:inline text-gray-600">•</span>
                <a href="{{ route('dashboard.profile') }}" class="text-primary-400 hover:text-primary-300">
                    Manage Subscription
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
