@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for The Real World clone */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-900 text-white font-sans;
  }
}

@layer components {
  
  .btn-dashboard {
    @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 inline-block text-center;
  }

  .btn-primary {
    @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center text-center;
  }

  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center text-center;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-red-600 text-white border border-white hover:border-red-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center text-center;
  }

  .btn-danger {
    @apply bg-transparent hover:bg-red-600 text-white border border-white hover:border-red-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center text-center;
  }
  
  .card {
    @apply bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-700;
  }

  .input-field {
    @apply bg-gray-700 border border-gray-600 text-white rounded-lg px-4 py-3 focus:ring-2 focus:ring-red-500 focus:border-transparent;
  }

  .nav-link {
    @apply font-medium transition-colors duration-200 hover:text-red-400;
  }

  .nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors duration-200;
  }

  .nav-item.active {
    @apply bg-red-600 text-white;
  }

  .nav-item svg {
    @apply mr-3 flex-shrink-0;
  }

  .mobile-nav-link {
    @apply flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-red-400 to-red-600 bg-clip-text text-transparent;
  }
}