<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Campus;
use App\Models\Testimonial;
use App\Models\User;

class HomeController extends Controller
{
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => User::whereNotNull('email_verified_at')->count(),
            'total_income' => '$2,847,392', // This would be calculated from real data
        ];

        $featuredCampuses = Campus::where('is_active', true)
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        $testimonials = Testimonial::where('status', 'approved')
            ->where('is_featured', true)
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        return view('home.index', compact('stats', 'featuredCampuses', 'testimonials'));
    }

    public function campuses()
    {
        $campuses = Campus::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        return view('home.campuses', compact('campuses'));
    }

    public function pricing()
    {
        $plans = [
            'prosper' => [
                'name' => 'Prosper',
                'monthly_price' => 49,
                'yearly_price' => 490,
                'features' => [
                    'Access to 5 campuses',
                    'Basic course library',
                    'Community chat access',
                    'Monthly live events',
                    'Mobile app access',
                ]
            ],
            'conquer' => [
                'name' => 'Conquer',
                'monthly_price' => 99,
                'yearly_price' => 990,
                'features' => [
                    'Access to all campuses',
                    'Complete course library',
                    'Priority chat support',
                    'Weekly live events',
                    'Mobile app access',
                    'Exclusive mentor sessions',
                    'Advanced analytics',
                ]
            ],
            'champions' => [
                'name' => 'Champions',
                'monthly_price' => 199,
                'yearly_price' => 1990,
                'features' => [
                    'Everything in Conquer',
                    'Direct mentor access',
                    'Daily live sessions',
                    '1-on-1 coaching calls',
                    'Private mastermind group',
                    'Business review sessions',
                    'Priority support',
                ]
            ]
        ];

        return view('home.pricing', compact('plans'));
    }

    public function testimonials()
    {
        $testimonials = Testimonial::where('status', 'approved')
            ->orderBy('sort_order')
            ->paginate(12);

        return view('home.testimonials', compact('testimonials'));
    }

    public function faq()
    {
        $faqs = [
            [
                'question' => 'What is The Real World?',
                'answer' => 'The Real World is a comprehensive online education platform that teaches you real-world skills to build wealth and achieve financial freedom.'
            ],
            [
                'question' => 'How does the campus system work?',
                'answer' => 'Each campus focuses on a specific business model or skill set. You can access multiple campuses based on your subscription plan and learn from expert mentors.'
            ],
            [
                'question' => 'Can I cancel my subscription anytime?',
                'answer' => 'Yes, you can cancel your subscription at any time. You will continue to have access until the end of your current billing period.'
            ],
            [
                'question' => 'Is there a money-back guarantee?',
                'answer' => 'We offer a 7-day free trial for all new members. If you\'re not satisfied, you can cancel within the trial period at no cost.'
            ],
            [
                'question' => 'How do I access the mobile app?',
                'answer' => 'The mobile app is available for download on both iOS and Android devices. You can access all courses and features on the go.'
            ]
        ];

        return view('home.faq', compact('faqs'));
    }

    public function terms()
    {
        return view('home.terms');
    }

    public function privacy()
    {
        return view('home.privacy');
    }

    public function earningsDisclaimer()
    {
        return view('home.earnings-disclaimer');
    }

    public function unsubscribe($token)
    {
        // In a real application, you would validate the token and unsubscribe the user
        // For now, we'll just show a confirmation page
        return view('home.unsubscribe', compact('token'));
    }
}
