<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Anyone can submit a contact form
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|in:general,membership,technical,billing,partnership,media',
            'message' => 'required|string|min:10|max:2000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Your name is required.',
            'name.max' => 'Name cannot exceed 255 characters.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.max' => 'Email address cannot exceed 255 characters.',
            'subject.required' => 'Please select a subject for your message.',
            'subject.in' => 'Please select a valid subject category.',
            'message.required' => 'Message is required.',
            'message.min' => 'Message must be at least 10 characters long.',
            'message.max' => 'Message cannot exceed 2000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => 'full name',
            'email' => 'email address',
            'subject' => 'subject',
            'message' => 'message',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check for spam patterns
            $spamPatterns = [
                'viagra',
                'casino',
                'lottery',
                'winner',
                'congratulations',
                'click here',
                'free money',
                'make money fast',
            ];

            $message = strtolower($this->message);
            foreach ($spamPatterns as $pattern) {
                if (str_contains($message, $pattern)) {
                    $validator->errors()->add('message', 'Your message appears to contain spam content.');
                    break;
                }
            }

            // Check for excessive links
            $linkCount = substr_count($message, 'http');
            if ($linkCount > 2) {
                $validator->errors()->add('message', 'Your message contains too many links.');
            }

            // Check for excessive caps
            $capsPercentage = 0;
            if (strlen($this->message) > 0) {
                $capsCount = strlen(preg_replace('/[^A-Z]/', '', $this->message));
                $capsPercentage = ($capsCount / strlen($this->message)) * 100;
            }
            
            if ($capsPercentage > 50 && strlen($this->message) > 20) {
                $validator->errors()->add('message', 'Please avoid using excessive capital letters.');
            }
        });
    }
}
