<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add indexes for better query performance
        
        // Course enrollments
        Schema::table('course_enrollments', function (Blueprint $table) {
            if (!$this->hasIndex('course_enrollments', 'course_enrollments_progress_percentage_index')) {
                $table->index('progress_percentage');
            }
            if (!$this->hasIndex('course_enrollments', 'course_enrollments_completed_at_index')) {
                $table->index('completed_at');
            }
            if (!$this->hasIndex('course_enrollments', 'course_enrollments_last_accessed_at_index')) {
                $table->index('last_accessed_at');
            }
        });
        
        // Event RSVPs
        Schema::table('event_rsvps', function (Blueprint $table) {
            if (!$this->hasIndex('event_rsvps', 'event_rsvps_status_index')) {
                $table->index('status');
            }
        });
        
        // Chat messages
        Schema::table('chat_messages', function (Blueprint $table) {
            if (!$this->hasIndex('chat_messages', 'chat_messages_type_index')) {
                $table->index('type');
            }
            if (!$this->hasIndex('chat_messages', 'chat_messages_is_edited_index')) {
                $table->index('is_edited');
            }
        });
        
        // Lessons
        Schema::table('lessons', function (Blueprint $table) {
            if (!$this->hasIndex('lessons', 'lessons_sort_order_index')) {
                $table->index('sort_order');
            }
            if (Schema::hasColumn('lessons', 'is_published') && !$this->hasIndex('lessons', 'lessons_is_published_index')) {
                $table->index('is_published');
            }
        });
        
        // User badges
        Schema::table('user_badges', function (Blueprint $table) {
            if (!$this->hasIndex('user_badges', 'user_badges_awarded_at_index')) {
                $table->index('awarded_at');
            }
        });
        
        // Subscriptions (if exists)
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                if (!$this->hasIndex('subscriptions', 'subscriptions_stripe_status_index')) {
                    $table->index('stripe_status');
                }
                if (!$this->hasIndex('subscriptions', 'subscriptions_ends_at_index')) {
                    $table->index('ends_at');
                }
            });
        }
        
        // Composite indexes for common queries
        Schema::table('courses', function (Blueprint $table) {
            if (!$this->hasIndex('courses', 'courses_is_published_is_featured_index')) {
                $table->index(['is_published', 'is_featured']);
            }
            if (!$this->hasIndex('courses', 'courses_campus_id_is_published_index')) {
                $table->index(['campus_id', 'is_published']);
            }
        });
        
        Schema::table('events', function (Blueprint $table) {
            if (!$this->hasIndex('events', 'events_start_time_is_published_index')) {
                $table->index(['start_time', 'is_published']);
            }
            if (!$this->hasIndex('events', 'events_category_start_time_index')) {
                $table->index(['category', 'start_time']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the indexes
        Schema::table('course_enrollments', function (Blueprint $table) {
            $table->dropIndex(['progress_percentage']);
            $table->dropIndex(['completed_at']);
            $table->dropIndex(['last_accessed_at']);
        });
        
        Schema::table('event_rsvps', function (Blueprint $table) {
            $table->dropIndex(['status']);
        });
        
        Schema::table('chat_messages', function (Blueprint $table) {
            $table->dropIndex(['type']);
            $table->dropIndex(['is_edited']);
        });
        
        Schema::table('lessons', function (Blueprint $table) {
            $table->dropIndex(['sort_order']);
            if (Schema::hasColumn('lessons', 'is_published')) {
                $table->dropIndex(['is_published']);
            }
        });
        
        Schema::table('user_badges', function (Blueprint $table) {
            $table->dropIndex(['awarded_at']);
        });
        
        if (Schema::hasTable('subscriptions')) {
            Schema::table('subscriptions', function (Blueprint $table) {
                $table->dropIndex(['stripe_status']);
                $table->dropIndex(['ends_at']);
            });
        }
        
        Schema::table('courses', function (Blueprint $table) {
            $table->dropIndex(['is_published', 'is_featured']);
            $table->dropIndex(['campus_id', 'is_published']);
        });
        
        Schema::table('events', function (Blueprint $table) {
            $table->dropIndex(['start_time', 'is_published']);
            $table->dropIndex(['category', 'start_time']);
        });
    }
    
    /**
     * Check if an index exists on a table
     */
    private function hasIndex(string $table, string $indexName): bool
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM `{$table}` WHERE Key_name = ?", [$indexName]);
            return count($indexes) > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
};
