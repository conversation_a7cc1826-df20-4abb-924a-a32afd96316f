<?php

namespace App\Traits;

use App\Models\Media;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

trait HasMedia
{
    /**
     * Get all media for the model
     */
    public function media(): MorphMany
    {
        return $this->morphMany(Media::class, 'model');
    }

    /**
     * Get media by collection
     */
    public function getMedia(string $collection = null)
    {
        $query = $this->media();
        
        if ($collection) {
            $query->where('collection_name', $collection);
        }
        
        return $query->get();
    }

    /**
     * Get the first media item from a collection
     */
    public function getFirstMedia(string $collection = null): ?Media
    {
        return $this->getMedia($collection)->first();
    }

    /**
     * Get the URL of the first media item from a collection
     */
    public function getFirstMediaUrl(string $collection = null, string $default = ''): string
    {
        $media = $this->getFirstMedia($collection);
        return $media ? $media->getUrl() : $default;
    }

    /**
     * Add media to the model
     */
    public function addMedia(UploadedFile $file, string $collection = null, array $metadata = []): Media
    {
        $disk = config('filesystems.default', 'public');
        $directory = $this->getMediaDirectory($collection);
        
        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        $path = $file->storeAs($directory, $filename, $disk);
        
        return $this->media()->create([
            'name' => $file->getClientOriginalName(),
            'file_name' => $filename,
            'mime_type' => $file->getMimeType(),
            'path' => $path,
            'disk' => $disk,
            'size' => $file->getSize(),
            'metadata' => $metadata,
            'collection_name' => $collection,
            'user_id' => auth()->id(),
            'is_public' => true,
            'uploaded_at' => now(),
        ]);
    }

    /**
     * Add media from URL
     */
    public function addMediaFromUrl(string $url, string $collection = null, array $metadata = []): Media
    {
        $disk = config('filesystems.default', 'public');
        $directory = $this->getMediaDirectory($collection);
        
        // Download file content
        $content = file_get_contents($url);
        $filename = $this->generateFilenameFromUrl($url);
        $path = $directory . '/' . $filename;
        
        // Store file
        Storage::disk($disk)->put($path, $content);
        
        // Get file info
        $mimeType = Storage::disk($disk)->mimeType($path);
        $size = Storage::disk($disk)->size($path);
        
        return $this->media()->create([
            'name' => basename($url),
            'file_name' => $filename,
            'mime_type' => $mimeType,
            'path' => $path,
            'disk' => $disk,
            'size' => $size,
            'metadata' => $metadata,
            'collection_name' => $collection,
            'user_id' => auth()->id(),
            'is_public' => true,
            'uploaded_at' => now(),
        ]);
    }

    /**
     * Clear media from a collection
     */
    public function clearMediaCollection(string $collection = null): void
    {
        $media = $this->getMedia($collection);
        
        foreach ($media as $mediaItem) {
            $mediaItem->deleteFile();
            $mediaItem->delete();
        }
    }

    /**
     * Delete specific media
     */
    public function deleteMedia(int $mediaId): bool
    {
        $media = $this->media()->find($mediaId);
        
        if ($media) {
            $media->deleteFile();
            return $media->delete();
        }
        
        return false;
    }

    /**
     * Check if model has media in collection
     */
    public function hasMedia(string $collection = null): bool
    {
        return $this->getMedia($collection)->count() > 0;
    }

    /**
     * Get media directory for the model
     */
    protected function getMediaDirectory(string $collection = null): string
    {
        $modelName = strtolower(class_basename($this));
        $directory = "media/{$modelName}";
        
        if ($this->getKey()) {
            $directory .= "/{$this->getKey()}";
        }
        
        if ($collection) {
            $directory .= "/{$collection}";
        }
        
        return $directory;
    }

    /**
     * Generate unique filename
     */
    protected function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = Str::slug($name);
        
        return $name . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    /**
     * Generate filename from URL
     */
    protected function generateFilenameFromUrl(string $url): string
    {
        $filename = basename(parse_url($url, PHP_URL_PATH));
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $name = pathinfo($filename, PATHINFO_FILENAME);
        $name = Str::slug($name);
        
        if (!$extension) {
            $extension = 'jpg'; // Default extension
        }
        
        return $name . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    /**
     * Get all media URLs from a collection
     */
    public function getMediaUrls(string $collection = null): array
    {
        return $this->getMedia($collection)->map(function ($media) {
            return $media->getUrl();
        })->toArray();
    }

    /**
     * Get media count for a collection
     */
    public function getMediaCount(string $collection = null): int
    {
        return $this->getMedia($collection)->count();
    }

    /**
     * Get total media size for a collection
     */
    public function getMediaSize(string $collection = null): int
    {
        return $this->getMedia($collection)->sum('size');
    }

    /**
     * Get human readable media size for a collection
     */
    public function getHumanReadableMediaSize(string $collection = null): string
    {
        $bytes = $this->getMediaSize($collection);
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
