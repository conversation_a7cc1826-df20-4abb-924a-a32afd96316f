<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Notification;
use Illuminate\Notifications\DatabaseNotification;

class NotificationService
{
    /**
     * Send notification to user
     */
    public function sendToUser(User $user, $notification): void
    {
        $user->notify($notification);
    }

    /**
     * Send notification to multiple users
     */
    public function sendToUsers($users, $notification): void
    {
        Notification::send($users, $notification);
    }

    /**
     * Send notification to all users
     */
    public function sendToAllUsers($notification): void
    {
        $users = User::all();
        Notification::send($users, $notification);
    }

    /**
     * Send notification to users with specific subscription
     */
    public function sendToSubscribers(string $plan, $notification): void
    {
        $users = User::where('subscription_plan', $plan)->get();
        Notification::send($users, $notification);
    }

    /**
     * Send notification to premium users
     */
    public function sendToPremiumUsers($notification): void
    {
        $users = User::whereNotNull('subscription_plan')
            ->where('subscription_plan', '!=', 'free')
            ->get();
        Notification::send($users, $notification);
    }

    /**
     * Get user's unread notifications
     */
    public function getUnreadNotifications(User $user, int $limit = 10): array
    {
        return $user->unreadNotifications()
            ->take($limit)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at,
                    'read_at' => $notification->read_at,
                ];
            })
            ->toArray();
    }

    /**
     * Get user's recent notifications
     */
    public function getRecentNotifications(User $user, int $limit = 20): array
    {
        return $user->notifications()
            ->take($limit)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at,
                    'read_at' => $notification->read_at,
                    'is_read' => !is_null($notification->read_at),
                ];
            })
            ->toArray();
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(string $notificationId): bool
    {
        $notification = DatabaseNotification::find($notificationId);
        
        if ($notification) {
            $notification->markAsRead();
            return true;
        }
        
        return false;
    }

    /**
     * Mark all user notifications as read
     */
    public function markAllAsRead(User $user): int
    {
        return $user->unreadNotifications()->update(['read_at' => now()]);
    }

    /**
     * Delete notification
     */
    public function deleteNotification(string $notificationId): bool
    {
        $notification = DatabaseNotification::find($notificationId);
        
        if ($notification) {
            $notification->delete();
            return true;
        }
        
        return false;
    }

    /**
     * Clear all user notifications
     */
    public function clearAllNotifications(User $user): int
    {
        return $user->notifications()->delete();
    }

    /**
     * Get notification statistics
     */
    public function getNotificationStats(User $user): array
    {
        return [
            'total' => $user->notifications()->count(),
            'unread' => $user->unreadNotifications()->count(),
            'read' => $user->readNotifications()->count(),
            'today' => $user->notifications()->whereDate('created_at', today())->count(),
            'this_week' => $user->notifications()->whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
        ];
    }

    /**
     * Get user's notification preferences
     */
    public function getNotificationPreferences(User $user): array
    {
        return [
            'email_notifications' => $user->email_notifications ?? true,
            'push_notifications' => $user->push_notifications ?? true,
            'course_updates' => $user->course_updates ?? true,
            'event_reminders' => $user->event_reminders ?? true,
            'achievement_alerts' => $user->achievement_alerts ?? true,
            'marketing_emails' => $user->marketing_emails ?? false,
        ];
    }

    /**
     * Update user's notification preferences
     */
    public function updateNotificationPreferences(User $user, array $preferences): bool
    {
        return $user->update($preferences);
    }

    /**
     * Send system-wide announcement
     */
    public function sendAnnouncement(string $title, string $message, array $options = []): void
    {
        $notificationData = [
            'title' => $title,
            'message' => $message,
            'type' => 'announcement',
            'icon' => $options['icon'] ?? '📢',
            'action_url' => $options['action_url'] ?? null,
            'action_text' => $options['action_text'] ?? null,
        ];

        // Create notification class dynamically
        $notification = new class($notificationData) extends \Illuminate\Notifications\Notification {
            private $data;

            public function __construct($data)
            {
                $this->data = $data;
            }

            public function via($notifiable)
            {
                return ['database'];
            }

            public function toDatabase($notifiable)
            {
                return $this->data;
            }
        };

        // Send to target audience
        if (isset($options['target']) && $options['target'] === 'premium') {
            $this->sendToPremiumUsers($notification);
        } elseif (isset($options['subscription_plan'])) {
            $this->sendToSubscribers($options['subscription_plan'], $notification);
        } else {
            $this->sendToAllUsers($notification);
        }
    }

    /**
     * Schedule notification for later
     */
    public function scheduleNotification(User $user, $notification, \DateTime $when): void
    {
        // This would typically use a queue system like Laravel's job queue
        // For now, we'll just send it immediately
        // In production, you'd use: dispatch(new SendNotificationJob($user, $notification))->delay($when);
        $this->sendToUser($user, $notification);
    }

    /**
     * Get notification templates
     */
    public function getNotificationTemplates(): array
    {
        return [
            'welcome' => [
                'title' => 'Welcome to The Real World!',
                'message' => 'You\'ve successfully joined our exclusive community. Start your journey to financial freedom today.',
                'icon' => '🎉',
            ],
            'course_completed' => [
                'title' => 'Course Completed!',
                'message' => 'Congratulations on completing {course_name}. You\'ve earned {xp} XP!',
                'icon' => '🎓',
            ],
            'level_up' => [
                'title' => 'Level Up!',
                'message' => 'Amazing! You\'ve reached level {level}. Keep up the great work!',
                'icon' => '⭐',
            ],
            'event_reminder' => [
                'title' => 'Event Reminder',
                'message' => '{event_name} starts in {time}. Don\'t miss out!',
                'icon' => '📅',
            ],
            'achievement_unlocked' => [
                'title' => 'Achievement Unlocked!',
                'message' => 'You\'ve earned the "{achievement_name}" achievement and gained {xp} XP!',
                'icon' => '🏆',
            ],
        ];
    }
}
