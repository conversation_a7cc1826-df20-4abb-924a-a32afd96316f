<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Spatie\Permission\Models\Role;

class AssignAdminRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:assign {email? : The email of the user to make admin}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign admin role to a user by email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        // If no email provided, ask for it
        if (!$email) {
            $email = $this->ask('Enter the email address of the user to make admin');
        }

        // Find the user
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return 1;
        }

        // Ensure admin role exists
        $adminRole = Role::firstOrCreate(['name' => 'admin']);

        // Check if user already has admin role
        if ($user->hasRole('admin')) {
            $this->info("User '{$user->name}' ({$email}) already has admin role.");
            return 0;
        }

        // Assign admin role
        $user->assignRole('admin');

        $this->info("Admin role successfully assigned to '{$user->name}' ({$email}).");

        return 0;
    }
}
