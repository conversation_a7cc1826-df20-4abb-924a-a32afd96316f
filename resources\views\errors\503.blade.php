@extends('layouts.app')

@section('title', '503 - Service Unavailable - The Real World')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg w-full text-center">
        <!-- Maintenance Icon -->
        <div class="mb-8">
            <div class="mx-auto w-32 h-32 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center">
                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
        </div>

        <!-- Main Message -->
        <h1 class="text-4xl font-bold text-white mb-4">We'll Be Right Back!</h1>
        
        <p class="text-xl text-gray-300 mb-8 leading-relaxed">
            The Real World is currently undergoing scheduled maintenance to bring you an even better experience.
        </p>

        <!-- Status Information -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="text-center">
                    <div class="text-2xl mb-2">🔧</div>
                    <h3 class="font-semibold text-white mb-1">Maintenance Type</h3>
                    <p class="text-gray-400 text-sm">System Upgrade</p>
                </div>
                
                <div class="text-center">
                    <div class="text-2xl mb-2">⏱️</div>
                    <h3 class="font-semibold text-white mb-1">Expected Duration</h3>
                    <p class="text-gray-400 text-sm">30-60 minutes</p>
                </div>
            </div>
        </div>

        <!-- What We're Working On -->
        <div class="text-left bg-gray-800 rounded-lg p-6 mb-8">
            <h3 class="text-lg font-semibold text-white mb-4 text-center">🚀 What We're Improving</h3>
            <ul class="space-y-3 text-gray-300">
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Enhanced performance and faster loading times
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    New features and improved user experience
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Security updates and bug fixes
                </li>
                <li class="flex items-start">
                    <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Database optimizations for better reliability
                </li>
            </ul>
        </div>

        <!-- Progress Bar -->
        <div class="mb-8">
            <div class="flex justify-between text-sm text-gray-400 mb-2">
                <span>Maintenance Progress</span>
                <span id="progress-text">65%</span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
                <div id="progress-bar" class="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-1000" style="width: 65%"></div>
            </div>
        </div>

        <!-- Estimated Time -->
        <div class="bg-blue-900/20 border border-blue-600 rounded-lg p-4 mb-8">
            <div class="flex items-center justify-center">
                <svg class="h-5 w-5 text-blue-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="text-blue-300">
                    <p class="font-medium">Estimated completion: <span id="completion-time">{{ now()->addMinutes(45)->format('g:i A T') }}</span></p>
                </div>
            </div>
        </div>

        <!-- Stay Updated -->
        <div class="space-y-4">
            <h3 class="text-lg font-semibold text-white">Stay Updated</h3>
            
            <div class="flex justify-center space-x-4">
                <a href="https://twitter.com/therealworldhq" target="_blank" 
                   class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-transparent hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                    Twitter Updates
                </a>
                
                <a href="https://discord.gg/therealworld" target="_blank" 
                   class="inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium rounded-lg text-gray-300 bg-transparent hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                    </svg>
                    Discord
                </a>
            </div>
        </div>

        <!-- Auto Refresh Notice -->
        <div class="mt-8 pt-6 border-t border-gray-700">
            <p class="text-gray-400 text-sm mb-2">
                This page will automatically refresh every 30 seconds
            </p>
            <div class="flex justify-center items-center space-x-2 text-xs text-gray-500">
                <span>Next refresh in:</span>
                <span id="countdown" class="font-mono bg-gray-800 px-2 py-1 rounded">30</span>
                <span>seconds</span>
            </div>
        </div>

        <!-- Emergency Contact -->
        <div class="mt-6">
            <p class="text-gray-400 text-sm">
                Urgent issues? Contact us at 
                <a href="mailto:<EMAIL>" class="text-primary-400 hover:text-primary-300">
                    <EMAIL>
                </a>
            </p>
        </div>
    </div>
</div>

<!-- Background Pattern -->
<div class="fixed inset-0 -z-10">
    <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-black"></div>
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23374151" fill-opacity="0.1"%3E%3Ccircle cx="7" cy="7" r="1"/%3E%3Ccircle cx="27" cy="7" r="1"/%3E%3Ccircle cx="47" cy="7" r="1"/%3E%3Ccircle cx="7" cy="27" r="1"/%3E%3Ccircle cx="27" cy="27" r="1"/%3E%3Ccircle cx="47" cy="27" r="1"/%3E%3Ccircle cx="7" cy="47" r="1"/%3E%3Ccircle cx="27" cy="47" r="1"/%3E%3Ccircle cx="47" cy="47" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
</div>

<script>
// Auto refresh every 30 seconds
let countdown = 30;
const countdownElement = document.getElementById('countdown');
const progressBar = document.getElementById('progress-bar');
const progressText = document.getElementById('progress-text');

// Countdown timer
const countdownTimer = setInterval(() => {
    countdown--;
    countdownElement.textContent = countdown;
    
    if (countdown <= 0) {
        window.location.reload();
    }
}, 1000);

// Simulate progress updates
let progress = 65;
const progressTimer = setInterval(() => {
    if (progress < 95) {
        progress += Math.random() * 2;
        progressBar.style.width = progress + '%';
        progressText.textContent = Math.round(progress) + '%';
    }
}, 5000);

// Update completion time every minute
setInterval(() => {
    const now = new Date();
    const completion = new Date(now.getTime() + (45 * 60000)); // 45 minutes from now
    document.getElementById('completion-time').textContent = completion.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        timeZoneName: 'short'
    });
}, 60000);
</script>
@endsection
