<?php

namespace Tests\Integration;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class ApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_api_requires_authentication(): void
    {
        $response = $this->getJson('/api/user');
        
        $response->assertStatus(401);
    }

    public function test_authenticated_user_can_access_api(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user');
        
        $response->assertStatus(200);
        $response->assertJson([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
        ]);
    }

    public function test_api_rate_limiting(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Make requests up to the limit
        for ($i = 0; $i < 120; $i++) {
            $response = $this->getJson('/api/user');
            $response->assertStatus(200);
        }

        // Next request should be rate limited
        $response = $this->getJson('/api/user');
        $response->assertStatus(429);
    }

    public function test_api_returns_proper_headers(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user');
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/json');
        $response->assertHeader('X-RateLimit-Limit');
        $response->assertHeader('X-RateLimit-Remaining');
    }

    public function test_api_notification_count(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create some notifications
        $user->notifications()->create([
            'id' => \Str::uuid(),
            'type' => 'test',
            'data' => ['message' => 'Test notification'],
            'read_at' => null,
        ]);

        $response = $this->getJson('/api/notifications/count');
        
        $response->assertStatus(200);
        $response->assertJson(['count' => 1]);
    }

    public function test_api_mark_notifications_read(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Create unread notifications
        $user->notifications()->create([
            'id' => \Str::uuid(),
            'type' => 'test',
            'data' => ['message' => 'Test notification'],
            'read_at' => null,
        ]);

        $response = $this->postJson('/api/notifications/mark-all-read');
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        // Verify notifications are marked as read
        $this->assertEquals(0, $user->unreadNotifications()->count());
    }

    public function test_api_chat_rooms(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/chat/rooms');
        
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'id',
                'name',
                'description',
                'is_active',
                'messages_count',
            ]
        ]);
    }

    public function test_api_validation_errors(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Test invalid data
        $response = $this->postJson('/api/profile', [
            'email' => 'invalid-email',
            'name' => '', // Required field
        ]);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['email', 'name']);
    }

    public function test_api_cors_headers(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user', [
            'Origin' => 'https://example.com'
        ]);
        
        $response->assertStatus(200);
        // CORS headers should be present if configured
    }

    public function test_api_error_handling(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // Test non-existent endpoint
        $response = $this->getJson('/api/non-existent-endpoint');
        
        $response->assertStatus(404);
        $response->assertJson([
            'message' => 'Not Found.'
        ]);
    }

    public function test_api_security_headers(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user');
        
        $response->assertStatus(200);
        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-Frame-Options', 'DENY');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
    }

    public function test_api_pagination(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        // This would test paginated endpoints when they exist
        $response = $this->getJson('/api/courses?page=1&per_page=10');
        
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'data',
                'links',
                'meta' => [
                    'current_page',
                    'per_page',
                    'total',
                ]
            ]);
        }
    }

    public function test_api_search_functionality(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/courses?search=test');
        
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'title',
                        'description',
                    ]
                ]
            ]);
        }
    }

    public function test_api_filtering(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/courses?difficulty=beginner');
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            if (!empty($data)) {
                foreach ($data as $course) {
                    $this->assertEquals('beginner', $course['difficulty_level'] ?? null);
                }
            }
        }
    }

    public function test_api_sorting(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/courses?sort=title&order=asc');
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            if (count($data) > 1) {
                $titles = array_column($data, 'title');
                $sortedTitles = $titles;
                sort($sortedTitles);
                $this->assertEquals($sortedTitles, $titles);
            }
        }
    }

    public function test_api_includes_relationships(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->getJson('/api/courses?include=campus,lessons');
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            if (!empty($data)) {
                $course = $data[0];
                // Check if relationships are included
                $this->assertArrayHasKey('campus', $course);
                $this->assertArrayHasKey('lessons', $course);
            }
        }
    }
}
