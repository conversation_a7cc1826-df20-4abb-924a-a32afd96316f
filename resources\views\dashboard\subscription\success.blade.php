@extends('layouts.dashboard')

@section('title', 'Subscription Success - The Real World')
@section('page-title', 'Welcome to The Real World!')

@section('content')
<div class="p-6">
    <div class="max-w-2xl mx-auto">
        <!-- Success Message -->
        <div class="text-center mb-8">
            <div class="w-24 h-24 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold mb-4">🎉 Welcome to The Real World!</h1>
            <p class="text-xl text-gray-300 mb-6">
                Your subscription has been activated successfully. You now have access to all {{ ucfirst($user->subscription_plan) }} plan features.
            </p>
            
            <div class="bg-green-600/20 border border-green-600/30 rounded-lg p-6 mb-8">
                <h3 class="text-lg font-semibold text-green-400 mb-2">🏆 Bonus XP Awarded!</h3>
                <p class="text-green-300">You've earned <strong>100 XP</strong> for subscribing to The Real World. Your journey to success starts now!</p>
            </div>
        </div>

        <!-- What's Next -->
        <div class="card mb-8">
            <h3 class="text-xl font-semibold mb-6">What's Next?</h3>
            
            <div class="space-y-4">
                <div class="flex items-start p-4 bg-gray-700 rounded-lg">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1">
                        <span class="text-white font-bold text-sm">1</span>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">Complete Your Profile</h4>
                        <p class="text-gray-400 text-sm mb-3">Add your bio, location, and goals to connect with like-minded entrepreneurs.</p>
                        <a href="{{ route('dashboard.profile.edit') }}" class="btn-secondary text-sm">Complete Profile</a>
                    </div>
                </div>

                <div class="flex items-start p-4 bg-gray-700 rounded-lg">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1">
                        <span class="text-white font-bold text-sm">2</span>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">Explore Your Campuses</h4>
                        <p class="text-gray-400 text-sm mb-3">Access premium courses and start learning from the best in the business.</p>
                        <a href="{{ route('dashboard.campuses') }}" class="btn-secondary text-sm">Browse Campuses</a>
                    </div>
                </div>

                <div class="flex items-start p-4 bg-gray-700 rounded-lg">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1">
                        <span class="text-white font-bold text-sm">3</span>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">Join the Community</h4>
                        <p class="text-gray-400 text-sm mb-3">Connect with other members in premium chat rooms and share your journey.</p>
                        <a href="{{ route('dashboard.chat') }}" class="btn-secondary text-sm">Join Chat</a>
                    </div>
                </div>

                <div class="flex items-start p-4 bg-gray-700 rounded-lg">
                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center mr-4 mt-1">
                        <span class="text-white font-bold text-sm">4</span>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">Attend Live Events</h4>
                        <p class="text-gray-400 text-sm mb-3">RSVP to exclusive events and learn directly from successful entrepreneurs.</p>
                        <a href="{{ route('dashboard.events.index') }}" class="btn-secondary text-sm">View Events</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Plan Benefits -->
        <div class="card mb-8">
            <h3 class="text-xl font-semibold mb-6">Your {{ ucfirst($user->subscription_plan) }} Plan Benefits</h3>
            
            @php
                $benefits = [
                    'prosper' => [
                        'Access to Prosper courses',
                        'Premium chat rooms',
                        'Priority support',
                        'Monthly live sessions',
                        'Course certificates',
                        'Mobile app access',
                    ],
                    'conquer' => [
                        'All Prosper features',
                        'Access to Conquer courses',
                        'Advanced trading strategies',
                        'Weekly 1-on-1 sessions',
                        'Exclusive events',
                        'Private Discord access',
                        'Advanced analytics',
                    ],
                    'champions' => [
                        'All Conquer features',
                        'Direct access to mentors',
                        'Private Champions chat',
                        'Daily live sessions',
                        'Personal success coaching',
                        'Exclusive networking events',
                        'Priority feature requests',
                        'Custom learning paths',
                    ],
                ];
            @endphp

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                @foreach($benefits[$user->subscription_plan] ?? [] as $benefit)
                <div class="flex items-center p-3 bg-gray-700 rounded-lg">
                    <svg class="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-sm">{{ $benefit }}</span>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Support Information -->
        <div class="card">
            <h3 class="text-xl font-semibold mb-4">Need Help?</h3>
            <p class="text-gray-400 mb-6">
                Our support team is here to help you succeed. As a {{ ucfirst($user->subscription_plan) }} member, you have access to priority support.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a href="{{ route('dashboard.chat') }}" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium">Community Support</h4>
                        <p class="text-sm text-gray-400">Get help from the community</p>
                    </div>
                </a>
                
                <a href="mailto:<EMAIL>" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium">Email Support</h4>
                        <p class="text-sm text-gray-400">Direct support via email</p>
                    </div>
                </a>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex items-center justify-center space-x-4">
            <a href="{{ route('dashboard') }}" class="btn-primary">
                Go to Dashboard
            </a>
            <a href="{{ route('dashboard.campuses') }}" class="btn-secondary">
                Start Learning
            </a>
        </div>
    </div>
</div>
@endsection
