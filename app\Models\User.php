<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Cashier\Billable;
use Spatie\Permission\Traits\HasRoles;
use App\Traits\HasMedia;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, Billable, HasRoles, HasMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar_url',
        'bio',
        'location',
        'social_twitter',
        'social_instagram',
        'social_linkedin',
        'xp',
        'level',
        'subscription_plan',
        'trial_ends_at',
        'is_active',
        'last_activity_at',
        'referral_code',
        'referred_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Relationships
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    public function referrals(): HasMany
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    public function lessonProgress(): HasMany
    {
        return $this->hasMany(UserLessonProgress::class);
    }

    public function userLessonProgress(): HasMany
    {
        return $this->hasMany(UserLessonProgress::class);
    }

    public function completedTasks(): HasMany
    {
        return $this->hasMany(UserTask::class);
    }

    public function userTasks(): HasMany
    {
        return $this->hasMany(UserTask::class);
    }

    public function badges(): BelongsToMany
    {
        return $this->belongsToMany(Badge::class, 'user_badges')
                    ->withPivot(['xp_earned', 'earning_data', 'awarded_at'])
                    ->withTimestamps();
    }

    /**
     * Get user's course enrollments
     */
    public function courseEnrollments()
    {
        return $this->hasMany(CourseEnrollment::class);
    }

    /**
     * Get user's achievements
     */
    public function achievements()
    {
        return $this->hasMany(UserAchievement::class);
    }

    /**
     * Check if user has a specific achievement
     */
    public function hasAchievement(string $type): bool
    {
        return $this->achievements()->where('achievement_type', $type)->exists();
    }

    /**
     * Check if user has a specific badge
     */
    public function hasBadge(string $slug): bool
    {
        return $this->badges()->whereHas('badge', function ($query) use ($slug) {
            $query->where('slug', $slug);
        })->exists();
    }

    public function eventRsvps(): HasMany
    {
        return $this->hasMany(EventRsvp::class);
    }

    public function chatMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function testimonials(): HasMany
    {
        return $this->hasMany(Testimonial::class);
    }

    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Helper Methods
     */
    public function getCurrentLevel(): int
    {
        // Calculate level based on XP (every 100 XP = 1 level)
        return max(1, floor($this->xp / 100) + 1);
    }

    public function getXpForNextLevel(): int
    {
        $currentLevel = $this->getCurrentLevel();
        return ($currentLevel * 100) - $this->xp;
    }

    public function hasActiveSubscription(): bool
    {
        return $this->subscribed() || ($this->trial_ends_at && $this->trial_ends_at->isFuture());
    }

    public function canAccessCampus(Campus $campus): bool
    {
        if (!$campus->is_premium) {
            return true;
        }

        if (!$this->hasActiveSubscription()) {
            return false;
        }

        // Check if user's plan allows access to this campus
        $requiredPlans = $campus->required_plans ?? [];
        if (empty($requiredPlans)) {
            return true;
        }

        return in_array($this->subscription_plan, $requiredPlans);
    }

    public function addXp(int $amount): void
    {
        $this->increment('xp', $amount);

        // Update level if needed
        $newLevel = $this->getCurrentLevel();
        if ($newLevel > $this->level) {
            $this->update(['level' => $newLevel]);
        }
    }

    public function getAvatarUrl(): string
    {
        return $this->avatar_url ?: 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&background=ef4444&color=ffffff';
    }

    /**
     * Get user's recent activities (computed from various sources)
     */
    public function getRecentActivities($limit = 10)
    {
        $activities = collect();

        // Recent lesson completions
        $recentLessons = $this->userLessonProgress()
            ->where('is_completed', true)
            ->with('lesson')
            ->latest('completed_at')
            ->take(5)
            ->get();

        foreach ($recentLessons as $progress) {
            $activities->push([
                'type' => 'lesson_completed',
                'description' => 'Completed lesson: ' . $progress->lesson->title,
                'date' => $progress->completed_at,
                'created_at' => $progress->completed_at, // For compatibility
            ]);
        }

        // Recent event RSVPs
        $recentRsvps = $this->eventRsvps()
            ->with('event')
            ->latest('rsvp_at')
            ->take(5)
            ->get();

        foreach ($recentRsvps as $rsvp) {
            $activities->push([
                'type' => 'event_rsvp',
                'description' => 'RSVP\'d to event: ' . $rsvp->event->title,
                'date' => $rsvp->rsvp_at,
                'created_at' => $rsvp->rsvp_at, // For compatibility
            ]);
        }

        return $activities->sortByDesc('date')->take($limit);
    }
}
