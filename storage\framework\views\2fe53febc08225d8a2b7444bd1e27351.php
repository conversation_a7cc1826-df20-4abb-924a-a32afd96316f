<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', config('app.name', 'The Real World')); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'Join The Real World and learn real-world skills to build wealth and achieve financial freedom.'); ?>">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=montserrat:400,500,600,700,800&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-sans antialiased bg-gray-900 text-white">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800" x-data="{ open: false }" role="navigation" aria-label="Main navigation">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="<?php echo e(route('home')); ?>" class="flex items-center group" aria-label="The Real World - Home">
                        <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                            </svg>
                        </div>
                        <span class="text-2xl font-bold text-gradient font-display group-hover:scale-105 transition-transform duration-200">The Real World</span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="<?php echo e(route('home')); ?>" class="nav-link <?php echo e(request()->routeIs('home') ? 'text-red-400 border-b-2 border-red-400' : 'text-gray-300 hover:text-white hover:border-b-2 hover:border-gray-600'); ?> pb-1 border-b-2 border-transparent transition-all duration-200">
                            Home
                        </a>
                        <a href="<?php echo e(route('campuses')); ?>" class="nav-link <?php echo e(request()->routeIs('campuses*') ? 'text-red-400 border-b-2 border-red-400' : 'text-gray-300 hover:text-white hover:border-b-2 hover:border-gray-600'); ?> pb-1 border-b-2 border-transparent transition-all duration-200">
                            Campuses
                        </a>
                        <a href="<?php echo e(route('pricing')); ?>" class="nav-link <?php echo e(request()->routeIs('pricing') ? 'text-red-400 border-b-2 border-red-400' : 'text-gray-300 hover:text-white hover:border-b-2 hover:border-gray-600'); ?> pb-1 border-b-2 border-transparent transition-all duration-200">
                            Pricing
                        </a>
                        <a href="<?php echo e(route('testimonials')); ?>" class="nav-link <?php echo e(request()->routeIs('testimonials') ? 'text-red-400 border-b-2 border-red-400' : 'text-gray-300 hover:text-white hover:border-b-2 hover:border-gray-600'); ?> pb-1 border-b-2 border-transparent transition-all duration-200">
                            Testimonials
                        </a>
                        <a href="<?php echo e(route('faq')); ?>" class="nav-link <?php echo e(request()->routeIs('faq') ? 'text-red-400 border-b-2 border-red-400' : 'text-gray-300 hover:text-white hover:border-b-2 hover:border-gray-600'); ?> pb-1 border-b-2 border-transparent transition-all duration-200">
                            FAQ
                        </a>
                    </div>
                </div>

                <!-- Auth Buttons -->
                <div class="hidden md:block">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="flex items-center space-x-4">
                            <!-- User Avatar & Dropdown -->
                            <div class="relative" x-data="{ userMenuOpen: false }">
                                <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-3 text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-900 rounded-lg p-2" aria-expanded="false" aria-haspopup="true">
                                    <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-bold text-sm"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                                    </div>
                                    <span class="text-sm font-medium"><?php echo e(auth()->user()->name); ?></span>
                                    <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': userMenuOpen }" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                <!-- Dropdown Menu -->
                                <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 mt-2 w-56 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-2 z-50" style="display: none;">
                                    <div class="px-4 py-2 border-b border-gray-700">
                                        <p class="text-sm font-medium text-white"><?php echo e(auth()->user()->name); ?></p>
                                        <p class="text-xs text-gray-400"><?php echo e(auth()->user()->email); ?></p>
                                    </div>

                                    <a href="<?php echo e(route('dashboard.index')); ?>" class="flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v3H8V5z"></path>
                                        </svg>
                                        Dashboard
                                    </a>

                                    <?php if(auth()->user()->subscription_plan === 'free' || !auth()->user()->subscription_plan): ?>
                                    <a href="<?php echo e(route('pricing')); ?>" class="flex items-center px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 transition-colors duration-200">
                                        <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                        Upgrade Plan
                                    </a>
                                    <?php endif; ?>

                                    <div class="border-t border-gray-700 mt-2 pt-2">
                                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="flex items-center w-full px-4 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                                </svg>
                                                Sign Out
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center space-x-4">
                            <a href="<?php echo e(route('login')); ?>" class="text-gray-300 hover:text-white font-medium transition-colors duration-200">
                                Sign In
                            </a>
                            <a href="<?php echo e(route('register')); ?>" class="btn-primary group">
                                <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                </svg>
                                Join Now
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="open = !open" class="text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-900 rounded-lg p-2 transition-colors duration-200" aria-expanded="false" aria-controls="mobile-menu" aria-label="Toggle navigation menu">
                        <svg class="h-6 w-6 transition-transform duration-200" :class="{ 'rotate-90': open }" stroke="currentColor" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                            <path :class="{'hidden': open, 'inline-flex': !open }" class="inline-flex" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            <path :class="{'hidden': !open, 'inline-flex': open }" class="hidden" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div :class="{'block': open, 'hidden': !open}" class="hidden md:hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-800 border-t border-gray-700">
                <!-- Navigation Links -->
                <a href="<?php echo e(route('home')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('home') ? 'bg-red-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Home
                </a>
                <a href="<?php echo e(route('campuses')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('campuses*') ? 'bg-red-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Campuses
                </a>
                <a href="<?php echo e(route('pricing')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('pricing') ? 'bg-red-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Pricing
                </a>
                <a href="<?php echo e(route('testimonials')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('testimonials') ? 'bg-red-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Testimonials
                </a>
                <a href="<?php echo e(route('faq')); ?>" class="mobile-nav-link <?php echo e(request()->routeIs('faq') ? 'bg-red-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-700'); ?>">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    FAQ
                </a>

                <!-- Auth Section -->
                <div class="border-t border-gray-700 pt-4 mt-4">
                    <?php if(auth()->guard()->check()): ?>
                        <!-- User Info -->
                        <div class="px-3 py-2 mb-3">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold text-sm"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-white"><?php echo e(auth()->user()->name); ?></p>
                                    <p class="text-xs text-gray-400"><?php echo e(auth()->user()->email); ?></p>
                                </div>
                            </div>
                        </div>

                        <a href="<?php echo e(route('dashboard.index')); ?>" class="mobile-nav-link text-gray-300 hover:text-white hover:bg-gray-700">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v3H8V5z"></path>
                            </svg>
                            Dashboard
                        </a>

                        <?php if(auth()->user()->subscription_plan === 'free' || !auth()->user()->subscription_plan): ?>
                        <a href="<?php echo e(route('pricing')); ?>" class="mobile-nav-link text-gray-300 hover:text-white hover:bg-gray-700">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            Upgrade Plan
                        </a>
                        <?php endif; ?>

                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="mobile-nav-link text-gray-300 hover:text-white hover:bg-gray-700 w-full text-left">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Sign Out
                            </button>
                        </form>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="mobile-nav-link text-gray-300 hover:text-white hover:bg-gray-700">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Sign In
                        </a>
                        <a href="<?php echo e(route('register')); ?>" class="mobile-nav-link bg-red-600 text-white hover:bg-red-700">
                            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                            </svg>
                            Join Now
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        <?php echo $__env->yieldContent('content'); ?>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 border-t border-gray-700">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Logo & Description -->
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-2xl font-bold text-gradient font-display mb-4">The Real World</h3>
                    <p class="text-gray-400 mb-4">
                        Join the ultimate online university where you learn real-world skills to build wealth and achieve financial freedom.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Twitter</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Instagram</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.014 5.367 18.647.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.928-.875 2.079-1.365 3.323-1.365s2.395.49 3.323 1.365c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.928.796-2.079 1.297-3.323 1.297zm7.83-9.405c-.49 0-.928-.438-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .49-.438.928-.928.928zm-7.83 1.297c1.297 0 2.343 1.046 2.343 2.343s-1.046 2.343-2.343 2.343-2.343-1.046-2.343-2.343 1.046-2.343 2.343-2.343z" clip-rule="evenodd"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo e(route('campuses')); ?>" class="text-gray-400 hover:text-white">Campuses</a></li>
                        <li><a href="<?php echo e(route('pricing')); ?>" class="text-gray-400 hover:text-white">Pricing</a></li>
                        <li><a href="<?php echo e(route('testimonials')); ?>" class="text-gray-400 hover:text-white">Testimonials</a></li>
                        <li><a href="<?php echo e(route('faq')); ?>" class="text-gray-400 hover:text-white">FAQ</a></li>
                    </ul>
                </div>

                <!-- Legal -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Legal</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo e(route('terms')); ?>" class="text-gray-400 hover:text-white">Terms of Service</a></li>
                        <li><a href="<?php echo e(route('privacy')); ?>" class="text-gray-400 hover:text-white">Privacy Policy</a></li>
                        <li><a href="<?php echo e(route('earnings-disclaimer')); ?>" class="text-gray-400 hover:text-white">Earnings Disclaimer</a></li>
                    </ul>
                </div>
            </div>

            <div class="mt-8 pt-8 border-t border-gray-700">
                <p class="text-center text-gray-400">
                    &copy; <?php echo e(date('Y')); ?> The Real World. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/layouts/app.blade.php ENDPATH**/ ?>