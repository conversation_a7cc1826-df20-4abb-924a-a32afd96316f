@extends('layouts.dashboard')

@section('title', 'Search Results - The Real World')
@section('page-title', 'Search Results')

@section('content')
<div class="p-6">
    <!-- Search Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h2 class="text-2xl font-bold">Search Results</h2>
                @if($query)
                    <p class="text-gray-400">Results for "<span class="text-white font-medium">{{ $query }}</span>"</p>
                @endif
            </div>
        </div>

        <!-- Search Form -->
        <form method="GET" action="{{ route('search') }}" class="mb-6">
            <div class="flex gap-4">
                <div class="flex-1">
                    <input type="text" 
                           name="q" 
                           value="{{ $query }}" 
                           placeholder="Search courses, events, members..."
                           class="input-field">
                </div>
                <select name="type" class="input-field w-auto">
                    <option value="all" {{ request('type') === 'all' ? 'selected' : '' }}>All Content</option>
                    <option value="courses" {{ request('type') === 'courses' ? 'selected' : '' }}>Courses</option>
                    <option value="events" {{ request('type') === 'events' ? 'selected' : '' }}>Events</option>
                    <option value="users" {{ request('type') === 'users' ? 'selected' : '' }}>Members</option>
                </select>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
            </div>
        </form>
    </div>

    @if(empty($query))
        <!-- Search Tips -->
        <div class="card text-center py-12">
            <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold mb-4">Search The Real World</h3>
            <p class="text-gray-400 mb-6">Find courses, events, members, and more across our platform</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl mb-2">📚</div>
                    <h4 class="font-medium mb-1">Courses</h4>
                    <p class="text-sm text-gray-400">Find learning content and tutorials</p>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl mb-2">📅</div>
                    <h4 class="font-medium mb-1">Events</h4>
                    <p class="text-sm text-gray-400">Discover upcoming live sessions</p>
                </div>
                <div class="bg-gray-700 rounded-lg p-4">
                    <div class="text-2xl mb-2">👤</div>
                    <h4 class="font-medium mb-1">Members</h4>
                    <p class="text-sm text-gray-400">Connect with other entrepreneurs</p>
                </div>
            </div>
        </div>
    @elseif(empty($results) || collect($results)->sum('count') === 0)
        <!-- No Results -->
        <div class="card text-center py-12">
            <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <h3 class="text-xl font-semibold mb-4">No results found</h3>
            <p class="text-gray-400 mb-6">We couldn't find anything matching "{{ $query }}"</p>
            
            <div class="space-y-2 text-sm text-gray-400">
                <p>Try:</p>
                <ul class="list-disc list-inside space-y-1">
                    <li>Checking your spelling</li>
                    <li>Using different keywords</li>
                    <li>Searching for more general terms</li>
                    <li>Browsing our campuses instead</li>
                </ul>
            </div>
            
            <div class="mt-6">
                <a href="{{ route('dashboard.campuses') }}" class="btn-primary">
                    Browse Campuses
                </a>
            </div>
        </div>
    @else
        <!-- Search Results -->
        <div class="space-y-8">
            @foreach($results as $section)
                @if($section['count'] > 0)
                    <div class="search-section">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">{{ $section['title'] }} ({{ $section['count'] }})</h3>
                            @if($section['count'] > 5)
                                <a href="{{ route('search') }}?q={{ urlencode($query) }}&type={{ $section['type'] }}" 
                                   class="text-primary-400 hover:text-primary-300 text-sm">
                                    View all {{ $section['title'] }}
                                </a>
                            @endif
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($section['items']->take(6) as $item)
                                <a href="{{ $item['url'] }}" 
                                   class="search-result-card bg-gray-700 rounded-lg p-4 hover:bg-gray-600 transition-colors block">
                                    <div class="flex items-start space-x-3">
                                        @if($item['image'])
                                            <img src="{{ $item['image'] }}" 
                                                 alt="{{ $item['title'] }}"
                                                 class="w-12 h-12 rounded-lg object-cover flex-shrink-0">
                                        @else
                                            <div class="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                @if($section['type'] === 'courses')
                                                    📚
                                                @elseif($section['type'] === 'events')
                                                    📅
                                                @elseif($section['type'] === 'users')
                                                    👤
                                                @else
                                                    📄
                                                @endif
                                            </div>
                                        @endif
                                        
                                        <div class="flex-1 min-w-0">
                                            <h4 class="font-medium text-sm truncate mb-1">{{ $item['title'] }}</h4>
                                            @if($item['description'])
                                                <p class="text-xs text-gray-400 line-clamp-2 mb-1">{{ $item['description'] }}</p>
                                            @endif
                                            @if($item['meta'])
                                                <p class="text-xs text-gray-500">{{ $item['meta'] }}</p>
                                            @endif
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endif
            @endforeach
        </div>
    @endif
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.search-result-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
</style>
@endsection
