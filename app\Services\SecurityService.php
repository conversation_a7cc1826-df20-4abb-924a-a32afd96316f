<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class SecurityService
{
    /**
     * Check if password is compromised
     */
    public function isPasswordCompromised(string $password): bool
    {
        $hash = strtoupper(sha1($password));
        $prefix = substr($hash, 0, 5);
        $suffix = substr($hash, 5);
        
        try {
            $response = file_get_contents("https://api.pwnedpasswords.com/range/{$prefix}");
            return strpos($response, $suffix) !== false;
        } catch (\Exception $e) {
            Log::warning('Failed to check password against breach database', ['error' => $e->getMessage()]);
            return false; // Fail open for availability
        }
    }
    
    /**
     * Generate secure random token
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, array $data = [], ?User $user = null): void
    {
        $logData = [
            'event' => $event,
            'timestamp' => now()->toISOString(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'user_id' => $user?->id,
            'data' => $data,
        ];
        
        Log::channel('security')->info($event, $logData);
    }
    
    /**
     * Check for suspicious login activity
     */
    public function checkSuspiciousActivity(User $user, Request $request): array
    {
        $suspiciousFactors = [];
        
        // Check for unusual IP address
        $recentIps = Cache::get("user_ips:{$user->id}", []);
        $currentIp = $request->ip();
        
        if (!in_array($currentIp, $recentIps) && count($recentIps) > 0) {
            $suspiciousFactors[] = 'new_ip_address';
        }
        
        // Update IP cache
        $recentIps[] = $currentIp;
        $recentIps = array_unique(array_slice($recentIps, -5)); // Keep last 5 IPs
        Cache::put("user_ips:{$user->id}", $recentIps, now()->addDays(30));
        
        // Check for unusual user agent
        $recentUserAgents = Cache::get("user_agents:{$user->id}", []);
        $currentUserAgent = $request->userAgent();
        
        if (!in_array($currentUserAgent, $recentUserAgents) && count($recentUserAgents) > 0) {
            $suspiciousFactors[] = 'new_user_agent';
        }
        
        // Update user agent cache
        $recentUserAgents[] = $currentUserAgent;
        $recentUserAgents = array_unique(array_slice($recentUserAgents, -3)); // Keep last 3 user agents
        Cache::put("user_agents:{$user->id}", $recentUserAgents, now()->addDays(30));
        
        // Check login frequency
        $loginCount = Cache::get("login_count:{$user->id}:" . now()->format('Y-m-d'), 0);
        if ($loginCount > 10) {
            $suspiciousFactors[] = 'excessive_logins';
        }
        
        Cache::increment("login_count:{$user->id}:" . now()->format('Y-m-d'));
        Cache::expire("login_count:{$user->id}:" . now()->format('Y-m-d'), 86400); // 24 hours
        
        return [
            'is_suspicious' => count($suspiciousFactors) > 0,
            'factors' => $suspiciousFactors,
            'risk_level' => $this->calculateRiskLevel($suspiciousFactors),
        ];
    }
    
    /**
     * Calculate risk level based on suspicious factors
     */
    private function calculateRiskLevel(array $factors): string
    {
        $riskScore = 0;
        
        foreach ($factors as $factor) {
            switch ($factor) {
                case 'new_ip_address':
                    $riskScore += 2;
                    break;
                case 'new_user_agent':
                    $riskScore += 1;
                    break;
                case 'excessive_logins':
                    $riskScore += 3;
                    break;
            }
        }
        
        if ($riskScore >= 4) {
            return 'high';
        } elseif ($riskScore >= 2) {
            return 'medium';
        } else {
            return 'low';
        }
    }
    
    /**
     * Sanitize user input
     */
    public function sanitizeInput(string $input): string
    {
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Remove control characters except newlines and tabs
        $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        return $input;
    }
    
    /**
     * Check if request is from a bot
     */
    public function isBotRequest(Request $request): bool
    {
        $userAgent = strtolower($request->userAgent() ?? '');
        
        $botPatterns = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget',
            'python', 'java', 'go-http', 'okhttp', 'apache-httpclient'
        ];
        
        foreach ($botPatterns as $pattern) {
            if (strpos($userAgent, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Generate CSRF token
     */
    public function generateCsrfToken(): string
    {
        return csrf_token();
    }
    
    /**
     * Validate CSRF token
     */
    public function validateCsrfToken(string $token): bool
    {
        return hash_equals(csrf_token(), $token);
    }
    
    /**
     * Encrypt sensitive data
     */
    public function encryptSensitiveData(string $data): string
    {
        return encrypt($data);
    }
    
    /**
     * Decrypt sensitive data
     */
    public function decryptSensitiveData(string $encryptedData): string
    {
        return decrypt($encryptedData);
    }
    
    /**
     * Hash password securely
     */
    public function hashPassword(string $password): string
    {
        return Hash::make($password);
    }
    
    /**
     * Verify password hash
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return Hash::check($password, $hash);
    }
    
    /**
     * Generate secure session ID
     */
    public function generateSessionId(): string
    {
        return $this->generateSecureToken(40);
    }
    
    /**
     * Check for SQL injection patterns
     */
    public function containsSqlInjection(string $input): bool
    {
        $patterns = [
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
            '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
            '/[\'";].*(\bOR\b|\bAND\b)/i',
            '/\b(exec|execute|sp_|xp_)\b/i',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check for XSS patterns
     */
    public function containsXss(string $input): bool
    {
        $patterns = [
            '/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<iframe\b/i',
            '/<object\b/i',
            '/<embed\b/i',
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get security headers
     */
    public function getSecurityHeaders(): array
    {
        return [
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';",
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
        ];
    }
    
    /**
     * Block suspicious IP addresses
     */
    public function isIpBlocked(string $ip): bool
    {
        $blockedIps = Cache::get('blocked_ips', []);
        return in_array($ip, $blockedIps);
    }
    
    /**
     * Block IP address
     */
    public function blockIp(string $ip, int $duration = 3600): void
    {
        $blockedIps = Cache::get('blocked_ips', []);
        $blockedIps[] = $ip;
        Cache::put('blocked_ips', array_unique($blockedIps), $duration);
        
        $this->logSecurityEvent('ip_blocked', ['ip' => $ip, 'duration' => $duration]);
    }
}
