@extends('layouts.admin')

@section('title', 'Create Lesson')
@section('page-title', 'Create Lesson')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Create New Lesson</h1>
            <p class="text-gray-400 mt-1">Add a new lesson to your course</p>
        </div>
        <a href="{{ route('admin.lessons.index') }}" class="btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Lessons
        </a>
    </div>

    <form action="{{ route('admin.lessons.store') }}" method="POST" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        @csrf
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Basic Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="course_id" class="block text-sm font-medium text-gray-300 mb-2">Course *</label>
                            <select name="course_id" id="course_id" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required>
                                <option value="">Select a course</option>
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" {{ old('course_id', request('course_id')) == $course->id ? 'selected' : '' }}>
                                        {{ $course->campus->name }} - {{ $course->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('course_id')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="sort_order" class="block text-sm font-medium text-gray-300 mb-2">Lesson Order</label>
                            <input type="number" name="sort_order" id="sort_order" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('sort_order') }}" min="0" placeholder="1">
                            <p class="mt-1 text-xs text-gray-400">Order within the course (1, 2, 3...)</p>
                            @error('sort_order')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Lesson Title *</label>
                        <input type="text" name="title" id="title" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('title') }}" required placeholder="Enter lesson title">
                        @error('title')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description *</label>
                        <textarea name="description" id="description" rows="3" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" required placeholder="Brief description of what students will learn">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Video Content -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Video Content</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="md:col-span-2">
                            <label for="video_url" class="block text-sm font-medium text-gray-300 mb-2">Video URL</label>
                            <input type="url" name="video_url" id="video_url" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('video_url') }}" placeholder="https://youtube.com/watch?v=...">
                            @error('video_url')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="video_type" class="block text-sm font-medium text-gray-300 mb-2">Video Type</label>
                            <select name="video_type" id="video_type" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                <option value="">Auto-detect</option>
                                <option value="youtube" {{ old('video_type') === 'youtube' ? 'selected' : '' }}>YouTube</option>
                                <option value="vimeo" {{ old('video_type') === 'vimeo' ? 'selected' : '' }}>Vimeo</option>
                                <option value="direct" {{ old('video_type') === 'direct' ? 'selected' : '' }}>Direct Link</option>
                            </select>
                            @error('video_type')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="duration_seconds" class="block text-sm font-medium text-gray-300 mb-2">Duration (seconds)</label>
                        <input type="number" name="duration_seconds" id="duration_seconds" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('duration_seconds') }}" min="1" placeholder="300">
                        <p class="mt-1 text-xs text-gray-400">Video duration in seconds (e.g., 300 for 5 minutes)</p>
                        @error('duration_seconds')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Additional Content -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Additional Content</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-300 mb-2">Lesson Content</label>
                        <textarea name="content" id="content" rows="6" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" placeholder="Additional text content, notes, or instructions">{{ old('content') }}</textarea>
                        @error('content')
                            <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="resource_url" class="block text-sm font-medium text-gray-300 mb-2">Resource URL</label>
                            <input type="url" name="resource_url" id="resource_url" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('resource_url') }}" placeholder="https://example.com/resource.pdf">
                            <p class="mt-1 text-xs text-gray-400">Link to additional resources (PDF, document, etc.)</p>
                            @error('resource_url')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="xp_reward" class="block text-sm font-medium text-gray-300 mb-2">XP Reward</label>
                            <input type="number" name="xp_reward" id="xp_reward" class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent" value="{{ old('xp_reward', 0) }}" min="0" max="1000" placeholder="50">
                            <p class="mt-1 text-xs text-gray-400">XP points awarded for completing this lesson</p>
                            @error('xp_reward')
                                <p class="mt-1 text-sm text-red-400">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Settings</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_active" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Active</span>
                                <p class="text-xs text-gray-400">Lesson is visible and accessible to students</p>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" name="is_preview" id="is_preview" value="1" {{ old('is_preview') ? 'checked' : '' }} class="h-4 w-4 text-primary-600 bg-gray-700 border-gray-600 rounded focus:ring-primary-500 focus:ring-2">
                            <label for="is_preview" class="ml-2 text-sm text-gray-300">
                                <span class="font-medium">Preview Lesson</span>
                                <p class="text-xs text-gray-400">Can be viewed without course enrollment</p>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between">
                <a href="{{ route('admin.lessons.index') }}" class="btn-outline">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Lesson
                </button>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Lesson Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Lesson Preview</h3>
                </div>
                <div class="p-6">
                    <div class="lesson-preview">
                        <div class="flex items-center mb-4">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-lg bg-primary-600 flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h4 class="lesson-title text-white font-medium">Lesson Title</h4>
                                <p class="lesson-course text-sm text-gray-400">Course Name</p>
                            </div>
                        </div>
                        <p class="lesson-description text-gray-300 text-sm mb-4">Lesson description will appear here</p>
                        <div class="flex items-center space-x-2">
                            <span class="lesson-xp inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">0 XP</span>
                            <span class="lesson-order inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Order: 1</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Guidelines -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Guidelines</h3>
                </div>
                <div class="p-6">
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Use clear, descriptive titles
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Keep descriptions concise
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Set appropriate XP rewards
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Order lessons logically
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-400 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Test video links thoroughly
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Live preview updates
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const xpInput = document.getElementById('xp_reward');
    const orderInput = document.getElementById('sort_order');
    const courseSelect = document.getElementById('course_id');

    titleInput.addEventListener('input', function() {
        document.querySelector('.lesson-title').textContent = this.value || 'Lesson Title';
    });

    descriptionInput.addEventListener('input', function() {
        document.querySelector('.lesson-description').textContent = this.value || 'Lesson description will appear here';
    });

    xpInput.addEventListener('input', function() {
        document.querySelector('.lesson-xp').textContent = (this.value || 0) + ' XP';
    });

    orderInput.addEventListener('input', function() {
        document.querySelector('.lesson-order').textContent = 'Order: ' + (this.value || '1');
    });

    courseSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        document.querySelector('.lesson-course').textContent = selectedOption.text || 'Course Name';
    });

    // Auto-detect video type from URL
    const videoUrlInput = document.getElementById('video_url');
    const videoTypeSelect = document.getElementById('video_type');
    
    videoUrlInput.addEventListener('input', function() {
        const url = this.value.toLowerCase();
        
        if (url.includes('youtube.com') || url.includes('youtu.be')) {
            videoTypeSelect.value = 'youtube';
        } else if (url.includes('vimeo.com')) {
            videoTypeSelect.value = 'vimeo';
        } else if (url.includes('.mp4') || url.includes('.webm') || url.includes('.ogg')) {
            videoTypeSelect.value = 'direct';
        }
    });
});
</script>
@endpush
