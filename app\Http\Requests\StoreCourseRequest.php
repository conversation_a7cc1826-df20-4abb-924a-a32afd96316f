<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCourseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->hasRole('admin') || $this->user()->hasR<PERSON>('instructor');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255|unique:courses,title',
            'description' => 'required|string|min:50',
            'campus_id' => 'required|exists:campuses,id',
            'difficulty_level' => 'required|in:beginner,intermediate,advanced',
            'estimated_duration' => 'nullable|integer|min:1|max:1000',
            'learning_objectives' => 'nullable|string|max:2000',
            'prerequisites' => 'nullable|string|max:1000',
            'is_premium' => 'boolean',
            'is_published' => 'boolean',
            'is_featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240', // 10MB max
            'xp_reward' => 'nullable|integer|min:0|max:10000',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Course title is required.',
            'title.unique' => 'A course with this title already exists.',
            'description.required' => 'Course description is required.',
            'description.min' => 'Course description must be at least 50 characters.',
            'campus_id.required' => 'Please select a campus for this course.',
            'campus_id.exists' => 'The selected campus does not exist.',
            'difficulty_level.required' => 'Please select a difficulty level.',
            'difficulty_level.in' => 'Difficulty level must be beginner, intermediate, or advanced.',
            'estimated_duration.integer' => 'Estimated duration must be a number.',
            'estimated_duration.min' => 'Estimated duration must be at least 1 hour.',
            'estimated_duration.max' => 'Estimated duration cannot exceed 1000 hours.',
            'image.image' => 'The uploaded file must be an image.',
            'image.mimes' => 'Image must be a JPEG, PNG, JPG, GIF, or WebP file.',
            'image.max' => 'Image size cannot exceed 10MB.',
            'xp_reward.integer' => 'XP reward must be a number.',
            'xp_reward.min' => 'XP reward cannot be negative.',
            'xp_reward.max' => 'XP reward cannot exceed 10,000.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'campus_id' => 'campus',
            'difficulty_level' => 'difficulty level',
            'estimated_duration' => 'estimated duration',
            'learning_objectives' => 'learning objectives',
            'is_premium' => 'premium status',
            'is_published' => 'published status',
            'is_featured' => 'featured status',
            'xp_reward' => 'XP reward',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_premium' => $this->boolean('is_premium'),
            'is_published' => $this->boolean('is_published'),
            'is_featured' => $this->boolean('is_featured'),
        ]);
    }
}
