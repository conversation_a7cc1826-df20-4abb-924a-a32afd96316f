<?php $__env->startSection('title', 'User Analytics'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-white">User Analytics</h1>
                <p class="text-gray-400 mt-1">Detailed insights into user behavior and engagement</p>
            </div>
            <a href="<?php echo e(route('admin.analytics.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Analytics
            </a>
        </div>
    </div>

    <!-- User Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Total Users -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Total Users</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['total_users'])); ?></p>
                </div>
            </div>
        </div>

        <!-- Verified Users -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Verified Users</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['verified_users'])); ?></p>
                    <p class="text-xs text-gray-400"><?php echo e($stats['total_users'] > 0 ? round(($stats['verified_users'] / $stats['total_users']) * 100, 1) : 0); ?>% of total</p>
                </div>
            </div>
        </div>

        <!-- Premium Users -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-600 rounded-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-400">Premium Users</p>
                    <p class="text-2xl font-bold text-white"><?php echo e(number_format($stats['premium_users'])); ?></p>
                    <p class="text-xs text-gray-400"><?php echo e($stats['total_users'] > 0 ? round(($stats['premium_users'] / $stats['total_users']) * 100, 1) : 0); ?>% of total</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Stats -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Active Today -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Active Today</h3>
                <div class="p-2 bg-green-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2"><?php echo e(number_format($stats['active_today'])); ?></p>
            <p class="text-sm text-gray-400">Users active in last 24h</p>
        </div>

        <!-- Active This Week -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Active This Week</h3>
                <div class="p-2 bg-blue-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2"><?php echo e(number_format($stats['active_this_week'])); ?></p>
            <p class="text-sm text-gray-400">Users active in last 7 days</p>
        </div>

        <!-- Active This Month -->
        <div class="bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-white">Active This Month</h3>
                <div class="p-2 bg-purple-600 rounded-lg">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
            </div>
            <p class="text-3xl font-bold text-white mb-2"><?php echo e(number_format($stats['active_this_month'])); ?></p>
            <p class="text-sm text-gray-400">Users active in last 30 days</p>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- User Registrations by Month -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">User Registrations (Last 12 Months)</h3>
            </div>
            <div class="p-6">
                <?php if($registrationsByMonth->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $registrationsByMonth; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300"><?php echo e(\Carbon\Carbon::createFromFormat('Y-m', $month->month)->format('M Y')); ?></span>
                                <div class="flex items-center">
                                    <div class="w-32 bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="bg-blue-600 h-2 rounded-full" 
                                             style="width: <?php echo e($registrationsByMonth->max('count') > 0 ? ($month->count / $registrationsByMonth->max('count')) * 100 : 0); ?>%"></div>
                                    </div>
                                    <span class="text-white font-medium w-8 text-right"><?php echo e($month->count); ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-400 text-center py-8">No registration data available</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- User Activity by Day -->
        <div class="bg-gray-800 rounded-lg">
            <div class="px-6 py-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white">Activity by Day of Week</h3>
            </div>
            <div class="p-6">
                <?php if($activityByDay->count() > 0): ?>
                    <div class="space-y-4">
                        <?php
                            $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                            $maxCount = $activityByDay->max('count');
                        ?>
                        <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $dayData = $activityByDay->firstWhere('day', $day);
                                $count = $dayData ? $dayData->count : 0;
                            ?>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300 w-20"><?php echo e($day); ?></span>
                                <div class="flex items-center flex-1 ml-4">
                                    <div class="w-full bg-gray-700 rounded-full h-2 mr-3">
                                        <div class="bg-purple-600 h-2 rounded-full" 
                                             style="width: <?php echo e($maxCount > 0 ? ($count / $maxCount) * 100 : 0); ?>%"></div>
                                    </div>
                                    <span class="text-white font-medium w-8 text-right"><?php echo e($count); ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-gray-400 text-center py-8">No activity data available</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/analytics/users.blade.php ENDPATH**/ ?>