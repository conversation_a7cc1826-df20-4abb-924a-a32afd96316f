<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add missing user profile fields
            if (!Schema::hasColumn('users', 'first_name')) {
                $table->string('first_name')->nullable()->after('name');
            }
            
            if (!Schema::hasColumn('users', 'last_name')) {
                $table->string('last_name')->nullable()->after('first_name');
            }
            
            if (!Schema::hasColumn('users', 'phone')) {
                $table->string('phone')->nullable()->after('email');
            }
            
            if (!Schema::hasColumn('users', 'date_of_birth')) {
                $table->date('date_of_birth')->nullable()->after('phone');
            }
            
            if (!Schema::hasColumn('users', 'country')) {
                $table->string('country', 2)->nullable()->after('date_of_birth');
                $table->index('country');
            }
            
            if (!Schema::hasColumn('users', 'bio')) {
                $table->text('bio')->nullable()->after('country');
            }
            
            if (!Schema::hasColumn('users', 'avatar_url')) {
                $table->string('avatar_url')->nullable()->after('bio');
            }
            
            if (!Schema::hasColumn('users', 'xp')) {
                $table->integer('xp')->default(0)->after('avatar_url');
                $table->index('xp');
            }
            
            if (!Schema::hasColumn('users', 'level')) {
                $table->integer('level')->default(1)->after('xp');
                $table->index('level');
            }
            
            if (!Schema::hasColumn('users', 'study_streak')) {
                $table->integer('study_streak')->default(0)->after('level');
            }
            
            if (!Schema::hasColumn('users', 'last_seen_at')) {
                $table->timestamp('last_seen_at')->nullable()->after('study_streak');
                $table->index('last_seen_at');
            }
            
            if (!Schema::hasColumn('users', 'is_public')) {
                $table->boolean('is_public')->default(true)->after('last_seen_at');
                $table->index('is_public');
            }
            
            if (!Schema::hasColumn('users', 'show_progress')) {
                $table->boolean('show_progress')->default(true)->after('is_public');
            }
            
            if (!Schema::hasColumn('users', 'social_links')) {
                $table->json('social_links')->nullable()->after('show_progress');
            }
            
            if (!Schema::hasColumn('users', 'notification_preferences')) {
                $table->json('notification_preferences')->nullable()->after('social_links');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'first_name',
                'last_name', 
                'phone',
                'date_of_birth',
                'country',
                'bio',
                'avatar_url',
                'xp',
                'level',
                'study_streak',
                'last_seen_at',
                'is_public',
                'show_progress',
                'social_links',
                'notification_preferences'
            ]);
        });
    }
};
