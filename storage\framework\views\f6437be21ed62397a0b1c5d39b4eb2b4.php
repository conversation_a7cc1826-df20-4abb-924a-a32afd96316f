<?php $__env->startSection('title', 'Lessons Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Lessons Management</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item active">Lessons</li>
                </ol>
            </nav>
        </div>
        <a href="<?php echo e(route('admin.lessons.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Create Lesson
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('admin.lessons.index')); ?>" class="row g-3">
                <div class="col-md-4">
                    <label for="course" class="form-label">Course</label>
                    <select name="course" id="course" class="form-select">
                        <option value="">All Courses</option>
                        <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($course->id); ?>" <?php echo e(request('course') == $course->id ? 'selected' : ''); ?>>
                                <?php echo e($course->campus->name); ?> - <?php echo e($course->title); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-filter"></i> Filter
                    </button>
                    <a href="<?php echo e(route('admin.lessons.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Lessons Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Lessons (<?php echo e($lessons->total()); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if($lessons->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Course</th>
                                <th>Order</th>
                                <th>Duration</th>
                                <th>XP</th>
                                <th>Status</th>
                                <th>Views</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $lessons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lesson): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?php echo e($lesson->title); ?></strong>
                                        <?php if($lesson->is_preview): ?>
                                            <span class="badge badge-info badge-sm ml-1">Preview</span>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted"><?php echo e(Str::limit($lesson->description, 60)); ?></small>
                                </td>
                                <td>
                                    <div>
                                        <span class="badge" style="background-color: <?php echo e($lesson->course->campus->color); ?>; color: white;">
                                            <?php echo e($lesson->course->campus->name); ?>

                                        </span>
                                    </div>
                                    <small class="text-muted"><?php echo e($lesson->course->title); ?></small>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-secondary"><?php echo e($lesson->sort_order ?? 'N/A'); ?></span>
                                </td>
                                <td class="text-center">
                                    <?php if($lesson->duration_seconds): ?>
                                        <?php echo e(gmdate('i:s', $lesson->duration_seconds)); ?>

                                    <?php else: ?>
                                        <span class="text-muted">N/A</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-success"><?php echo e($lesson->xp_reward ?? 0); ?> XP</span>
                                </td>
                                <td>
                                    <?php if($lesson->is_active): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <div>
                                        <strong><?php echo e($lesson->userProgress()->count()); ?></strong>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo e($lesson->userProgress()->where('is_completed', true)->count()); ?> completed
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo e(route('admin.lessons.show', $lesson)); ?>" class="btn btn-sm btn-outline-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('admin.lessons.edit', $lesson)); ?>" class="btn btn-sm btn-outline-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('admin.lessons.toggle-status', $lesson)); ?>" method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-<?php echo e($lesson->is_active ? 'secondary' : 'success'); ?>" 
                                                    title="<?php echo e($lesson->is_active ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas fa-<?php echo e($lesson->is_active ? 'pause' : 'play'); ?>"></i>
                                            </button>
                                        </form>
                                        <?php if($lesson->userProgress()->count() == 0): ?>
                                        <form action="<?php echo e(route('admin.lessons.destroy', $lesson)); ?>" method="POST" class="d-inline" 
                                              onsubmit="return confirm('Are you sure you want to delete this lesson?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    <?php echo e($lessons->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-video fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No lessons found</h5>
                    <p class="text-muted">Create your first lesson to get started.</p>
                    <a href="<?php echo e(route('admin.lessons.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Lesson
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Auto-submit form when filters change
document.addEventListener('DOMContentLoaded', function() {
    const filters = ['course', 'status'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/lessons/index.blade.php ENDPATH**/ ?>