<?php $__env->startSection('title', 'Event Details - ' . $event->title); ?>
<?php $__env->startSection('page-title', 'Event Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white"><?php echo e($event->title); ?></h1>
            <p class="text-gray-400 mt-1">Event details and management</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.events.edit', $event)); ?>" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Event
            </a>
            <a href="<?php echo e(route('admin.events.index')); ?>" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Events
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Event Overview -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event Overview</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300 leading-relaxed"><?php echo e($event->description); ?></p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Category</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                <?php echo e($event->category === 'workshop' ? 'bg-blue-100 text-blue-800' : 
                                   ($event->category === 'webinar' ? 'bg-green-100 text-green-800' : 
                                   ($event->category === 'networking' ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'))); ?>">
                                <?php echo e(ucfirst($event->category)); ?>

                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            <?php if($event->start_time > now()): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Upcoming
                                </span>
                            <?php elseif($event->end_time > now()): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Live
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Completed
                                </span>
                            <?php endif; ?>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Start Time</label>
                            <p class="text-white"><?php echo e($event->start_time->format('M j, Y g:i A')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">End Time</label>
                            <p class="text-white"><?php echo e($event->end_time->format('M j, Y g:i A')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Duration</label>
                            <p class="text-white"><?php echo e($event->start_time->diffInMinutes($event->end_time)); ?> minutes</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Max Attendees</label>
                            <p class="text-white"><?php echo e($event->max_attendees ?? 'Unlimited'); ?></p>
                        </div>
                    </div>

                    <?php if($event->meeting_url): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Meeting URL</label>
                            <a href="<?php echo e($event->meeting_url); ?>" target="_blank" class="text-primary-400 hover:text-primary-300 break-all">
                                <?php echo e($event->meeting_url); ?>

                            </a>
                        </div>
                    <?php endif; ?>

                    <?php if($event->recording_url): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Recording URL</label>
                            <a href="<?php echo e($event->recording_url); ?>" target="_blank" class="text-primary-400 hover:text-primary-300 break-all">
                                <?php echo e($event->recording_url); ?>

                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Event RSVPs -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event RSVPs</h3>
                
                <?php if($event->rsvps->count() > 0): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Attendee</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">RSVP Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-700">
                                <?php $__currentLoopData = $event->rsvps->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rsvp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-sm font-medium text-white mr-3">
                                                    <?php echo e(substr($rsvp->user->name, 0, 1)); ?>

                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-white"><?php echo e($rsvp->user->name); ?></div>
                                                    <div class="text-sm text-gray-400"><?php echo e($rsvp->user->email); ?></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            <?php echo e($rsvp->created_at->format('M j, Y g:i A')); ?>

                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Confirmed
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <form method="POST" action="<?php echo e(route('admin.events.rsvps.destroy', [$event, $rsvp])); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="text-red-400 hover:text-red-300" 
                                                        onclick="return confirm('Are you sure you want to remove this RSVP?')">
                                                    Remove
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <?php if($event->rsvps->count() > 10): ?>
                        <div class="mt-4 text-center">
                            <a href="<?php echo e(route('admin.events.rsvps', $event)); ?>" class="text-primary-400 hover:text-primary-300">
                                View all <?php echo e($event->rsvps->count()); ?> RSVPs
                            </a>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <div class="text-gray-400">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-300">No RSVPs</h3>
                            <p class="mt-1 text-sm text-gray-400">No one has RSVP'd to this event yet.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Event Stats -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total RSVPs</span>
                        <span class="text-white font-medium"><?php echo e($event->rsvps->count()); ?></span>
                    </div>
                    <?php if($event->max_attendees): ?>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Capacity</span>
                            <span class="text-white font-medium">
                                <?php echo e(round($event->rsvps->count() / $event->max_attendees * 100)); ?>%
                            </span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full" style="width: <?php echo e(min(100, round($event->rsvps->count() / $event->max_attendees * 100))); ?>%"></div>
                        </div>
                    <?php endif; ?>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Created</span>
                        <span class="text-white font-medium"><?php echo e($event->created_at->format('M j, Y')); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Last Updated</span>
                        <span class="text-white font-medium"><?php echo e($event->updated_at->format('M j, Y')); ?></span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <?php if($event->start_time > now()): ?>
                        <button onclick="sendReminders()" class="w-full btn-outline-primary">
                            Send Reminders
                        </button>
                    <?php endif; ?>
                    
                    <?php if($event->meeting_url): ?>
                        <a href="<?php echo e($event->meeting_url); ?>" target="_blank" class="w-full btn-outline-primary">
                            Join Meeting
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo e(route('admin.events.export', $event)); ?>" class="w-full btn-outline-secondary">
                        Export Attendees
                    </a>
                    
                    <form method="POST" action="<?php echo e(route('admin.events.destroy', $event)); ?>" 
                          onsubmit="return confirm('Are you sure you want to delete this event? This action cannot be undone.')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-outline-danger">
                            Delete Event
                        </button>
                    </form>
                </div>
            </div>

            <!-- Event Timeline -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Event Timeline</h3>
                
                <div class="space-y-3">
                    <div class="flex items-center text-sm">
                        <div class="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                        <span class="text-gray-300">Event created</span>
                        <span class="text-gray-400 ml-auto"><?php echo e($event->created_at->format('M j')); ?></span>
                    </div>
                    
                    <?php if($event->start_time > now()): ?>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                            <span class="text-gray-300">Event starts</span>
                            <span class="text-gray-400 ml-auto"><?php echo e($event->start_time->format('M j')); ?></span>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                            <span class="text-gray-300">Event started</span>
                            <span class="text-gray-400 ml-auto"><?php echo e($event->start_time->format('M j')); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($event->end_time <= now()): ?>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-gray-400 rounded-full mr-3"></div>
                            <span class="text-gray-300">Event ended</span>
                            <span class="text-gray-400 ml-auto"><?php echo e($event->end_time->format('M j')); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function sendReminders() {
    if (confirm('Send reminder notifications to all RSVP\'d attendees?')) {
        fetch(`/admin/events/<?php echo e($event->id); ?>/send-reminders`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Reminders sent successfully!');
            } else {
                alert('Failed to send reminders. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to send reminders. Please try again.');
        });
    }
}
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/events/show.blade.php ENDPATH**/ ?>