<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\Cashier\Cashier;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class SubscriptionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        Stripe::set<PERSON><PERSON><PERSON><PERSON>(config('cashier.secret'));
    }

    /**
     * Show subscription plans
     */
    public function plans()
    {
        $plans = [
            'prosper' => [
                'name' => 'Prosper',
                'price_id_monthly' => 'price_prosper_monthly', // Replace with actual Stripe price IDs
                'price_id_yearly' => 'price_prosper_yearly',
                'monthly_price' => 49,
                'yearly_price' => 490,
                'features' => [
                    'Access to 5 campuses',
                    'Basic course library',
                    'Community chat access',
                    'Monthly live events',
                    'Mobile app access',
                ]
            ],
            'conquer' => [
                'name' => 'Conquer',
                'price_id_monthly' => 'price_conquer_monthly',
                'price_id_yearly' => 'price_conquer_yearly',
                'monthly_price' => 99,
                'yearly_price' => 990,
                'features' => [
                    'Access to all campuses',
                    'Complete course library',
                    'Priority chat support',
                    'Weekly live events',
                    'Mobile app access',
                    'Exclusive mentor sessions',
                    'Advanced analytics',
                ]
            ],
            'champions' => [
                'name' => 'Champions',
                'price_id_monthly' => 'price_champions_monthly',
                'price_id_yearly' => 'price_champions_yearly',
                'monthly_price' => 199,
                'yearly_price' => 1990,
                'features' => [
                    'Everything in Conquer',
                    'Direct mentor access',
                    'Daily live sessions',
                    '1-on-1 coaching calls',
                    'Private mastermind group',
                    'Business review sessions',
                    'Priority support',
                ]
            ]
        ];

        return view('subscription.plans', compact('plans'));
    }

    /**
     * Create Stripe Checkout Session
     */
    public function checkout(Request $request)
    {
        $request->validate([
            'plan' => 'required|in:prosper,conquer,champions',
            'billing' => 'required|in:monthly,yearly'
        ]);

        $user = Auth::user();
        $plan = $request->plan;
        $billing = $request->billing;

        // Get price ID based on plan and billing cycle
        $priceId = $this->getPriceId($plan, $billing);

        try {
            $checkout = $user->newSubscription('default', $priceId)
                ->checkout([
                    'success_url' => route('subscription.success') . '?session_id={CHECKOUT_SESSION_ID}',
                    'cancel_url' => route('subscription.cancel'),
                    'metadata' => [
                        'plan' => $plan,
                        'billing' => $billing,
                    ],
                ]);

            return redirect($checkout->url);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Unable to create checkout session. Please try again.']);
        }
    }

    /**
     * Handle successful subscription
     */
    public function success(Request $request)
    {
        $sessionId = $request->get('session_id');

        if ($sessionId) {
            $session = Session::retrieve($sessionId);
            $user = Auth::user();

            // Update user subscription plan
            $plan = $session->metadata->plan ?? 'prosper';
            $user->update(['subscription_plan' => $plan]);

            return view('subscription.success', compact('session', 'plan'));
        }

        return redirect()->route('dashboard.index')->with('success', 'Subscription activated successfully!');
    }

    /**
     * Handle cancelled subscription
     */
    public function cancel()
    {
        return view('subscription.cancel');
    }

    /**
     * Get Stripe price ID
     */
    private function getPriceId($plan, $billing)
    {
        $priceIds = [
            'prosper' => [
                'monthly' => 'price_prosper_monthly',
                'yearly' => 'price_prosper_yearly',
            ],
            'conquer' => [
                'monthly' => 'price_conquer_monthly',
                'yearly' => 'price_conquer_yearly',
            ],
            'champions' => [
                'monthly' => 'price_champions_monthly',
                'yearly' => 'price_champions_yearly',
            ],
        ];

        return $priceIds[$plan][$billing] ?? 'price_prosper_monthly';
    }
}
