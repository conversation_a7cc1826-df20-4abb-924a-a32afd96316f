<?php $__env->startSection('title', $campus->name . ' - Campus Details'); ?>
<?php $__env->startSection('page-title', 'Campus Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Campus Details</h1>
            <p class="text-gray-400 mt-1"><?php echo e($campus->name); ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.campuses.edit', $campus)); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Campus
            </a>
            <a href="<?php echo e(route('admin.campuses.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Campuses
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Campus Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Campus Name</label>
                            <p class="text-white"><?php echo e($campus->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Slug</label>
                            <p class="text-white font-mono"><?php echo e($campus->slug); ?></p>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300"><?php echo e($campus->description); ?></p>
                    </div>

                    <?php if($campus->teaser_description): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Teaser Description</label>
                        <p class="text-gray-300"><?php echo e($campus->teaser_description); ?></p>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            <div class="flex items-center space-x-2">
                                <?php if($campus->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                <?php endif; ?>
                                <?php if($campus->is_premium): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Premium</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Sort Order</label>
                            <p class="text-white"><?php echo e($campus->sort_order ?? 0); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Theme Color</label>
                            <div class="flex items-center space-x-2">
                                <?php if($campus->color): ?>
                                    <div class="w-6 h-6 rounded border border-gray-600" style="background-color: <?php echo e($campus->color); ?>"></div>
                                    <span class="text-white font-mono"><?php echo e($campus->color); ?></span>
                                <?php else: ?>
                                    <span class="text-gray-400">Not set</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if($campus->required_plans && count($campus->required_plans) > 0): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Required Plans</label>
                        <div class="flex flex-wrap gap-2">
                            <?php $__currentLoopData = $campus->required_plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"><?php echo e(ucfirst($plan)); ?></span>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Created</label>
                            <p class="text-white"><?php echo e($campus->created_at->format('M j, Y')); ?></p>
                            <p class="text-xs text-gray-400"><?php echo e($campus->created_at->diffForHumans()); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Last Updated</label>
                            <p class="text-white"><?php echo e($campus->updated_at->format('M j, Y')); ?></p>
                            <p class="text-xs text-gray-400"><?php echo e($campus->updated_at->diffForHumans()); ?></p>
                        </div>
                    </div>

                    <?php if($campus->icon_url): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Campus Icon</label>
                        <div class="flex items-center space-x-3">
                            <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-12 h-12 rounded-lg object-cover">
                            <div>
                                <p class="text-white text-sm"><?php echo e($campus->icon_url); ?></p>
                                <a href="<?php echo e($campus->icon_url); ?>" target="_blank" class="text-primary-400 hover:text-primary-300 text-xs">View full size</a>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Campus Courses -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-white">Campus Courses (<?php echo e($campus->courses_count ?? 0); ?>)</h3>
                        <a href="<?php echo e(route('admin.courses.create', ['campus' => $campus->id])); ?>" class="btn-primary text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Course
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    <?php if($campus->courses && $campus->courses->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $campus->courses->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <h4 class="text-lg font-medium text-white"><?php echo e($course->title); ?></h4>
                                        <p class="text-sm text-gray-400 mt-1"><?php echo e(Str::limit($course->description, 100)); ?></p>
                                        <div class="flex items-center space-x-4 mt-2">
                                            <span class="text-xs text-gray-400"><?php echo e($course->enrollments->count() ?? 0); ?> students</span>
                                            <span class="text-xs text-gray-400"><?php echo e($course->lessons->count() ?? 0); ?> lessons</span>
                                            <?php if($course->is_active): ?>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Active</span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <a href="<?php echo e(route('admin.courses.show', $course)); ?>" class="text-primary-400 hover:text-primary-300">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </a>
                                        <a href="<?php echo e(route('admin.courses.edit', $course)); ?>" class="text-gray-400 hover:text-white">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($campus->courses->count() > 5): ?>
                                <div class="text-center">
                                    <a href="<?php echo e(route('admin.courses.index', ['campus' => $campus->id])); ?>" class="text-primary-400 hover:text-primary-300 text-sm">
                                        View all <?php echo e($campus->courses->count()); ?> courses →
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-white mb-2">No courses yet</h3>
                            <p class="text-gray-400 mb-4">This campus doesn't have any courses yet.</p>
                            <a href="<?php echo e(route('admin.courses.create', ['campus' => $campus->id])); ?>" class="btn-primary">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create First Course
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Campus Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Preview</h3>
                </div>
                <div class="p-6">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <?php if($campus->icon_url): ?>
                                <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-12 h-12 rounded-lg object-cover mr-3">
                            <?php else: ?>
                                <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-3" style="background-color: <?php echo e($campus->color ?? '#3B82F6'); ?>">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                            <?php endif; ?>
                            <div>
                                <h4 class="font-medium text-white"><?php echo e($campus->name); ?></h4>
                                <p class="text-xs text-gray-400"><?php echo e($campus->courses_count ?? 0); ?> courses • <?php echo e($campus->users_count ?? 0); ?> students</p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300"><?php echo e($campus->teaser_description ?? Str::limit($campus->description, 100)); ?></p>
                        <div class="mt-3 flex items-center space-x-2">
                            <?php if($campus->is_active): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                            <?php endif; ?>
                            <?php if($campus->is_premium): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Premium</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-blue-400"><?php echo e($campus->courses_count ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Courses</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400"><?php echo e($campus->users_count ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Students</div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-center mt-4">
                        <div>
                            <div class="text-2xl font-bold text-purple-400"><?php echo e($campus->tasks_count ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Tasks</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-yellow-400"><?php echo e($campus->chatRooms_count ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Chat Rooms</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('admin.campuses.edit', $campus)); ?>" class="w-full btn-primary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Campus
                    </a>
                    
                    <a href="<?php echo e(route('admin.courses.index', ['campus' => $campus->id])); ?>" class="w-full btn-secondary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Manage Courses
                    </a>

                    <a href="<?php echo e(route('admin.courses.create', ['campus' => $campus->id])); ?>" class="w-full btn-secondary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add New Course
                    </a>

                    <?php if($campus->courses_count == 0): ?>
                    <form action="<?php echo e(route('admin.campuses.destroy', $campus)); ?>" method="POST" onsubmit="return confirm('Are you sure you want to delete this campus? This action cannot be undone.')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-outline text-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Campus
                        </button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/campuses/show.blade.php ENDPATH**/ ?>