<?php $__env->startSection('title', 'Campus Details - ' . $campus->name); ?>
<?php $__env->startSection('page-title', 'Campus Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Campus Details</h1>
            <p class="text-gray-400 mt-1"><?php echo e($campus->name); ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.campuses.edit', $campus)); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Campus
            </a>
            <a href="<?php echo e(route('admin.campuses.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Campuses
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Campus Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Campus Name</label>
                            <p class="text-white"><?php echo e($campus->name); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Slug</label>
                            <p class="text-gray-300 font-mono"><?php echo e($campus->slug); ?></p>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300"><?php echo e($campus->description); ?></p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Campus Color</label>
                            <div class="flex items-center space-x-2">
                                <div class="w-6 h-6 rounded border border-gray-600" style="background-color: <?php echo e($campus->color); ?>"></div>
                                <span class="text-gray-300 font-mono"><?php echo e($campus->color); ?></span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Sort Order</label>
                            <p class="text-white"><?php echo e($campus->sort_order ?? 'N/A'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            <div class="flex items-center space-x-2">
                                <?php if($campus->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                <?php endif; ?>
                                <?php if($campus->is_featured): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Featured</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <?php if($campus->location || $campus->timezone): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php if($campus->location): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Location</label>
                            <p class="text-white"><?php echo e($campus->location); ?></p>
                        </div>
                        <?php endif; ?>
                        <?php if($campus->timezone): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Timezone</label>
                            <p class="text-white"><?php echo e($campus->timezone); ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if($campus->features): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Features</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <pre class="text-gray-300 text-sm"><?php echo e(json_encode($campus->features, JSON_PRETTY_PRINT)); ?></pre>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Created</label>
                            <p class="text-gray-300"><?php echo e($campus->created_at->format('M j, Y g:i A')); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Last Updated</label>
                            <p class="text-gray-300"><?php echo e($campus->updated_at->format('M j, Y g:i A')); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campus Media -->
            <?php if($campus->image_url || $campus->icon_url): ?>
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Media</h3>
                </div>
                <div class="p-6 space-y-4">
                    <?php if($campus->image_url): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campus Image</label>
                        <img src="<?php echo e($campus->image_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-full max-w-md rounded-lg">
                    </div>
                    <?php endif; ?>
                    <?php if($campus->icon_url): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campus Icon</label>
                        <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?> Icon" class="w-16 h-16 rounded-lg">
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Campus Courses -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Courses</h3>
                </div>
                <div class="p-6">
                    <?php if($campus->courses()->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <?php $__currentLoopData = $campus->courses()->take(6)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-lg flex items-center justify-center" style="background-color: <?php echo e($campus->color); ?>">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-white truncate"><?php echo e($course->title); ?></p>
                                        <p class="text-sm text-gray-400"><?php echo e($course->enrollments()->count()); ?> students</p>
                                    </div>
                                    <a href="<?php echo e(route('admin.courses.show', $course)); ?>" class="text-primary-400 hover:text-primary-300">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                        
                        <?php if($campus->courses()->count() > 6): ?>
                        <div class="text-center mt-4">
                            <p class="text-sm text-gray-400">Showing 6 of <?php echo e($campus->courses()->count()); ?> courses</p>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-300">No courses yet</h3>
                            <p class="mt-1 text-sm text-gray-500">This campus doesn't have any courses yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Campus Statistics -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center mb-4">
                        <div>
                            <div class="text-2xl font-bold text-primary-400"><?php echo e($campus->users()->count()); ?></div>
                            <div class="text-sm text-gray-400">Total Users</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400"><?php echo e($campus->courses()->count()); ?></div>
                            <div class="text-sm text-gray-400">Courses</div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-blue-400"><?php echo e($campus->courses()->sum(function($course) { return $course->lessons()->count(); })); ?></div>
                            <div class="text-sm text-gray-400">Total Lessons</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-yellow-400"><?php echo e($campus->courses()->sum(function($course) { return $course->enrollments()->count(); })); ?></div>
                            <div class="text-sm text-gray-400">Enrollments</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Campus Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Campus Preview</h3>
                </div>
                <div class="p-6">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 rounded-lg mb-4" style="background-color: <?php echo e($campus->color); ?>">
                            <?php if($campus->icon_url): ?>
                                <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-10 h-10 rounded">
                            <?php else: ?>
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <h4 class="text-white font-medium mb-2"><?php echo e($campus->name); ?></h4>
                        <p class="text-gray-300 text-sm mb-4"><?php echo e($campus->description); ?></p>
                        <?php if($campus->location): ?>
                            <div class="text-xs text-gray-400"><?php echo e($campus->location); ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('admin.campuses.edit', $campus)); ?>" class="w-full btn-primary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Campus
                    </a>
                    <a href="<?php echo e(route('admin.courses.create', ['campus_id' => $campus->id])); ?>" class="w-full btn-outline text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Course
                    </a>
                    <?php if($campus->users()->count() == 0 && $campus->courses()->count() == 0): ?>
                    <form action="<?php echo e(route('admin.campuses.destroy', $campus)); ?>" method="POST" class="w-full" 
                          onsubmit="return confirm('Are you sure you want to delete this campus?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-danger text-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Campus
                        </button>
                    </form>
                    <?php else: ?>
                    <button class="w-full btn-outline text-center opacity-50 cursor-not-allowed" disabled title="Cannot delete campus with users or courses">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Cannot Delete (Has Data)
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/campuses/show.blade.php ENDPATH**/ ?>