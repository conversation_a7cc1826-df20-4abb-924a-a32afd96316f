<?php $__env->startSection('title', 'Campus Details - ' . $campus->name); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Campus Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.campuses.index')); ?>">Campuses</a></li>
                    <li class="breadcrumb-item active"><?php echo e($campus->name); ?></li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?php echo e(route('admin.campuses.edit', $campus)); ?>" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit Campus
            </a>
            <a href="<?php echo e(route('admin.campuses.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Campus Information -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Campus Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Name:</label>
                                <p class="mb-0"><?php echo e($campus->name); ?></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Slug:</label>
                                <p class="mb-0"><code><?php echo e($campus->slug); ?></code></p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Color:</label>
                                <p class="mb-0">
                                    <span class="badge" style="background-color: <?php echo e($campus->color); ?>; color: white;">
                                        <?php echo e($campus->color); ?>

                                    </span>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Sort Order:</label>
                                <p class="mb-0"><?php echo e($campus->sort_order); ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Status:</label>
                                <p class="mb-0">
                                    <?php if($campus->is_active): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Inactive</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Type:</label>
                                <p class="mb-0">
                                    <?php if($campus->is_premium): ?>
                                        <span class="badge badge-warning">Premium</span>
                                    <?php else: ?>
                                        <span class="badge badge-info">Free</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <?php if($campus->is_premium && $campus->required_plans): ?>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Required Plans:</label>
                                <p class="mb-0">
                                    <?php $__currentLoopData = $campus->required_plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge badge-secondary mr-1"><?php echo e(ucfirst($plan)); ?></span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </p>
                            </div>
                            <?php endif; ?>
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Created:</label>
                                <p class="mb-0"><?php echo e($campus->created_at->format('M j, Y g:i A')); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if($campus->icon_url): ?>
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Icon:</label>
                        <div>
                            <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?> Icon" class="img-thumbnail" style="max-width: 100px;">
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Description:</label>
                        <div class="border p-3 rounded bg-light">
                            <?php echo nl2br(e($campus->description)); ?>

                        </div>
                    </div>

                    <?php if($campus->teaser_description): ?>
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Teaser Description:</label>
                        <div class="border p-3 rounded bg-light">
                            <?php echo nl2br(e($campus->teaser_description)); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-primary"><?php echo e($campus->courses_count ?? 0); ?></h4>
                                <small class="text-muted">Courses</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo e($campus->users_count ?? 0); ?></h4>
                            <small class="text-muted">Enrolled Users</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?php echo e(route('admin.courses.index', ['campus' => $campus->id])); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-book"></i> View Courses
                        </a>
                        <a href="<?php echo e(route('admin.events.index', ['campus' => $campus->id])); ?>" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-calendar"></i> View Events
                        </a>
                        <a href="<?php echo e(route('admin.tasks.index', ['campus' => $campus->id])); ?>" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-tasks"></i> View Tasks
                        </a>
                        <?php if($campus->is_active): ?>
                        <a href="<?php echo e(route('campuses.show', $campus->slug)); ?>" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-external-link-alt"></i> View Public Page
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Courses -->
    <?php if($campus->courses->count() > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Courses (<?php echo e($campus->courses->count()); ?>)</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Difficulty</th>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Enrollments</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $campus->courses->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td>
                                <strong><?php echo e($course->title); ?></strong>
                                <?php if($course->is_featured): ?>
                                    <span class="badge badge-warning badge-sm ml-1">Featured</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge badge-<?php echo e($course->difficulty === 'beginner' ? 'success' : ($course->difficulty === 'intermediate' ? 'warning' : 'danger')); ?>">
                                    <?php echo e(ucfirst($course->difficulty)); ?>

                                </span>
                            </td>
                            <td><?php echo e($course->duration_minutes ? $course->duration_minutes . ' min' : 'N/A'); ?></td>
                            <td>
                                <?php if($course->is_active): ?>
                                    <span class="badge badge-success">Active</span>
                                <?php else: ?>
                                    <span class="badge badge-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo e($course->enrollments_count ?? 0); ?></td>
                            <td>
                                <a href="<?php echo e(route('admin.courses.show', $course)); ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?php echo e(route('admin.courses.edit', $course)); ?>" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
                <?php if($campus->courses->count() > 10): ?>
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('admin.courses.index', ['campus' => $campus->id])); ?>" class="btn btn-outline-primary">
                        View All <?php echo e($campus->courses->count()); ?> Courses
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/campuses/show.blade.php ENDPATH**/ ?>