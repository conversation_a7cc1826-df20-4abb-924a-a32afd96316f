<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\UserTask;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class LeaderboardController extends Controller
{
    /**
     * Display the leaderboard
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'all_time'); // all_time, monthly, weekly, daily

        // Get leaderboard data based on period
        switch ($period) {
            case 'daily':
                $leaderboard = $this->getDailyLeaderboard();
                break;
            case 'weekly':
                $leaderboard = $this->getWeeklyLeaderboard();
                break;
            case 'monthly':
                $leaderboard = $this->getMonthlyLeaderboard();
                break;
            default:
                $leaderboard = $this->getAllTimeLeaderboard();
        }

        // Find current user's position
        $userPosition = $leaderboard->search(function($item) use ($user) {
            return $item->id === $user->id;
        });

        $userRank = $userPosition !== false ? $userPosition + 1 : null;

        // Get top performers (top 10)
        $topPerformers = $leaderboard->take(10);

        // Get user's stats for the period
        $userStats = $this->getUserStatsForPeriod($user, $period);

        return view('dashboard.leaderboard.index', compact(
            'topPerformers', 'userRank', 'userStats', 'period', 'user'
        ));
    }

    /**
     * Get all-time leaderboard
     */
    private function getAllTimeLeaderboard()
    {
        return User::select('users.*')
            ->where('is_active', true)
            ->orderBy('xp', 'desc')
            ->orderBy('level', 'desc')
            ->get();
    }

    /**
     * Get monthly leaderboard
     */
    private function getMonthlyLeaderboard()
    {
        $startOfMonth = Carbon::now()->startOfMonth();

        return User::select('users.*')
            ->selectRaw('COALESCE(SUM(tasks.xp_reward), 0) as period_xp')
            ->leftJoin('user_tasks', 'users.id', '=', 'user_tasks.user_id')
            ->leftJoin('tasks', 'user_tasks.task_id', '=', 'tasks.id')
            ->where('users.is_active', true)
            ->where(function($query) use ($startOfMonth) {
                $query->where('user_tasks.completed_at', '>=', $startOfMonth)
                      ->orWhereNull('user_tasks.completed_at');
            })
            ->groupBy('users.id')
            ->orderBy('period_xp', 'desc')
            ->orderBy('users.xp', 'desc')
            ->get();
    }

    /**
     * Get weekly leaderboard
     */
    private function getWeeklyLeaderboard()
    {
        $startOfWeek = Carbon::now()->startOfWeek();

        return User::select('users.*')
            ->selectRaw('COALESCE(SUM(tasks.xp_reward), 0) as period_xp')
            ->leftJoin('user_tasks', 'users.id', '=', 'user_tasks.user_id')
            ->leftJoin('tasks', 'user_tasks.task_id', '=', 'tasks.id')
            ->where('users.is_active', true)
            ->where(function($query) use ($startOfWeek) {
                $query->where('user_tasks.completed_at', '>=', $startOfWeek)
                      ->orWhereNull('user_tasks.completed_at');
            })
            ->groupBy('users.id')
            ->orderBy('period_xp', 'desc')
            ->orderBy('users.xp', 'desc')
            ->get();
    }

    /**
     * Get daily leaderboard
     */
    private function getDailyLeaderboard()
    {
        $today = Carbon::today();

        return User::select('users.*')
            ->selectRaw('COALESCE(SUM(tasks.xp_reward), 0) as period_xp')
            ->leftJoin('user_tasks', 'users.id', '=', 'user_tasks.user_id')
            ->leftJoin('tasks', 'user_tasks.task_id', '=', 'tasks.id')
            ->where('users.is_active', true)
            ->where(function($query) use ($today) {
                $query->whereDate('user_tasks.completed_at', $today)
                      ->orWhereNull('user_tasks.completed_at');
            })
            ->groupBy('users.id')
            ->orderBy('period_xp', 'desc')
            ->orderBy('users.xp', 'desc')
            ->get();
    }

    /**
     * Get user stats for specific period
     */
    private function getUserStatsForPeriod($user, $period)
    {
        $query = UserTask::where('user_id', $user->id)
            ->join('tasks', 'user_tasks.task_id', '=', 'tasks.id');

        switch ($period) {
            case 'daily':
                $query->whereDate('user_tasks.completed_at', Carbon::today());
                break;
            case 'weekly':
                $query->where('user_tasks.completed_at', '>=', Carbon::now()->startOfWeek());
                break;
            case 'monthly':
                $query->where('user_tasks.completed_at', '>=', Carbon::now()->startOfMonth());
                break;
        }

        $periodXp = $query->sum('tasks.xp_reward');
        $tasksCompleted = $query->count();

        return [
            'period_xp' => $periodXp,
            'tasks_completed' => $tasksCompleted,
            'total_xp' => $user->xp,
            'level' => $user->level,
        ];
    }
}
