<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class UserLeveledUp implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $user;
    public $newLevel;
    public $previousLevel;

    /**
     * Create a new event instance.
     */
    public function __construct(User $user, int $newLevel, int $previousLevel)
    {
        $this->user = $user;
        $this->newLevel = $newLevel;
        $this->previousLevel = $previousLevel;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('user.' . $this->user->id),
            new Channel('leaderboard'), // For updating leaderboard in real-time
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'user.leveled-up';
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'user_id' => $this->user->id,
            'user_name' => $this->user->name,
            'new_level' => $this->newLevel,
            'previous_level' => $this->previousLevel,
            'total_xp' => $this->user->xp,
            'message' => "{$this->user->name} reached Level {$this->newLevel}!",
        ];
    }
}
