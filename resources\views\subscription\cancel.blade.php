@extends('layouts.app')

@section('title', 'Subscription Cancelled - The Real World')
@section('description', 'Your subscription process was cancelled. You can try again anytime.')

@section('content')
<section class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Cancel Icon -->
        <div class="w-24 h-24 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
        </div>

        <!-- Cancel Message -->
        <h1 class="text-4xl md:text-5xl font-bold font-display mb-6">
            Subscription <span class="text-yellow-400">Cancelled</span>
        </h1>
        
        <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            No worries! Your subscription process was cancelled and you haven't been charged anything. 
            You can try again anytime when you're ready to join The Real World.
        </p>

        <!-- What Happened -->
        <div class="bg-gray-800 rounded-lg p-8 mb-8 max-w-2xl mx-auto">
            <h2 class="text-2xl font-bold mb-4">What Happened?</h2>
            <p class="text-gray-300 leading-relaxed">
                You cancelled the subscription process before completing the payment. This is completely normal and happens to many people. 
                No charges were made to your payment method.
            </p>
        </div>

        <!-- Why Join -->
        <div class="bg-gray-800 rounded-lg p-8 mb-8 max-w-2xl mx-auto">
            <h2 class="text-2xl font-bold mb-6">Why Students Love The Real World</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Real Results</h3>
                        <p class="text-gray-400 text-sm">Students earning $10k+ monthly</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Expert Mentors</h3>
                        <p class="text-gray-400 text-sm">Learn from successful entrepreneurs</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">24/7 Community</h3>
                        <p class="text-gray-400 text-sm">Connect with like-minded students</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-1">Risk-Free Trial</h3>
                        <p class="text-gray-400 text-sm">7 days free, cancel anytime</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <a href="{{ route('subscription.plans') }}" class="btn-primary text-lg px-8 py-4">
                Try Again
            </a>
            <a href="{{ route('pricing') }}" class="btn-secondary text-lg px-8 py-4">
                View Pricing
            </a>
        </div>

        <!-- Alternative Options -->
        <div class="text-center">
            <p class="text-gray-400 mb-4">
                Still have questions? We're here to help you make the right decision.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('faq') }}" class="text-primary-400 hover:text-primary-300">
                    View FAQ
                </a>
                <span class="hidden sm:inline text-gray-600">•</span>
                <a href="{{ route('testimonials') }}" class="text-primary-400 hover:text-primary-300">
                    Read Success Stories
                </a>
                <span class="hidden sm:inline text-gray-600">•</span>
                <a href="mailto:<EMAIL>" class="text-primary-400 hover:text-primary-300">
                    Contact Support
                </a>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="mt-12">
            <a href="{{ route('home') }}" class="text-gray-400 hover:text-white transition-colors">
                ← Back to Homepage
            </a>
        </div>
    </div>
</section>
@endsection
