<?php

namespace Tests\Unit;

use App\Models\Badge;
use App\Models\Course;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_has_full_name_attribute(): void
    {
        $user = User::factory()->make([
            'first_name' => 'John',
            'last_name' => 'Do<PERSON>',
        ]);

        $this->assertEquals('<PERSON>', $user->full_name);
    }

    public function test_user_has_avatar_url_attribute(): void
    {
        $user = User::factory()->make([
            'name' => '<PERSON>',
            'avatar_url' => null,
        ]);

        $avatarUrl = $user->avatar_url;
        $this->assertStringContainsString('gravatar.com', $avatarUrl);
        $this->assertStringContainsString(md5(strtolower($user->email)), $avatarUrl);
    }

    public function test_user_has_custom_avatar_url(): void
    {
        $customUrl = 'https://example.com/avatar.jpg';
        $user = User::factory()->make([
            'avatar_url' => $customUrl,
        ]);

        $this->assertEquals($customUrl, $user->avatar_url);
    }

    public function test_user_can_have_badges(): void
    {
        $user = User::factory()->create();
        $badge = Badge::factory()->create();

        $user->badges()->attach($badge, ['earned_at' => now()]);

        $this->assertTrue($user->badges->contains($badge));
        $this->assertEquals(1, $user->badges()->count());
    }

    public function test_user_can_be_enrolled_in_courses(): void
    {
        $user = User::factory()->create();
        $course = Course::factory()->create();

        $user->courseEnrollments()->create([
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'progress_percentage' => 0,
        ]);

        $this->assertTrue($user->enrolledCourses->contains($course));
        $this->assertEquals(1, $user->courseEnrollments()->count());
    }

    public function test_user_can_complete_courses(): void
    {
        $user = User::factory()->create();
        $course = Course::factory()->create();

        $enrollment = $user->courseEnrollments()->create([
            'course_id' => $course->id,
            'enrolled_at' => now(),
            'progress_percentage' => 100,
            'completed_at' => now(),
        ]);

        $completedCourses = $user->completedCourses();
        $this->assertTrue($completedCourses->contains($course));
    }

    public function test_user_level_calculation(): void
    {
        $user = User::factory()->create(['xp' => 0]);
        $this->assertEquals(1, $user->level);

        $user->xp = 1000;
        $this->assertEquals(2, $user->level);

        $user->xp = 5000;
        $this->assertEquals(3, $user->level);
    }

    public function test_user_xp_to_next_level(): void
    {
        $user = User::factory()->create(['xp' => 500]);
        
        $xpToNext = $user->xp_to_next_level;
        $this->assertEquals(500, $xpToNext); // 1000 - 500 = 500
    }

    public function test_user_level_progress_percentage(): void
    {
        $user = User::factory()->create(['xp' => 500]);
        
        $progress = $user->level_progress_percentage;
        $this->assertEquals(50, $progress); // 500/1000 * 100 = 50%
    }

    public function test_user_has_active_subscription(): void
    {
        $user = User::factory()->create();
        
        // Mock subscription
        $user->subscriptions()->create([
            'name' => 'default',
            'stripe_id' => 'sub_test',
            'stripe_status' => 'active',
            'stripe_price' => 'price_test',
            'quantity' => 1,
        ]);

        $this->assertTrue($user->hasActiveSubscription());
    }

    public function test_user_without_subscription(): void
    {
        $user = User::factory()->create();
        
        $this->assertFalse($user->hasActiveSubscription());
    }

    public function test_user_notification_preferences_default(): void
    {
        $user = User::factory()->create(['notification_preferences' => null]);
        
        $preferences = $user->notification_preferences;
        $this->assertIsArray($preferences);
        $this->assertTrue($preferences['email_course_updates'] ?? true);
        $this->assertTrue($preferences['email_event_reminders'] ?? true);
    }

    public function test_user_social_links_accessor(): void
    {
        $socialLinks = [
            'twitter' => 'https://twitter.com/johndoe',
            'linkedin' => 'https://linkedin.com/in/johndoe',
        ];
        
        $user = User::factory()->create(['social_links' => $socialLinks]);
        
        $this->assertEquals($socialLinks, $user->social_links);
    }

    public function test_user_can_earn_xp(): void
    {
        $user = User::factory()->create(['xp' => 100]);
        
        $user->earnXp(50);
        
        $this->assertEquals(150, $user->fresh()->xp);
    }

    public function test_user_study_streak_increment(): void
    {
        $user = User::factory()->create(['study_streak' => 5]);
        
        $user->incrementStudyStreak();
        
        $this->assertEquals(6, $user->fresh()->study_streak);
    }

    public function test_user_study_streak_reset(): void
    {
        $user = User::factory()->create(['study_streak' => 10]);
        
        $user->resetStudyStreak();
        
        $this->assertEquals(0, $user->fresh()->study_streak);
    }

    public function test_user_last_seen_update(): void
    {
        $user = User::factory()->create(['last_seen_at' => null]);
        
        $user->updateLastSeen();
        
        $this->assertNotNull($user->fresh()->last_seen_at);
    }

    public function test_user_is_online(): void
    {
        $user = User::factory()->create(['last_seen_at' => now()]);
        
        $this->assertTrue($user->isOnline());
        
        $user->last_seen_at = now()->subMinutes(10);
        $this->assertFalse($user->isOnline());
    }

    public function test_user_can_access_premium_content(): void
    {
        $user = User::factory()->create();
        
        // Without subscription
        $this->assertFalse($user->canAccessPremiumContent());
        
        // With active subscription
        $user->subscriptions()->create([
            'name' => 'default',
            'stripe_id' => 'sub_test',
            'stripe_status' => 'active',
            'stripe_price' => 'price_test',
            'quantity' => 1,
        ]);
        
        $this->assertTrue($user->canAccessPremiumContent());
    }
}
