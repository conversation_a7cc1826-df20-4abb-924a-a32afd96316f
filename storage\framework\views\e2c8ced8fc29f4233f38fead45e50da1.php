<?php $__env->startSection('title', $task->title . ' - Task Details'); ?>
<?php $__env->startSection('page-title', 'Task Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">Task Details</h1>
            <p class="text-gray-400 mt-1"><?php echo e($task->title); ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('admin.tasks.edit', $task)); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Task
            </a>
            <a href="<?php echo e(route('admin.tasks.index')); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Tasks
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Task Information -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Information</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Task Title</label>
                            <p class="text-white"><?php echo e($task->title); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Campus</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"><?php echo e($task->campus->name); ?></span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300"><?php echo e($task->description); ?></p>
                    </div>

                    <?php if($task->instructions): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Instructions</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <p class="text-gray-300"><?php echo e($task->instructions); ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Task Type</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"><?php echo e(ucfirst($task->task_type)); ?></span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Difficulty</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?php echo e($task->difficulty === 'easy' ? 'green' : ($task->difficulty === 'medium' ? 'yellow' : 'red')); ?>-100 text-<?php echo e($task->difficulty === 'easy' ? 'green' : ($task->difficulty === 'medium' ? 'yellow' : 'red')); ?>-800"><?php echo e(ucfirst($task->difficulty)); ?></span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">XP Reward</label>
                            <div class="flex items-center">
                                <svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-white font-medium"><?php echo e($task->xp_reward); ?></span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Sort Order</label>
                            <p class="text-white"><?php echo e($task->sort_order ?? 0); ?></p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            <div class="flex items-center space-x-2">
                                <?php if($task->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                <?php endif; ?>
                                <?php if($task->is_daily): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Daily</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Created</label>
                            <p class="text-white"><?php echo e($task->created_at->format('M j, Y')); ?></p>
                            <p class="text-xs text-gray-400"><?php echo e($task->created_at->diffForHumans()); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Last Updated</label>
                            <p class="text-white"><?php echo e($task->updated_at->format('M j, Y')); ?></p>
                            <p class="text-xs text-gray-400"><?php echo e($task->updated_at->diffForHumans()); ?></p>
                        </div>
                    </div>

                    <?php if($task->task_data && count($task->task_data) > 0): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Task Data</label>
                        <div class="bg-gray-700 rounded-lg p-4">
                            <pre class="text-sm text-gray-300 whitespace-pre-wrap"><?php echo e(json_encode($task->task_data, JSON_PRETTY_PRINT)); ?></pre>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Completions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Recent Completions</h3>
                </div>
                <div class="p-6">
                    <?php if($recentCompletions && $recentCompletions->count() > 0): ?>
                        <div class="space-y-4">
                            <?php $__currentLoopData = $recentCompletions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $completion): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-white font-bold text-sm"><?php echo e(substr($completion->user->name, 0, 1)); ?></span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-white"><?php echo e($completion->user->name); ?></p>
                                            <p class="text-xs text-gray-400"><?php echo e($completion->user->email); ?></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="flex items-center text-yellow-400">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                            </svg>
                                            <span class="text-sm font-medium"><?php echo e($completion->xp_earned ?? $task->xp_reward); ?></span>
                                        </div>
                                        <p class="text-xs text-gray-400"><?php echo e($completion->completed_at ? $completion->completed_at->diffForHumans() : 'Recently'); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            <h3 class="text-lg font-medium text-white mb-2">No completions yet</h3>
                            <p class="text-gray-400">This task hasn't been completed by any users yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Task Preview -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Task Preview</h3>
                </div>
                <div class="p-6">
                    <div class="bg-gray-700 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="w-12 h-12 rounded-lg bg-<?php echo e($task->difficulty === 'easy' ? 'green' : ($task->difficulty === 'medium' ? 'yellow' : 'red')); ?>-600 flex items-center justify-center mr-3">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium text-white"><?php echo e($task->title); ?></h4>
                                <p class="text-xs text-gray-400"><?php echo e($task->campus->name); ?> • <?php echo e(ucfirst($task->difficulty)); ?></p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-3"><?php echo e(Str::limit($task->description, 100)); ?></p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-yellow-400">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                                <span class="text-sm font-medium"><?php echo e($task->xp_reward); ?> XP</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <?php if($task->is_active): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Active</span>
                                <?php else: ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">Inactive</span>
                                <?php endif; ?>
                                <?php if($task->is_daily): ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Daily</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Statistics</h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-blue-400"><?php echo e($stats['total_completions'] ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Total Completions</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-green-400"><?php echo e($stats['unique_users'] ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Unique Users</div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 gap-4 text-center mt-4">
                        <div>
                            <div class="text-2xl font-bold text-purple-400"><?php echo e($stats['completions_today'] ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Completions Today</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-2 gap-4 text-center mt-4">
                        <div>
                            <div class="text-2xl font-bold text-yellow-400"><?php echo e($stats['completions_this_week'] ?? 0); ?></div>
                            <div class="text-sm text-gray-400">This Week</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-red-400"><?php echo e($stats['completions_this_month'] ?? 0); ?></div>
                            <div class="text-sm text-gray-400">This Month</div>
                        </div>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-orange-400"><?php echo e($stats['total_xp_awarded'] ?? 0); ?></div>
                            <div class="text-sm text-gray-400">Total XP Awarded</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg">
                <div class="px-6 py-4 border-b border-gray-700">
                    <h3 class="text-lg font-semibold text-white">Quick Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <a href="<?php echo e(route('admin.tasks.edit', $task)); ?>" class="w-full btn-primary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Task
                    </a>
                    
                    <form action="<?php echo e(route('admin.tasks.toggle-status', $task)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>
                        <button type="submit" class="w-full <?php echo e($task->is_active ? 'btn-outline' : 'btn-secondary'); ?> text-center">
                            <?php if($task->is_active): ?>
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                </svg>
                                Deactivate Task
                            <?php else: ?>
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Activate Task
                            </button>
                            <?php endif; ?>
                        </button>
                    </form>

                    <a href="<?php echo e(route('admin.campuses.show', $task->campus)); ?>" class="w-full btn-secondary text-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        View Campus
                    </a>

                    <form action="<?php echo e(route('admin.tasks.destroy', $task)); ?>" method="POST" onsubmit="return confirm('Are you sure you want to delete this task? This action cannot be undone.')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="w-full btn-outline text-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete Task
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/admin/tasks/show.blade.php ENDPATH**/ ?>