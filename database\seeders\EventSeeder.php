<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Event;
use App\Models\Campus;
use Carbon\Carbon;

class EventSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get campuses
        $ecommerceCampus = Campus::where('slug', 'ecommerce')->first();
        $freelancingCampus = Campus::where('slug', 'freelancing')->first();
        $cryptoCampus = Campus::where('slug', 'cryptocurrency')->first();

        $events = [
            // Upcoming Events
            [
                'campus_id' => $ecommerceCampus?->id ?? 1,
                'title' => 'E-commerce Mastery Workshop',
                'slug' => 'ecommerce-mastery-workshop',
                'description' => 'Join <PERSON> for an exclusive workshop on building profitable e-commerce businesses. Learn the strategies that generate millions in revenue.',
                'category' => 'workshop',
                'thumbnail_url' => 'https://via.placeholder.com/400x200/ef4444/ffffff?text=E-commerce+Workshop',
                'start_time' => Carbon::now()->addDays(2)->setTime(19, 0), // 7 PM in 2 days
                'end_time' => Carbon::now()->addDays(2)->setTime(21, 0), // 9 PM in 2 days
                'video_url' => 'https://zoom.us/j/example123',
                'meeting_url' => 'https://zoom.us/j/example123',
                'status' => 'scheduled',
                'max_attendees' => 500,
                'is_premium' => true,
                'is_published' => true,
                'required_plans' => ['conquer', 'champions'],
            ],
            [
                'campus_id' => $freelancingCampus?->id ?? 2,
                'title' => 'Copywriting Secrets Live Session',
                'slug' => 'copywriting-secrets-live-session',
                'description' => 'Discover the copywriting techniques that convert prospects into paying customers. Interactive Q&A session included.',
                'category' => 'live-session',
                'thumbnail_url' => 'https://via.placeholder.com/400x200/3b82f6/ffffff?text=Copywriting+Session',
                'start_time' => Carbon::now()->addDays(5)->setTime(18, 0), // 6 PM in 5 days
                'end_time' => Carbon::now()->addDays(5)->setTime(19, 30), // 7:30 PM in 5 days
                'video_url' => 'https://zoom.us/j/example456',
                'meeting_url' => 'https://zoom.us/j/example456',
                'status' => 'scheduled',
                'max_attendees' => 200,
                'is_premium' => false,
                'is_published' => true,
                'required_plans' => ['prosper', 'conquer', 'champions'],
            ],
            [
                'campus_id' => $cryptoCampus?->id ?? 3,
                'title' => 'Crypto Trading Masterclass',
                'slug' => 'crypto-trading-masterclass',
                'description' => 'Learn advanced crypto trading strategies from professional traders. Technical analysis, risk management, and portfolio optimization.',
                'category' => 'masterclass',
                'thumbnail_url' => 'https://via.placeholder.com/400x200/10b981/ffffff?text=Crypto+Masterclass',
                'start_time' => Carbon::now()->addWeek()->setTime(20, 0), // 8 PM next week
                'end_time' => Carbon::now()->addWeek()->setTime(22, 0), // 10 PM next week
                'video_url' => 'https://zoom.us/j/example789',
                'meeting_url' => 'https://zoom.us/j/example789',
                'status' => 'scheduled',
                'max_attendees' => 300,
                'is_premium' => true,
                'is_published' => true,
                'required_plans' => ['conquer', 'champions'],
            ],

            // Today's Event (Live Soon)
            [
                'campus_id' => $ecommerceCampus?->id ?? 1,
                'title' => 'Daily Motivation & Q&A',
                'slug' => 'daily-motivation-qa',
                'description' => 'Start your day with motivation and get your questions answered by successful entrepreneurs.',
                'category' => 'daily',
                'thumbnail_url' => 'https://via.placeholder.com/400x200/f59e0b/ffffff?text=Daily+Motivation',
                'start_time' => Carbon::now()->addHours(2), // 2 hours from now
                'end_time' => Carbon::now()->addHours(3), // 3 hours from now
                'video_url' => 'https://zoom.us/j/daily123',
                'meeting_url' => 'https://zoom.us/j/daily123',
                'status' => 'scheduled',
                'max_attendees' => 1000,
                'is_premium' => false,
                'is_published' => true,
                'required_plans' => null, // Free for all
            ],

            // Past Event
            [
                'campus_id' => $freelancingCampus?->id ?? 2,
                'title' => 'Building Your Personal Brand',
                'slug' => 'building-personal-brand',
                'description' => 'How to build a powerful personal brand that attracts clients and opportunities.',
                'category' => 'workshop',
                'thumbnail_url' => 'https://via.placeholder.com/400x200/8b5cf6/ffffff?text=Personal+Brand',
                'start_time' => Carbon::now()->subDays(3)->setTime(19, 0), // 3 days ago
                'end_time' => Carbon::now()->subDays(3)->setTime(21, 0), // 3 days ago
                'recording_url' => 'https://youtube.com/watch?v=example',
                'status' => 'completed',
                'max_attendees' => 250,
                'is_premium' => false,
                'is_published' => true,
                'required_plans' => ['prosper', 'conquer', 'champions'],
            ],
        ];

        foreach ($events as $eventData) {
            Event::create($eventData);
        }
    }
}
