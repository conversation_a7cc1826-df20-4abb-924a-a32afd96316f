<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;

class UserSubscriptionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $users = User::all();
        
        if ($users->count() < 2) {
            $this->command->info('Not enough users to create subscriptions. Skipping...');
            return;
        }

        // Create some premium subscriptions for existing users
        $premiumUsers = $users->random(min(2, $users->count()));
        
        foreach ($premiumUsers as $user) {
            UserSubscription::create([
                'user_id' => $user->id,
                'plan_name' => collect(['prosper', 'conquer', 'champions'])->random(),
                'billing_cycle' => collect(['monthly', 'yearly'])->random(),
                'amount' => collect([49.00, 99.00, 199.00])->random(),
                'currency' => 'USD',
                'status' => 'active',
                'started_at' => Carbon::now()->subDays(rand(1, 30)),
                'expires_at' => Carbon::now()->addDays(rand(30, 365)),
                'features' => json_encode([
                    'access_all_campuses',
                    'priority_support',
                    'exclusive_content',
                    'live_events'
                ]),
                'campus_access' => json_encode([1, 2, 3, 4, 5, 6, 7]), // All campuses
            ]);
        }

        // Create some expired subscriptions
        $expiredUsers = $users->whereNotIn('id', $premiumUsers->pluck('id'))->random(min(1, $users->count() - $premiumUsers->count()));
        
        foreach ($expiredUsers as $user) {
            UserSubscription::create([
                'user_id' => $user->id,
                'plan_name' => 'prosper',
                'billing_cycle' => 'monthly',
                'amount' => 49.00,
                'currency' => 'USD',
                'status' => 'expired',
                'started_at' => Carbon::now()->subDays(60),
                'expires_at' => Carbon::now()->subDays(5),
                'cancelled_at' => Carbon::now()->subDays(5),
                'features' => json_encode([
                    'basic_access',
                    'standard_support'
                ]),
                'campus_access' => json_encode([1, 2]), // Limited campuses
            ]);
        }

        $this->command->info('User subscriptions seeded successfully!');
    }
}
