@extends('layouts.admin')

@section('title', 'Course Details - ' . $course->title)
@section('page-title', 'Course Details')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-white">{{ $course->title }}</h1>
            <p class="text-gray-400 mt-1">Course details and management</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.courses.edit', $course) }}" class="btn-primary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Course
            </a>
            <a href="{{ route('admin.courses.index') }}" class="btn-secondary">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Courses
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Course Overview -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Course Overview</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-1">Description</label>
                        <p class="text-gray-300 leading-relaxed">{{ $course->description }}</p>
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Campus</label>
                            <p class="text-white">{{ $course->campus->name ?? 'No Campus' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Difficulty Level</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                {{ $course->difficulty_level === 'beginner' ? 'bg-green-100 text-green-800' : 
                                   ($course->difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                {{ ucfirst($course->difficulty_level) }}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Estimated Duration</label>
                            <p class="text-white">{{ $course->estimated_duration ?? 'Not specified' }} hours</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                            @if($course->is_published)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Published
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Draft
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Course Lessons -->
            <div class="bg-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Course Lessons</h3>
                    <a href="{{ route('admin.lessons.create', ['course' => $course->id]) }}" class="btn-outline-primary">
                        Add Lesson
                    </a>
                </div>

                @if($course->lessons->count() > 0)
                    <div class="space-y-3">
                        @foreach($course->lessons as $lesson)
                            <div class="bg-gray-700 rounded-lg p-4 flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-sm font-medium text-white">
                                        {{ $lesson->order }}
                                    </div>
                                    <div>
                                        <h4 class="text-white font-medium">{{ $lesson->title }}</h4>
                                        <p class="text-gray-400 text-sm">{{ $lesson->duration ?? 'No duration set' }}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('admin.lessons.edit', $lesson) }}" class="text-blue-400 hover:text-blue-300 text-sm">
                                        Edit
                                    </a>
                                    <form method="POST" action="{{ route('admin.lessons.destroy', $lesson) }}" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-400 hover:text-red-300 text-sm" 
                                                onclick="return confirm('Are you sure you want to delete this lesson?')">
                                            Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="text-gray-400">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-300">No lessons</h3>
                            <p class="mt-1 text-sm text-gray-400">Get started by adding the first lesson to this course.</p>
                            <div class="mt-6">
                                <a href="{{ route('admin.lessons.create', ['course' => $course->id]) }}" class="btn-primary">
                                    Add First Lesson
                                </a>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Enrolled Students -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Enrolled Students</h3>
                
                @if($course->enrollments->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-700">
                            <thead>
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Student</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Progress</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Enrolled</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Last Activity</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-700">
                                @foreach($course->enrollments->take(10) as $enrollment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-sm font-medium text-white mr-3">
                                                    {{ substr($enrollment->user->name, 0, 1) }}
                                                </div>
                                                <div>
                                                    <div class="text-sm font-medium text-white">{{ $enrollment->user->name }}</div>
                                                    <div class="text-sm text-gray-400">{{ $enrollment->user->email }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-16 bg-gray-700 rounded-full h-2 mr-2">
                                                    <div class="bg-primary-600 h-2 rounded-full" style="width: {{ $enrollment->progress_percentage }}%"></div>
                                                </div>
                                                <span class="text-sm text-white">{{ $enrollment->progress_percentage }}%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            {{ $enrollment->created_at->format('M j, Y') }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            {{ $enrollment->last_accessed_at?->diffForHumans() ?? 'Never' }}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($course->enrollments->count() > 10)
                        <div class="mt-4 text-center">
                            <a href="{{ route('admin.courses.enrollments', $course) }}" class="text-primary-400 hover:text-primary-300">
                                View all {{ $course->enrollments->count() }} enrollments
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-8">
                        <div class="text-gray-400">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-300">No enrollments</h3>
                            <p class="mt-1 text-sm text-gray-400">No students have enrolled in this course yet.</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Course Stats -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Course Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total Enrollments</span>
                        <span class="text-white font-medium">{{ $course->enrollments->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Completion Rate</span>
                        <span class="text-white font-medium">
                            {{ $course->enrollments->count() > 0 ? round($course->enrollments->where('progress_percentage', 100)->count() / $course->enrollments->count() * 100) : 0 }}%
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total Lessons</span>
                        <span class="text-white font-medium">{{ $course->lessons->count() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Created</span>
                        <span class="text-white font-medium">{{ $course->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Last Updated</span>
                        <span class="text-white font-medium">{{ $course->updated_at->format('M j, Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-gray-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.lessons.create', ['course' => $course->id]) }}" class="w-full btn-outline-primary">
                        Add New Lesson
                    </a>
                    
                    @if($course->is_published)
                        <form method="POST" action="{{ route('admin.courses.unpublish', $course) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full btn-outline-secondary">
                                Unpublish Course
                            </button>
                        </form>
                    @else
                        <form method="POST" action="{{ route('admin.courses.publish', $course) }}">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="w-full btn-outline-primary">
                                Publish Course
                            </button>
                        </form>
                    @endif
                    
                    <form method="POST" action="{{ route('admin.courses.destroy', $course) }}" 
                          onsubmit="return confirm('Are you sure you want to delete this course? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="w-full btn-outline-danger">
                            Delete Course
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
