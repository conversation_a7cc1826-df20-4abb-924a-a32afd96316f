<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Testimonial;
use App\Models\User;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a user if none exists
        $user = User::first();
        if (!$user) {
            $user = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]);
        }

        $testimonials = [
            [
                'user_id' => $user->id,
                'name' => '<PERSON>',
                'income_display' => '$15,000/month',
                'quote' => 'The Real World changed my life completely. I went from working a dead-end job to making more in a month than I used to make in a year.',
                'location' => 'United States',
                'business_type' => 'E-commerce',
                'status' => 'approved',
                'is_featured' => true,
                'sort_order' => 1,
                'approved_at' => now(),
            ],
            [
                'user_id' => $user->id,
                'name' => '<PERSON>',
                'income_display' => '$8,500/month',
                'quote' => 'I started with zero knowledge about freelancing. Now I run a successful copywriting agency with multiple clients.',
                'location' => 'Canada',
                'business_type' => 'Freelancing',
                'status' => 'approved',
                'is_featured' => true,
                'sort_order' => 2,
                'approved_at' => now(),
            ],
            [
                'user_id' => $user->id,
                'name' => 'Ahmed Hassan',
                'income_display' => '$25,000/month',
                'quote' => 'The cryptocurrency campus taught me everything I needed to know. My portfolio has grown 400% in just 6 months.',
                'location' => 'United Kingdom',
                'business_type' => 'Cryptocurrency',
                'status' => 'approved',
                'is_featured' => true,
                'sort_order' => 3,
                'approved_at' => now(),
            ],
            [
                'user_id' => $user->id,
                'name' => 'Isabella Rodriguez',
                'income_display' => '$12,000/month',
                'quote' => 'Real estate seemed impossible until I joined The Real World. Now I own 3 rental properties and they pay for my lifestyle.',
                'location' => 'Spain',
                'business_type' => 'Real Estate',
                'status' => 'approved',
                'is_featured' => true,
                'sort_order' => 4,
                'approved_at' => now(),
            ],
            [
                'user_id' => $user->id,
                'name' => 'David Kim',
                'income_display' => '$18,000/month',
                'quote' => 'AI automation is the future and The Real World showed me how to capitalize on it. My business runs itself now.',
                'location' => 'South Korea',
                'business_type' => 'AI & Automation',
                'status' => 'approved',
                'is_featured' => true,
                'sort_order' => 5,
                'approved_at' => now(),
            ],
            [
                'user_id' => $user->id,
                'name' => 'Emma Thompson',
                'income_display' => '$6,800/month',
                'quote' => 'As a single mom, I needed something that worked. The Real World gave me the skills to build a business around my schedule.',
                'location' => 'Australia',
                'business_type' => 'E-commerce',
                'status' => 'approved',
                'is_featured' => true,
                'sort_order' => 6,
                'approved_at' => now(),
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
}
