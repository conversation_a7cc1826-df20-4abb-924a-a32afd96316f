<?php

namespace App\Listeners;

use App\Events\UserLeveledUp;
use App\Notifications\LevelUp;
use App\Services\AchievementService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendLevelUpNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserLeveledUp $event): void
    {
        // Send level up notification
        $event->user->notify(new LevelUp(
            $event->newLevel,
            $event->previousLevel,
            50 // XP reward for leveling up
        ));

        // Award level up achievement
        app(AchievementService::class)->checkAchievements($event->user, 'level_up', [
            'level' => $event->newLevel,
            'previous_level' => $event->previousLevel,
        ]);

        // Check for XP-based badges
        app(AchievementService::class)->checkXpBadges($event->user);
    }
}
