<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_room_id',
        'user_id',
        'message',
        'type',
        'file_url',
        'file_name',
        'file_size',
        'is_edited',
        'edited_at',
        'is_deleted',
        'deleted_at',
        'reply_to_message_id',
    ];

    protected $casts = [
        'is_edited' => 'boolean',
        'edited_at' => 'datetime',
        'is_deleted' => 'boolean',
        'deleted_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function chatRoom(): BelongsTo
    {
        return $this->belongsTo(ChatRoom::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scopes
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Helper Methods
     */
    public function getFormattedTime(): string
    {
        $now = now();
        $messageTime = $this->created_at;

        if ($messageTime->isToday()) {
            return $messageTime->format('g:i A');
        } elseif ($messageTime->isYesterday()) {
            return 'Yesterday ' . $messageTime->format('g:i A');
        } elseif ($messageTime->diffInDays($now) < 7) {
            return $messageTime->format('D g:i A');
        } else {
            return $messageTime->format('M j, g:i A');
        }
    }

    public function canBeEditedBy(User $user): bool
    {
        // User can edit their own messages within 15 minutes
        return $this->user_id === $user->id &&
               $this->created_at->diffInMinutes(now()) <= 15;
    }

    public function canBeDeletedBy(User $user): bool
    {
        // User can delete their own messages, or admins can delete any
        return $this->user_id === $user->id || $user->hasRole('admin');
    }

    public function getProcessedMessage(): string
    {
        $message = $this->message;

        // Convert URLs to links
        $message = preg_replace(
            '/(https?:\/\/[^\s]+)/',
            '<a href="$1" target="_blank" class="text-primary-400 hover:text-primary-300 underline">$1</a>',
            $message
        );

        // Convert @mentions to highlights (basic implementation)
        $message = preg_replace(
            '/@(\w+)/',
            '<span class="text-yellow-400 font-medium">@$1</span>',
            $message
        );

        return $message;
    }
}
