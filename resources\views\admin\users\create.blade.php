@extends('layouts.admin')

@section('title', 'Create User')

@section('content')
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-2xl font-bold">Create New User</h1>
            <p class="text-gray-400 mt-1">Add a new user to the system</p>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Users
            </a>
        </div>
    </div>

    <!-- Create Form -->
    <div class="bg-gray-800 rounded-lg border border-gray-700">
        <form action="{{ route('admin.users.store') }}" method="POST" class="p-6">
            @csrf

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4 text-white">Basic Information</h3>
                        
                        <!-- Name -->
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}" 
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" 
                                   placeholder="Enter full name" required>
                            @error('name')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div class="mb-4">
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}" 
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" 
                                   placeholder="Enter email address" required>
                            @error('email')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div class="mb-4">
                            <label for="password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                            <input type="password" id="password" name="password" 
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" 
                                   placeholder="Enter password" required>
                            @error('password')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div class="mb-4">
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-300 mb-2">Confirm Password</label>
                            <input type="password" id="password_confirmation" name="password_confirmation" 
                                   class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" 
                                   placeholder="Confirm password" required>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <!-- Account Settings -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4 text-white">Account Settings</h3>
                        
                        <!-- Subscription Plan -->
                        <div class="mb-6">
                            <label for="subscription_plan" class="block text-sm font-medium text-gray-300 mb-2">Subscription Plan</label>
                            <select id="subscription_plan" name="subscription_plan" 
                                    class="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent" 
                                    required>
                                <option value="">Select a plan</option>
                                @foreach($subscriptionPlans as $plan)
                                    <option value="{{ $plan }}" {{ old('subscription_plan') === $plan ? 'selected' : '' }}>
                                        {{ ucfirst($plan) }}
                                        @if($plan === 'free')
                                            - Free Access
                                        @elseif($plan === 'prosper')
                                            - $49/month
                                        @elseif($plan === 'conquer')
                                            - $99/month
                                        @elseif($plan === 'champions')
                                            - $199/month
                                        @endif
                                    </option>
                                @endforeach
                            </select>
                            @error('subscription_plan')
                                <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Roles & Permissions -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4 text-white">Roles & Permissions</h3>
                        <p class="text-sm text-gray-400 mb-4">Select the roles to assign to this user</p>
                        
                        <div class="space-y-3">
                            @foreach($roles as $role)
                                <label class="flex items-start">
                                    <input type="checkbox" name="roles[]" value="{{ $role->name }}" 
                                           {{ in_array($role->name, old('roles', [])) ? 'checked' : '' }}
                                           class="mt-1 rounded border-gray-600 text-red-600 shadow-sm focus:ring-red-500 focus:ring-offset-gray-800">
                                    <div class="ml-3">
                                        <span class="text-sm font-medium text-gray-300">{{ ucfirst($role->name) }}</span>
                                        <p class="text-xs text-gray-400">
                                            @if($role->name === 'admin')
                                                Full system access and management
                                            @elseif($role->name === 'champion')
                                                Premium user with special privileges
                                            @else
                                                Standard user access
                                            @endif
                                        </p>
                                    </div>
                                </label>
                            @endforeach
                        </div>
                        @error('roles')
                            <p class="text-red-400 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-3 mt-8 pt-6 border-t border-gray-700">
                <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create User
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
