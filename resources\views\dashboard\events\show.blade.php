@extends('layouts.dashboard')

@section('title', $event->title . ' - The Real World')
@section('page-title', $event->title)

@section('content')
<div class="p-6">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ route('dashboard.events.index') }}"
           class="inline-flex items-center text-gray-400 hover:text-white transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Events
        </a>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Event Header -->
            <div class="card mb-8">
                <!-- Event Image -->
                @if($event->thumbnail_url)
                <div class="relative mb-6">
                    <img src="{{ $event->thumbnail_url }}" alt="{{ $event->title }}" 
                         class="w-full h-64 object-cover rounded-lg">
                    
                    <!-- Status Overlay -->
                    @php $statusBadge = $event->getStatusBadge(); @endphp
                    <div class="absolute top-4 right-4">
                        <span class="px-4 py-2 rounded-full text-sm font-medium {{ $statusBadge['class'] }}">
                            {{ $statusBadge['text'] }}
                        </span>
                    </div>
                </div>
                @endif

                <!-- Event Title & Details -->
                <div class="flex items-start justify-between mb-6">
                    <div class="flex-1">
                        <h1 class="text-3xl font-bold mb-4">{{ $event->title }}</h1>
                        
                        <!-- Event Meta -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="flex items-center text-gray-300">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                {{ $event->start_datetime->format('l, F j, Y') }}
                            </div>
                            
                            <div class="flex items-center text-gray-300">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ $event->start_datetime->format('g:i A') }} - {{ $event->end_datetime->format('g:i A') }}
                            </div>
                            
                            <div class="flex items-center text-gray-300">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                {{ $event->campus->name ?? 'General' }}
                            </div>
                            
                            <div class="flex items-center text-gray-300">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ $event->getFormattedDuration() }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Event Description -->
                <div class="prose prose-invert max-w-none">
                    <p class="text-gray-300 leading-relaxed">{{ $event->description }}</p>
                </div>
            </div>

            <!-- Event Details -->
            <div class="card">
                <h3 class="text-xl font-semibold mb-6">Event Details</h3>
                
                <div class="space-y-6">
                    <!-- What You'll Learn -->
                    <div>
                        <h4 class="font-semibold mb-3">What You'll Learn:</h4>
                        <ul class="space-y-2 text-gray-300">
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Proven strategies used by successful entrepreneurs
                            </li>
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Real-world case studies and examples
                            </li>
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Interactive Q&A session with mentors
                            </li>
                            <li class="flex items-start">
                                <svg class="w-5 h-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Actionable steps you can implement immediately
                            </li>
                        </ul>
                    </div>

                    <!-- Requirements -->
                    @if($event->is_premium)
                    <div>
                        <h4 class="font-semibold mb-3">Requirements:</h4>
                        <div class="bg-yellow-600/20 rounded-lg p-4">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-yellow-400 font-medium">Premium Event</span>
                            </div>
                            <p class="text-yellow-300 text-sm mt-2">
                                This event requires a premium subscription to access.
                            </p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- RSVP Card -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Event Registration</h3>
                
                <!-- Countdown Timer -->
                @if($event->countdown['status'] === 'upcoming')
                <div class="bg-primary-600/20 rounded-lg p-4 mb-6">
                    <p class="text-primary-400 text-sm font-medium mb-3 text-center">{{ $event->countdown['message'] }}</p>
                    <div class="grid grid-cols-4 gap-2 text-center">
                        <div>
                            <div class="text-xl font-bold text-primary-400">{{ $event->countdown['days'] }}</div>
                            <div class="text-xs text-gray-400">Days</div>
                        </div>
                        <div>
                            <div class="text-xl font-bold text-primary-400">{{ $event->countdown['hours'] }}</div>
                            <div class="text-xs text-gray-400">Hours</div>
                        </div>
                        <div>
                            <div class="text-xl font-bold text-primary-400">{{ $event->countdown['minutes'] }}</div>
                            <div class="text-xs text-gray-400">Min</div>
                        </div>
                        <div>
                            <div class="text-xl font-bold text-primary-400">{{ $event->countdown['seconds'] }}</div>
                            <div class="text-xs text-gray-400">Sec</div>
                        </div>
                    </div>
                </div>
                @elseif($event->countdown['status'] === 'live')
                <div class="bg-red-600/20 rounded-lg p-4 mb-6">
                    <p class="text-red-400 font-medium text-center">
                        🔴 LIVE NOW
                    </p>
                </div>
                @endif

                <!-- Registration Status -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-400">Attendees:</span>
                        <span class="font-medium">{{ $event->rsvp_count }}/{{ $event->max_attendees ?? '∞' }}</span>
                    </div>

                    <!-- Action Button -->
                    @if($event->can_join)
                        <a href="{{ route('dashboard.events.join', $event) }}" 
                           class="btn-primary w-full text-center">
                            🔴 Join Live Event
                        </a>
                    @elseif($event->is_user_registered)
                        @if($event->countdown['status'] === 'upcoming')
                            <div class="text-center">
                                <div class="bg-green-600/20 rounded-lg p-3 mb-3">
                                    <p class="text-green-400 font-medium">✓ You're registered!</p>
                                </div>
                                <button onclick="cancelRsvp({{ $event->id }})" 
                                        class="btn-secondary w-full" 
                                        id="cancel-btn">
                                    Cancel RSVP
                                </button>
                            </div>
                        @else
                            <button class="btn-secondary w-full" disabled>
                                Event Ended
                            </button>
                        @endif
                    @else
                        @if($event->countdown['status'] === 'upcoming' && $event->canUserAccess($user))
                            <button onclick="rsvpToEvent({{ $event->id }})" 
                                    class="btn-primary w-full" 
                                    id="rsvp-btn">
                                RSVP to Event
                            </button>
                        @elseif(!$event->canUserAccess($user))
                            <a href="{{ route('subscription.plans') }}" 
                               class="btn-secondary w-full text-center">
                                Upgrade to Access
                            </a>
                        @else
                            <button class="btn-secondary w-full" disabled>
                                Event Ended
                            </button>
                        @endif
                    @endif
                </div>
            </div>

            <!-- Recent Attendees -->
            @if($recentAttendees->count() > 0)
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Recent Attendees</h3>
                <div class="space-y-3">
                    @foreach($recentAttendees->take(6) as $attendee)
                    <div class="flex items-center">
                        <img src="{{ $attendee->user->getAvatarUrl() }}" 
                             alt="{{ $attendee->user->name }}" 
                             class="w-8 h-8 rounded-full mr-3">
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white truncate">{{ $attendee->user->name }}</p>
                            <p class="text-xs text-gray-400">Level {{ $attendee->user->level }}</p>
                        </div>
                    </div>
                    @endforeach
                    
                    @if($recentAttendees->count() > 6)
                    <p class="text-xs text-gray-400 text-center pt-2">
                        +{{ $recentAttendees->count() - 6 }} more attending
                    </p>
                    @endif
                </div>
            </div>
            @endif

            <!-- Event Info -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Event Information</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Campus:</span>
                        <span>{{ $event->campus->name ?? 'General' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Duration:</span>
                        <span>{{ $event->getFormattedDuration() }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Max Attendees:</span>
                        <span>{{ $event->max_attendees ?? 'Unlimited' }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">Type:</span>
                        <span class="capitalize">{{ str_replace('_', ' ', $event->status) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function rsvpToEvent(eventId) {
    const button = document.getElementById('rsvp-btn');
    button.disabled = true;
    button.textContent = 'RSVP\'ing...';

    fetch(`/dashboard/events/${eventId}/rsvp`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.message, 'error');
            button.disabled = false;
            button.textContent = 'RSVP to Event';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        button.disabled = false;
        button.textContent = 'RSVP to Event';
    });
}

function cancelRsvp(eventId) {
    const button = document.getElementById('cancel-btn');
    button.disabled = true;
    button.textContent = 'Cancelling...';

    fetch(`/dashboard/events/${eventId}/rsvp`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification(data.message, 'error');
            button.disabled = false;
            button.textContent = 'Cancel RSVP';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');
        button.disabled = false;
        button.textContent = 'Cancel RSVP';
    });
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg text-white ${type === 'success' ? 'bg-green-600' : 'bg-red-600'}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endsection
