@extends('layouts.dashboard')

@section('title', 'Billing - The Real World')
@section('page-title', 'Billing & Invoices')

@section('content')
<div class="p-6">
    <!-- Settings Navigation -->
    <div class="mb-8">
        <nav class="flex space-x-8">
            <a href="{{ route('dashboard.settings.index') }}" 
               class="pb-2 border-b-2 border-transparent text-gray-400 hover:text-white transition-colors">
                General
            </a>
            <a href="{{ route('dashboard.settings.subscription') }}" 
               class="pb-2 border-b-2 border-transparent text-gray-400 hover:text-white transition-colors">
                Subscription
            </a>
            <a href="{{ route('dashboard.settings.billing') }}" 
               class="pb-2 border-b-2 border-primary-500 text-primary-400 font-medium">
                Billing
            </a>
        </nav>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Current Subscription -->
        <div class="card">
            <h3 class="text-xl font-semibold mb-6">Current Subscription</h3>
            
            <div class="bg-gray-700 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h4 class="text-lg font-semibold">{{ ucfirst($user->subscription_plan ?? 'free') }} Plan</h4>
                        <p class="text-gray-400">
                            @if($user->subscription_plan && $user->subscription_plan !== 'free')
                                Active subscription
                            @else
                                Free plan
                            @endif
                        </p>
                    </div>
                    <div class="text-right">
                        @if($user->subscription_plan && $user->subscription_plan !== 'free')
                            @php
                                $prices = ['prosper' => 49, 'conquer' => 99, 'champions' => 199];
                                $price = $prices[$user->subscription_plan] ?? 0;
                            @endphp
                            <div class="text-2xl font-bold">${{ $price }}</div>
                            <div class="text-sm text-gray-400">per month</div>
                        @else
                            <div class="text-2xl font-bold text-green-400">$0</div>
                            <div class="text-sm text-gray-400">Free forever</div>
                        @endif
                    </div>
                </div>
                
                @if($user->subscription_plan && $user->subscription_plan !== 'free')
                <div class="border-t border-gray-600 pt-4">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-400">Next billing date:</span>
                        <span>{{ now()->addMonth()->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between text-sm mt-2">
                        <span class="text-gray-400">Payment method:</span>
                        <span>•••• •••• •••• 4242</span>
                    </div>
                </div>
                @endif
            </div>

            <div class="flex space-x-4">
                <a href="{{ route('subscription.plans') }}" class="btn-primary">
                    Manage Subscription
                </a>
                @if($user->subscription_plan && $user->subscription_plan !== 'free')
                <button class="btn-secondary">Update Payment Method</button>
                @endif
            </div>
        </div>

        <!-- Payment Method -->
        @if($user->subscription_plan && $user->subscription_plan !== 'free')
        <div class="card">
            <h3 class="text-xl font-semibold mb-6">Payment Method</h3>
            
            <div class="bg-gray-700 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-8 bg-blue-600 rounded flex items-center justify-center mr-4">
                            <span class="text-white text-xs font-bold">VISA</span>
                        </div>
                        <div>
                            <div class="font-medium">•••• •••• •••• 4242</div>
                            <div class="text-sm text-gray-400">Expires 12/2025</div>
                        </div>
                    </div>
                    <button class="text-primary-400 hover:text-primary-300 text-sm">Edit</button>
                </div>
            </div>

            <div class="bg-gray-700 rounded-lg p-4">
                <h4 class="font-medium mb-2">Billing Address</h4>
                <div class="text-sm text-gray-400">
                    <p>123 Main Street</p>
                    <p>New York, NY 10001</p>
                    <p>United States</p>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Billing History -->
    @if($user->subscription_plan && $user->subscription_plan !== 'free')
    <div class="mt-8">
        <div class="card">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold">Billing History</h3>
                <button class="btn-secondary text-sm">Download All</button>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Invoice</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700">
                        @php
                            $prices = ['prosper' => 49, 'conquer' => 99, 'champions' => 199];
                            $invoices = [
                                [
                                    'date' => now()->subMonth(),
                                    'description' => ucfirst($user->subscription_plan) . ' Plan - Monthly',
                                    'amount' => $prices[$user->subscription_plan] ?? 0,
                                    'status' => 'Paid',
                                    'id' => 'inv_001'
                                ],
                                [
                                    'date' => now()->subMonths(2),
                                    'description' => ucfirst($user->subscription_plan) . ' Plan - Monthly',
                                    'amount' => $prices[$user->subscription_plan] ?? 0,
                                    'status' => 'Paid',
                                    'id' => 'inv_002'
                                ],
                                [
                                    'date' => now()->subMonths(3),
                                    'description' => ucfirst($user->subscription_plan) . ' Plan - Monthly',
                                    'amount' => $prices[$user->subscription_plan] ?? 0,
                                    'status' => 'Paid',
                                    'id' => 'inv_003'
                                ],
                            ];
                        @endphp
                        
                        @foreach($invoices as $invoice)
                        <tr class="hover:bg-gray-700">
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                {{ $invoice['date']->format('M j, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-white">{{ $invoice['description'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                ${{ number_format($invoice['amount'], 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-green-600 text-white">
                                    {{ $invoice['status'] }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <button class="text-primary-400 hover:text-primary-300">
                                    Download PDF
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    @endif

    <!-- Support -->
    <div class="mt-8">
        <div class="card">
            <h3 class="text-xl font-semibold mb-4">Need Help with Billing?</h3>
            <p class="text-gray-400 mb-6">
                Our billing support team is here to help with any questions about your subscription, invoices, or payment methods.
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="mailto:<EMAIL>" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium">Email Support</h4>
                        <p class="text-sm text-gray-400"><EMAIL></p>
                    </div>
                </a>
                
                <a href="{{ route('dashboard.chat') }}" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium">Live Chat</h4>
                        <p class="text-sm text-gray-400">Get instant help</p>
                    </div>
                </a>
                
                <a href="tel:+1234567890" 
                   class="flex items-center p-4 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-6 h-6 text-primary-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium">Phone Support</h4>
                        <p class="text-sm text-gray-400">+****************</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
