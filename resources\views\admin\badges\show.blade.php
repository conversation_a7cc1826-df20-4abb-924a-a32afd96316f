@extends('layouts.admin')

@section('title', 'Badge Details - ' . $badge->name)

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Badge Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.badges.index') }}">Badges</a></li>
                    <li class="breadcrumb-item active">{{ $badge->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.badges.edit', $badge) }}" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Badge
            </a>
            <a href="{{ route('admin.badges.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Badges
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Badge Information -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Badge Name</label>
                                <p class="form-control-plaintext">{{ $badge->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Slug</label>
                                <p class="form-control-plaintext"><code>{{ $badge->slug }}</code></p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Description</label>
                        <p class="form-control-plaintext">{{ $badge->description }}</p>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Type</label>
                                <p class="form-control-plaintext">
                                    <span class="badge badge-primary">{{ ucfirst($badge->type) }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">XP Reward</label>
                                <p class="form-control-plaintext">
                                    <span class="badge badge-success">{{ $badge->xp_reward ?? 0 }} XP</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Status</label>
                                <p class="form-control-plaintext">
                                    @if($badge->is_active)
                                        <span class="badge badge-success">Active</span>
                                    @else
                                        <span class="badge badge-danger">Inactive</span>
                                    @endif
                                    @if($badge->is_rare)
                                        <span class="badge badge-warning ml-1">Rare</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($badge->requirements)
                    <div class="mb-3">
                        <label class="form-label font-weight-bold">Requirements</label>
                        <pre class="bg-light p-3 rounded"><code>{{ json_encode($badge->requirements, JSON_PRETTY_PRINT) }}</code></pre>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Sort Order</label>
                                <p class="form-control-plaintext">{{ $badge->sort_order ?? 0 }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label font-weight-bold">Created</label>
                                <p class="form-control-plaintext">{{ $badge->created_at->format('M j, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Badge Earners -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Badge Earners</h6>
                </div>
                <div class="card-body">
                    @if($badge->users()->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Email</th>
                                        <th>Earned Date</th>
                                        <th>XP Earned</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($badge->users()->latest('user_badges.created_at')->take(10)->get() as $user)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center mr-2">
                                                    <span class="text-white font-weight-bold">{{ substr($user->name, 0, 1) }}</span>
                                                </div>
                                                <div>
                                                    <strong>{{ $user->name }}</strong>
                                                    <br>
                                                    <small class="text-muted">Level {{ $user->level }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $user->email }}</td>
                                        <td>{{ $user->pivot->created_at->format('M j, Y') }}</td>
                                        <td>
                                            <span class="badge badge-success">{{ $user->pivot->xp_earned ?? $badge->xp_reward ?? 0 }} XP</span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if($badge->users()->count() > 10)
                        <div class="text-center mt-3">
                            <small class="text-muted">Showing 10 of {{ $badge->users()->count() }} total earners</small>
                        </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-award fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No one has earned this badge yet</h5>
                            <p class="text-muted">This badge is waiting for its first earner!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Badge Preview & Stats -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Preview</h6>
                </div>
                <div class="card-body text-center">
                    <div class="badge-preview mb-3">
                        <div class="badge-icon" style="width: 100px; height: 100px; background-color: {{ $badge->color ?? '#6366f1' }}; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                            @if($badge->icon_url)
                                <img src="{{ $badge->icon_url }}" alt="{{ $badge->name }}" style="width: 60px; height: 60px; border-radius: 50%;">
                            @else
                                <i class="fas fa-award text-white" style="font-size: 2.5rem;"></i>
                            @endif
                        </div>
                        <h4 class="badge-name">{{ $badge->name }}</h4>
                        <p class="badge-description text-muted">{{ $badge->description }}</p>
                        <div class="badge-meta">
                            <span class="badge badge-primary">{{ ucfirst($badge->type) }}</span>
                            <span class="badge badge-success">{{ $badge->xp_reward ?? 0 }} XP</span>
                            @if($badge->is_rare)
                                <span class="badge badge-warning">Rare</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Badge Statistics -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Badge Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-6">
                            <div class="border-right">
                                <h3 class="text-primary">{{ $badge->users()->count() }}</h3>
                                <small class="text-muted">Total Earned</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success">{{ $badge->users()->wherePivot('created_at', '>=', now()->subDays(30))->count() }}</h3>
                            <small class="text-muted">This Month</small>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-right">
                                <h4 class="text-info">{{ $badge->users()->wherePivot('created_at', '>=', now()->subDays(7))->count() }}</h4>
                                <small class="text-muted">This Week</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ $badge->users()->wherePivot('created_at', '>=', now()->startOfDay())->count() }}</h4>
                            <small class="text-muted">Today</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.badges.edit', $badge) }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-edit"></i> Edit Badge
                        </a>
                        @if($badge->users()->count() == 0)
                        <form action="{{ route('admin.badges.destroy', $badge) }}" method="POST" class="d-inline" 
                              onsubmit="return confirm('Are you sure you want to delete this badge?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-outline-danger btn-sm w-100">
                                <i class="fas fa-trash"></i> Delete Badge
                            </button>
                        </form>
                        @else
                        <button class="btn btn-outline-danger btn-sm" disabled title="Cannot delete badge with earners">
                            <i class="fas fa-trash"></i> Cannot Delete (Has Earners)
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}
</style>
@endpush
