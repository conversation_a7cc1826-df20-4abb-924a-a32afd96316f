<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Campus management
            'view campuses',
            'create campuses',
            'edit campuses',
            'delete campuses',

            // Course management
            'view courses',
            'create courses',
            'edit courses',
            'delete courses',

            // Lesson management
            'view lessons',
            'create lessons',
            'edit lessons',
            'delete lessons',

            // Task management
            'view tasks',
            'create tasks',
            'edit tasks',
            'delete tasks',

            // Event management
            'view events',
            'create events',
            'edit events',
            'delete events',

            // Chat management
            'view chat',
            'moderate chat',
            'delete messages',

            // Badge management
            'view badges',
            'create badges',
            'edit badges',
            'delete badges',
            'award badges',

            // Testimonial management
            'view testimonials',
            'approve testimonials',
            'reject testimonials',
            'delete testimonials',

            // Analytics
            'view analytics',
            'view reports',

            // System settings
            'manage settings',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles and assign permissions
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $adminRole->givePermissionTo(Permission::all());

        $mentorRole = Role::firstOrCreate(['name' => 'mentor']);
        $mentorRole->givePermissionTo([
            'view users',
            'view campuses',
            'view courses',
            'view lessons',
            'view tasks',
            'view events',
            'create events',
            'edit events',
            'view chat',
            'moderate chat',
            'view badges',
            'award badges',
            'view testimonials',
            'approve testimonials',
        ]);

        $championRole = Role::firstOrCreate(['name' => 'champion']);
        $championRole->givePermissionTo([
            'view campuses',
            'view courses',
            'view lessons',
            'view tasks',
            'view events',
            'view chat',
            'view badges',
        ]);

        $userRole = Role::firstOrCreate(['name' => 'user']);
        $userRole->givePermissionTo([
            'view campuses',
            'view courses',
            'view lessons',
            'view tasks',
            'view events',
            'view chat',
            'view badges',
        ]);
    }
}
