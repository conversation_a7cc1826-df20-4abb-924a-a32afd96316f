<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Course;

class CourseCompleted extends Notification implements ShouldQueue
{
    use Queueable;

    protected $course;
    protected $xpEarned;

    /**
     * Create a new notification instance.
     */
    public function __construct(Course $course, int $xpEarned = 0)
    {
        $this->course = $course;
        $this->xpEarned = $xpEarned;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
                    ->subject('🏆 Course Completed: ' . $this->course->title)
                    ->greeting('Congratulations, ' . $notifiable->name . '!')
                    ->line('You\'ve successfully completed the course: **' . $this->course->title . '**')
                    ->line('This is a major milestone in your journey to financial freedom. You\'ve proven that you have what it takes to learn, grow, and execute.')
                    ->when($this->xpEarned > 0, function ($message) {
                        return $message->line('🎯 **XP Earned:** ' . number_format($this->xpEarned) . ' XP');
                    })
                    ->line('**What\'s next?**')
                    ->line('• Apply what you\'ve learned immediately')
                    ->line('• Share your progress with the community')
                    ->line('• Continue with the next course in your learning path')
                    ->line('• Help other students who are just starting')
                    ->action('Continue Learning', route('dashboard.campuses'))
                    ->line('Remember: Knowledge without action is worthless. Now go make some money!')
                    ->salutation('Keep grinding,')
                    ->salutation('The Real World Team');
    }

    /**
     * Get the database representation of the notification.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => 'Course Completed!',
            'message' => 'You\'ve successfully completed "' . $this->course->title . '"' . ($this->xpEarned > 0 ? ' and earned ' . number_format($this->xpEarned) . ' XP!' : '!'),
            'action_url' => route('dashboard.campuses'),
            'action_text' => 'Continue Learning',
            'type' => 'course_completed',
            'icon' => '🏆',
            'data' => [
                'course_id' => $this->course->id,
                'course_title' => $this->course->title,
                'xp_earned' => $this->xpEarned,
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Course Completed!',
            'message' => 'You\'ve completed "' . $this->course->title . '".',
            'type' => 'course_completed',
            'course_id' => $this->course->id,
            'xp_earned' => $this->xpEarned,
        ];
    }
}
