@extends('layouts.dashboard')

@section('title', 'Courses - The Real World')
@section('page-title', 'Courses')

@section('content')
<div class="p-6">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h2 class="text-2xl font-bold mb-2">All Courses</h2>
            <p class="text-gray-400">Explore our comprehensive course library</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('dashboard.search.advanced') }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                </svg>
                Advanced Search
            </a>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" 
                       id="searchInput"
                       placeholder="Search courses..."
                       class="input-field">
            </div>
            
            <select id="campusFilter" class="input-field w-auto">
                <option value="">All Campuses</option>
                @foreach($campuses as $campus)
                    <option value="{{ $campus->id }}">{{ $campus->name }}</option>
                @endforeach
            </select>
            
            <select id="difficultyFilter" class="input-field w-auto">
                <option value="">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
            </select>
            
            <select id="sortFilter" class="input-field w-auto">
                <option value="newest">Newest</option>
                <option value="popular">Most Popular</option>
                <option value="rating">Highest Rated</option>
                <option value="duration">Duration</option>
            </select>
        </div>
    </div>

    <!-- Course Grid -->
    <div id="courseGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        @forelse($courses as $course)
            <div class="course-card bg-gray-700 rounded-lg overflow-hidden hover:bg-gray-600 transition-colors">
                <div class="aspect-video bg-gray-800 flex items-center justify-center">
                    @if($course->getFirstMediaUrl('thumbnail'))
                        <img src="{{ $course->getFirstMediaUrl('thumbnail') }}" 
                             alt="{{ $course->title }}" 
                             class="w-full h-full object-cover">
                    @else
                        <div class="text-6xl">📚</div>
                    @endif
                </div>
                
                <div class="p-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-xs bg-primary-600 text-white px-2 py-1 rounded">
                            {{ $course->campus->name ?? 'General' }}
                        </span>
                        <span class="text-xs text-gray-400 capitalize">
                            {{ $course->difficulty_level }}
                        </span>
                    </div>
                    
                    <h3 class="font-semibold text-lg mb-2 line-clamp-2">{{ $course->title }}</h3>
                    <p class="text-gray-400 text-sm mb-4 line-clamp-3">{{ $course->description }}</p>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-4 text-sm text-gray-400">
                            @if($course->estimated_duration)
                                <span>{{ $course->estimated_duration }} min</span>
                            @endif
                            @if($course->rating)
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                    {{ number_format($course->rating, 1) }}
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    @auth
                        @php
                            $enrollment = $course->enrollments()->where('user_id', auth()->id())->first();
                            $progress = $enrollment ? $enrollment->progress_percentage : 0;
                        @endphp
                        
                        @if($enrollment)
                            <div class="mb-4">
                                <div class="flex items-center justify-between text-sm mb-1">
                                    <span class="text-gray-400">Progress</span>
                                    <span class="font-medium">{{ $progress }}%</span>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2">
                                    <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" 
                                         style="width: {{ $progress }}%"></div>
                                </div>
                            </div>
                        @endif
                    @endauth
                    
                    <a href="{{ route('dashboard.courses.show', $course) }}" 
                       class="btn-primary w-full text-center">
                        @auth
                            @if($enrollment)
                                @if($progress >= 100)
                                    Review Course
                                @else
                                    Continue Learning
                                @endif
                            @else
                                Start Course
                            @endif
                        @else
                            View Course
                        @endauth
                    </a>
                </div>
            </div>
        @empty
            <div class="col-span-full text-center py-12">
                <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">No courses found</h3>
                <p class="text-gray-400">Check back later for new courses or adjust your filters</p>
            </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($courses->hasPages())
        <div class="mt-8">
            {{ $courses->links() }}
        </div>
    @endif
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}
</style>

<script>
// Simple filtering functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const campusFilter = document.getElementById('campusFilter');
    const difficultyFilter = document.getElementById('difficultyFilter');
    const sortFilter = document.getElementById('sortFilter');

    function updateFilters() {
        const params = new URLSearchParams(window.location.search);
        
        if (searchInput.value) params.set('search', searchInput.value);
        else params.delete('search');
        
        if (campusFilter.value) params.set('campus', campusFilter.value);
        else params.delete('campus');
        
        if (difficultyFilter.value) params.set('difficulty', difficultyFilter.value);
        else params.delete('difficulty');
        
        if (sortFilter.value) params.set('sort', sortFilter.value);
        else params.delete('sort');
        
        window.location.search = params.toString();
    }

    searchInput.addEventListener('input', debounce(updateFilters, 500));
    campusFilter.addEventListener('change', updateFilters);
    difficultyFilter.addEventListener('change', updateFilters);
    sortFilter.addEventListener('change', updateFilters);

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
</script>
@endsection
