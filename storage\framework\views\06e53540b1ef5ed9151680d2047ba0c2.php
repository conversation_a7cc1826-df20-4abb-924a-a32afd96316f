<?php $__env->startSection('title', 'Campuses - The Real World'); ?>
<?php $__env->startSection('description', 'Explore all available campuses and choose your path to financial freedom. Each campus teaches specific skills to build wealth.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold font-display mb-6">
            Choose Your <span class="text-gradient">Campus</span>
        </h1>
        <p class="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Each campus is designed to teach you a specific skill set that can generate real income. 
            Pick your path to financial freedom and start learning from the best mentors in the industry.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('register')); ?>" class="btn-primary text-lg px-8 py-4">
                Start Learning Today
            </a>
            <a href="<?php echo e(route('pricing')); ?>" class="btn-secondary text-lg px-8 py-4">
                View Pricing Plans
            </a>
        </div>
    </div>
</section>

<!-- Campuses Grid -->
<section class="py-20 bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $campuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $campus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="card hover:transform hover:scale-105 transition-all duration-300 cursor-pointer group relative overflow-hidden">
                <!-- Premium Badge -->
                <?php if($campus->is_premium): ?>
                <div class="absolute top-4 right-4 z-10">
                    <span class="bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded-full">
                        Premium
                    </span>
                </div>
                <?php endif; ?>

                <!-- Campus Icon & Header -->
                <div class="flex items-center mb-6">
                    <div class="w-16 h-16 rounded-xl mr-4 flex items-center justify-center text-3xl flex-shrink-0" 
                         style="background-color: <?php echo e($campus->color); ?>20; border: 2px solid <?php echo e($campus->color); ?>;">
                        <?php if($campus->icon_url): ?>
                            <img src="<?php echo e($campus->icon_url); ?>" alt="<?php echo e($campus->name); ?>" class="w-10 h-10">
                        <?php else: ?>
                            <span style="color: <?php echo e($campus->color); ?>">💼</span>
                        <?php endif; ?>
                    </div>
                    <div>
                        <h3 class="text-2xl font-bold group-hover:text-red-400 transition-colors mb-1">
                            <?php echo e($campus->name); ?>

                        </h3>
                        <p class="text-sm text-gray-400">
                            <?php echo e($campus->is_premium ? 'Premium Campus' : 'Free Access'); ?>

                        </p>
                    </div>
                </div>

                <!-- Description -->
                <p class="text-gray-300 mb-6 leading-relaxed">
                    <?php echo e($campus->description); ?>

                </p>

                <!-- Features/Benefits -->
                <div class="space-y-2 mb-6">
                    <div class="flex items-center text-sm text-gray-400">
                        <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Expert mentors & live sessions
                    </div>
                    <div class="flex items-center text-sm text-gray-400">
                        <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Step-by-step courses
                    </div>
                    <div class="flex items-center text-sm text-gray-400">
                        <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Community support
                    </div>
                    <div class="flex items-center text-sm text-gray-400">
                        <svg class="w-4 h-4 mr-2 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Real-world projects
                    </div>
                </div>

                <!-- Action Button -->
                <div class="flex items-center justify-between">
                    <?php if(auth()->guard()->check()): ?>
                        <?php if(auth()->user()->canAccessCampus($campus)): ?>
                            <a href="#" class="btn-primary w-full text-center">
                                Enter Campus
                            </a>
                        <?php else: ?>
                            <a href="<?php echo e(route('pricing')); ?>" class="btn-secondary w-full text-center">
                                Upgrade to Access
                            </a>
                        <?php endif; ?>
                    <?php else: ?>
                        <a href="<?php echo e(route('register')); ?>" class="btn-primary w-full text-center">
                            Join to Access
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Hover Effect Overlay -->
                <div class="absolute inset-0 bg-gradient-to-t from-red-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-span-full text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">No Campuses Available</h3>
                <p class="text-gray-400">Check back soon for new campuses and learning opportunities!</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gray-900">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold font-display mb-6">
            Ready to Start Your Journey?
        </h2>
        <p class="text-xl text-gray-400 mb-8">
            Join thousands of students who are already building wealth with real-world skills. 
            Choose your campus and start learning today.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="<?php echo e(route('register')); ?>" class="btn-primary text-lg px-8 py-4">
                Join The Real World
            </a>
            <a href="<?php echo e(route('pricing')); ?>" class="btn-secondary text-lg px-8 py-4">
                View All Plans
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\LMS\resources\views/home/<USER>/ ?>