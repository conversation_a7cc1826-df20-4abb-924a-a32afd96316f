@extends('layouts.dashboard')

@section('title', $badge->name . ' - <PERSON><PERSON> Details - The Real World')
@section('page-title', 'Badge Details')

@section('content')
<div class="p-6">
    <!-- Back Button -->
    <div class="mb-6">
        <a href="{{ route('dashboard.badges.index') }}" class="inline-flex items-center text-gray-400 hover:text-white transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Badges
        </a>
    </div>

    <!-- Badge Header -->
    <div class="card mb-8">
        <div class="flex items-center space-x-6">
            <!-- Badge Icon -->
            <div class="text-8xl {{ $userBadge ? '' : 'grayscale opacity-50' }}">
                {{ $badge->icon ?? '🏆' }}
            </div>
            
            <!-- Badge Info -->
            <div class="flex-1">
                <div class="flex items-center space-x-4 mb-2">
                    <h1 class="text-3xl font-bold">{{ $badge->name }}</h1>
                    @if($userBadge)
                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
                            ✓ Earned
                        </span>
                    @else
                        <span class="bg-gray-600 text-gray-300 px-3 py-1 rounded-full text-sm">
                            Not Earned
                        </span>
                    @endif
                </div>
                
                <p class="text-gray-400 text-lg mb-4">{{ $badge->description }}</p>
                
                <div class="flex items-center space-x-6 text-sm text-gray-500">
                    @if($badge->xp_reward)
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            {{ $badge->xp_reward }} XP Reward
                        </div>
                    @endif
                    
                    @if($badge->difficulty_level)
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                            </svg>
                            {{ ucfirst($badge->difficulty_level) }} Difficulty
                        </div>
                    @endif
                    
                    @if($userBadge)
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Earned {{ $userBadge->pivot->earned_at->diffForHumans() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Requirements Section -->
        <div class="lg:col-span-2">
            <div class="card">
                <h2 class="text-xl font-semibold mb-6">Requirements</h2>
                
                @if($badge->requirements && count($badge->requirements) > 0)
                    <div class="space-y-4">
                        @foreach($badge->requirements as $requirement => $target)
                            @php
                                $progressData = $progress[$requirement] ?? ['current' => 0, 'target' => $target, 'percentage' => 0];
                                $isCompleted = $progressData['percentage'] >= 100;
                            @endphp
                            
                            <div class="border border-gray-700 rounded-lg p-4 {{ $isCompleted ? 'bg-green-900/20 border-green-600' : '' }}">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center space-x-3">
                                        @if($isCompleted)
                                            <div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
                                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                            </div>
                                        @else
                                            <div class="w-6 h-6 bg-gray-600 rounded-full"></div>
                                        @endif
                                        
                                        <h3 class="font-semibold {{ $isCompleted ? 'text-green-400' : '' }}">
                                            {{ ucfirst(str_replace('_', ' ', $requirement)) }}
                                        </h3>
                                    </div>
                                    
                                    <span class="text-sm {{ $isCompleted ? 'text-green-400' : 'text-gray-400' }}">
                                        {{ $progressData['current'] }} / {{ $progressData['target'] }}
                                    </span>
                                </div>
                                
                                <!-- Progress Bar -->
                                <div class="w-full bg-gray-700 rounded-full h-2">
                                    <div class="h-2 rounded-full transition-all duration-500 {{ $isCompleted ? 'bg-green-600' : 'bg-primary-600' }}" 
                                         style="width: {{ min(100, $progressData['percentage']) }}%"></div>
                                </div>
                                
                                @if(!$isCompleted)
                                    <p class="text-xs text-gray-500 mt-2">
                                        {{ $progressData['target'] - $progressData['current'] }} more needed
                                    </p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <div class="text-4xl mb-4">🎯</div>
                        <h3 class="text-lg font-semibold mb-2">No Specific Requirements</h3>
                        <p class="text-gray-400">This badge is awarded automatically based on your activity.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Badge Stats -->
        <div class="space-y-6">
            <!-- Badge Statistics -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Badge Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-400">Total Earned</span>
                        <span class="font-semibold">{{ $badge->users()->count() }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-400">Rarity</span>
                        <span class="font-semibold">
                            @php
                                $totalUsers = \App\Models\User::count();
                                $badgeHolders = $badge->users()->count();
                                $rarity = $totalUsers > 0 ? ($badgeHolders / $totalUsers) * 100 : 0;
                            @endphp
                            
                            @if($rarity < 1)
                                <span class="text-purple-400">Legendary</span>
                            @elseif($rarity < 5)
                                <span class="text-yellow-400">Epic</span>
                            @elseif($rarity < 15)
                                <span class="text-blue-400">Rare</span>
                            @elseif($rarity < 30)
                                <span class="text-green-400">Uncommon</span>
                            @else
                                <span class="text-gray-400">Common</span>
                            @endif
                            ({{ number_format($rarity, 1) }}%)
                        </span>
                    </div>
                    
                    @if($badge->created_at)
                        <div class="flex justify-between">
                            <span class="text-gray-400">Created</span>
                            <span class="font-semibold">{{ $badge->created_at->format('M j, Y') }}</span>
                        </div>
                    @endif
                    
                    @if($userBadge)
                        <div class="flex justify-between">
                            <span class="text-gray-400">Your Rank</span>
                            <span class="font-semibold text-primary-400">
                                #{{ $badge->users()->where('user_badges.earned_at', '<', $userBadge->pivot->earned_at)->count() + 1 }}
                            </span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Earners -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Recent Earners</h3>
                
                @php
                    $recentEarners = $badge->users()
                        ->orderBy('user_badges.earned_at', 'desc')
                        ->take(5)
                        ->get();
                @endphp
                
                @if($recentEarners->count() > 0)
                    <div class="space-y-3">
                        @foreach($recentEarners as $earner)
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                    {{ substr($earner->name, 0, 1) }}
                                </div>
                                <div class="flex-1">
                                    <div class="font-semibold text-sm">{{ $earner->name }}</div>
                                    <div class="text-xs text-gray-400">
                                        {{ $earner->pivot->earned_at->diffForHumans() }}
                                    </div>
                                </div>
                                <div class="text-xs text-gray-500">
                                    Level {{ $earner->level }}
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <div class="text-2xl mb-2">👥</div>
                        <p class="text-gray-400 text-sm">No one has earned this badge yet!</p>
                        @if(!$userBadge)
                            <p class="text-primary-400 text-sm mt-1">Be the first!</p>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Action Button -->
            @if(!$userBadge)
                <div class="card text-center">
                    <h3 class="text-lg font-semibold mb-2">Ready to Earn This Badge?</h3>
                    <p class="text-gray-400 text-sm mb-4">Complete the requirements above to unlock this achievement.</p>
                    
                    @if($badge->requirements && isset($badge->requirements['courses_completed']))
                        <a href="{{ route('dashboard.courses.index') }}" class="btn-primary w-full">
                            Browse Courses
                        </a>
                    @elseif($badge->requirements && isset($badge->requirements['events_attended']))
                        <a href="{{ route('dashboard.events.index') }}" class="btn-primary w-full">
                            View Events
                        </a>
                    @else
                        <a href="{{ route('dashboard.index') }}" class="btn-primary w-full">
                            Go to Dashboard
                        </a>
                    @endif
                </div>
            @else
                <div class="card text-center">
                    <div class="text-4xl mb-3">🎉</div>
                    <h3 class="text-lg font-semibold mb-2 text-green-400">Congratulations!</h3>
                    <p class="text-gray-400 text-sm mb-4">You've earned this badge and gained {{ $badge->xp_reward }} XP!</p>
                    
                    <div class="flex space-x-2">
                        <a href="{{ route('dashboard.badges.index') }}" class="btn-secondary flex-1">
                            View All Badges
                        </a>
                        <button onclick="shareAchievement()" class="btn-primary flex-1">
                            Share
                        </button>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<script>
function shareAchievement() {
    if (navigator.share) {
        navigator.share({
            title: 'I earned the {{ $badge->name }} badge!',
            text: 'I just earned the {{ $badge->name }} badge on The Real World! {{ $badge->description }}',
            url: window.location.href
        });
    } else {
        // Fallback to copying to clipboard
        const text = `I just earned the {{ $badge->name }} badge on The Real World! {{ $badge->description }} ${window.location.href}`;
        navigator.clipboard.writeText(text).then(() => {
            alert('Achievement details copied to clipboard!');
        });
    }
}
</script>

<style>
.grayscale {
    filter: grayscale(100%);
}
</style>
@endsection
