<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Only letters, spaces, hyphens, apostrophes, and dots
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                'unique:users,email',
                'not_regex:/\+.*@/', // Prevent email aliases with +
            ],
            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
            ],
            'terms' => [
                'required',
                'accepted',
            ],
            'subscription_plan' => [
                'required',
                'string',
                'in:prosper,conquer,champions',
            ],
            'payment_method' => [
                'required_unless:subscription_plan,free',
                'string',
            ],
            'referral_code' => [
                'nullable',
                'string',
                'max:50',
                'exists:users,referral_code',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The name may only contain letters, spaces, hyphens, apostrophes, and dots.',
            'email.not_regex' => 'Email aliases with + are not allowed.',
            'email.email' => 'Please provide a valid email address.',
            'password.uncompromised' => 'The password has appeared in a data breach. Please choose a different password.',
            'terms.accepted' => 'You must accept the terms and conditions.',
            'subscription_plan.in' => 'Please select a valid subscription plan.',
            'referral_code.exists' => 'The referral code is invalid.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'terms' => 'terms and conditions',
            'subscription_plan' => 'subscription plan',
            'payment_method' => 'payment method',
            'referral_code' => 'referral code',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => trim($this->name),
            'email' => strtolower(trim($this->email)),
        ]);
    }
}
