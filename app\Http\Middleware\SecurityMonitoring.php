<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class SecurityMonitoring
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Monitor suspicious activity
        $this->monitorSuspiciousActivity($request);
        
        // Check for blocked IPs
        if ($this->isIpBlocked($request->ip())) {
            Log::warning('Blocked IP attempted access', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'user_agent' => $request->userAgent(),
            ]);
            
            abort(403, 'Access denied');
        }
        
        // Monitor request patterns
        $this->monitorRequestPatterns($request);
        
        $response = $next($request);
        
        // Log security events
        $this->logSecurityEvents($request, $response);
        
        return $response;
    }

    /**
     * Monitor suspicious activity patterns
     */
    protected function monitorSuspiciousActivity(Request $request): void
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        // Check for rapid requests from same IP
        $requestKey = "requests:{$ip}";
        $requestCount = Cache::get($requestKey, 0);
        Cache::put($requestKey, $requestCount + 1, now()->addMinutes(1));
        
        if ($requestCount > 100) { // More than 100 requests per minute
            $this->flagSuspiciousActivity($ip, 'rapid_requests', [
                'count' => $requestCount,
                'user_agent' => $userAgent,
            ]);
        }
        
        // Check for suspicious user agents
        $suspiciousAgents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'curl',
            'wget',
            'python-requests',
            'bot',
            'crawler',
            'spider',
        ];
        
        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                $this->flagSuspiciousActivity($ip, 'suspicious_user_agent', [
                    'user_agent' => $userAgent,
                ]);
                break;
            }
        }
        
        // Check for SQL injection patterns in URL
        $url = $request->fullUrl();
        $sqlPatterns = [
            'union select',
            'drop table',
            'insert into',
            'delete from',
            'update set',
            'script>',
            '<iframe',
            'javascript:',
            'onload=',
            'onerror=',
        ];
        
        foreach ($sqlPatterns as $pattern) {
            if (stripos($url, $pattern) !== false) {
                $this->flagSuspiciousActivity($ip, 'sql_injection_attempt', [
                    'url' => $url,
                    'pattern' => $pattern,
                ]);
                break;
            }
        }
    }

    /**
     * Monitor request patterns for anomalies
     */
    protected function monitorRequestPatterns(Request $request): void
    {
        $ip = $request->ip();
        $path = $request->path();
        
        // Monitor 404 attempts
        if (!$request->route()) {
            $key = "404_attempts:{$ip}";
            $count = Cache::get($key, 0);
            Cache::put($key, $count + 1, now()->addMinutes(10));
            
            if ($count > 20) { // More than 20 404s in 10 minutes
                $this->flagSuspiciousActivity($ip, 'excessive_404s', [
                    'count' => $count,
                    'path' => $path,
                ]);
            }
        }
        
        // Monitor admin panel access attempts
        if (str_starts_with($path, 'admin/') && !$request->user()?->hasRole('admin')) {
            $key = "admin_attempts:{$ip}";
            $count = Cache::get($key, 0);
            Cache::put($key, $count + 1, now()->addMinutes(30));
            
            if ($count > 5) { // More than 5 admin attempts in 30 minutes
                $this->flagSuspiciousActivity($ip, 'unauthorized_admin_access', [
                    'count' => $count,
                    'path' => $path,
                ]);
            }
        }
        
        // Monitor login attempts
        if ($path === 'login' && $request->isMethod('POST')) {
            $key = "login_attempts:{$ip}";
            $count = Cache::get($key, 0);
            Cache::put($key, $count + 1, now()->addMinutes(15));
            
            if ($count > 10) { // More than 10 login attempts in 15 minutes
                $this->flagSuspiciousActivity($ip, 'brute_force_login', [
                    'count' => $count,
                ]);
            }
        }
    }

    /**
     * Flag suspicious activity
     */
    protected function flagSuspiciousActivity(string $ip, string $type, array $data = []): void
    {
        Log::warning('Suspicious activity detected', [
            'ip' => $ip,
            'type' => $type,
            'data' => $data,
            'timestamp' => now(),
        ]);
        
        // Increment threat score
        $threatKey = "threat_score:{$ip}";
        $threatScore = Cache::get($threatKey, 0);
        $threatScore += $this->getThreatScoreIncrement($type);
        Cache::put($threatKey, $threatScore, now()->addHours(24));
        
        // Block IP if threat score is too high
        if ($threatScore >= 100) {
            $this->blockIp($ip, $type);
        }
        
        // Send alert if configured
        if (config('security.monitoring.enabled')) {
            $this->sendSecurityAlert($ip, $type, $data);
        }
    }

    /**
     * Get threat score increment for activity type
     */
    protected function getThreatScoreIncrement(string $type): int
    {
        return match ($type) {
            'sql_injection_attempt' => 50,
            'brute_force_login' => 30,
            'unauthorized_admin_access' => 25,
            'suspicious_user_agent' => 20,
            'rapid_requests' => 15,
            'excessive_404s' => 10,
            default => 5,
        };
    }

    /**
     * Block an IP address
     */
    protected function blockIp(string $ip, string $reason): void
    {
        $blockedIps = Cache::get('blocked_ips', []);
        $blockedIps[$ip] = [
            'reason' => $reason,
            'blocked_at' => now(),
            'expires_at' => now()->addHours(24),
        ];
        
        Cache::put('blocked_ips', $blockedIps, now()->addDays(7));
        
        Log::critical('IP address blocked', [
            'ip' => $ip,
            'reason' => $reason,
            'blocked_at' => now(),
        ]);
    }

    /**
     * Check if IP is blocked
     */
    protected function isIpBlocked(string $ip): bool
    {
        $blockedIps = Cache::get('blocked_ips', []);
        
        if (!isset($blockedIps[$ip])) {
            return false;
        }
        
        $blockInfo = $blockedIps[$ip];
        
        // Check if block has expired
        if (now()->gt($blockInfo['expires_at'])) {
            unset($blockedIps[$ip]);
            Cache::put('blocked_ips', $blockedIps, now()->addDays(7));
            return false;
        }
        
        return true;
    }

    /**
     * Log security events
     */
    protected function logSecurityEvents(Request $request, Response $response): void
    {
        // Log failed authentication attempts
        if ($response->getStatusCode() === 401) {
            Log::info('Authentication failed', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'user_agent' => $request->userAgent(),
            ]);
        }
        
        // Log authorization failures
        if ($response->getStatusCode() === 403) {
            Log::info('Authorization failed', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'user' => $request->user()?->id,
            ]);
        }
        
        // Log server errors
        if ($response->getStatusCode() >= 500) {
            Log::error('Server error occurred', [
                'ip' => $request->ip(),
                'url' => $request->fullUrl(),
                'status' => $response->getStatusCode(),
                'user' => $request->user()?->id,
            ]);
        }
    }

    /**
     * Send security alert
     */
    protected function sendSecurityAlert(string $ip, string $type, array $data): void
    {
        // This would integrate with your notification system
        // For now, just log the alert
        Log::alert('Security alert triggered', [
            'ip' => $ip,
            'type' => $type,
            'data' => $data,
            'timestamp' => now(),
        ]);
    }
}
