# The Real World - Lara<PERSON> Clone

<p align="center">
  <img src="https://img.shields.io/badge/Laravel-10.x-red.svg" alt="Laravel Version">
  <img src="https://img.shields.io/badge/PHP-8.1+-blue.svg" alt="PHP Version">
  <img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License">
  <img src="https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg" alt="Status">
</p>

A complete Laravel-based clone of The Real World platform (app.jointherealworld.com) featuring courses, events, chat, subscriptions, and more.

## 🚀 Features

### Core Features
- **User Authentication & Authorization** - Custom auth system with roles and permissions
- **Course Management** - Complete course system with lessons, progress tracking, and certificates
- **Event System** - Live events, webinars, workshops with RSVP functionality
- **Real-time Chat** - Multi-room chat system with WebSocket support
- **Subscription Management** - Stripe integration for premium memberships
- **Badge & Achievement System** - Gamification with XP, levels, and badges
- **Leaderboards** - User rankings and progress tracking
- **Admin Panel** - Comprehensive admin dashboard for content management

### Technical Features
- **Performance Optimized** - Redis caching, database optimization, image compression
- **Security Hardened** - Rate limiting, CSRF protection, input validation, security monitoring
- **Mobile Responsive** - Fully responsive design for all devices
- **SEO Optimized** - Meta tags, sitemaps, structured data
- **API Ready** - RESTful API with Sanctum authentication
- **Real-time Updates** - WebSocket integration with Pusher
- **File Management** - Image optimization, video streaming, file uploads
- **Email System** - Transactional emails, notifications, newsletters

## 📋 Requirements

- **PHP**: 8.1 or higher
- **Laravel**: 10.x
- **Database**: MySQL 8.0+ or MariaDB 10.3+
- **Cache**: Redis 6.0+
- **Node.js**: 18.x or higher
- **Composer**: 2.x

### PHP Extensions Required
- BCMath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML, GD/Imagick, Redis

## 🛠️ Installation

### 1. Clone Repository
```bash
git clone <repository-url> realworld-clone
cd realworld-clone
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install
```

### 3. Environment Setup
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure your database and other settings in .env
```

### 4. Database Setup
```bash
# Run migrations
php artisan migrate

# Seed database with sample data
php artisan db:seed
```

### 5. Storage Setup
```bash
# Create storage link
php artisan storage:link

# Set proper permissions
chmod -R 775 storage bootstrap/cache
```

### 6. Build Assets
```bash
# Development
npm run dev

# Production
npm run build
```

### 7. Start Development Server
```bash
# Start Laravel development server
php artisan serve

# Start Vite development server (in another terminal)
npm run dev

# Start queue worker (in another terminal)
php artisan queue:work
```

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in becoming a sponsor, please visit the Laravel [Patreon page](https://patreon.com/taylorotwell).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Cubet Techno Labs](https://cubettech.com)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[Many](https://www.many.co.uk)**
- **[Webdock, Fast VPS Hosting](https://www.webdock.io/en)**
- **[DevSquad](https://devsquad.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[OP.GG](https://op.gg)**
- **[WebReinvent](https://webreinvent.com/?utm_source=laravel&utm_medium=github&utm_campaign=patreon-sponsors)**
- **[Lendio](https://lendio.com)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
